BasedOnStyle: LLVM
# Language: C
# Indentation and Spacing
IndentWidth: 4
TabWidth: 4
UseTab: Never
IndentCaseLabels: false
ColumnLimit: 120
ContinuationIndentWidth: 4
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignEscapedNewlines: Left
AlignOperands: false

# Braces and Blocks
BraceWrapping:
  AfterClass: false
  AfterControlStatement: false
  AfterEnum: false
  AfterFunction: false
  AfterNamespace: false
  AfterStruct: false
  AfterUnion: false
  BeforeCatch: false
  BeforeElse: false
  IndentBraces: false
  SplitEmptyFunction: true
  SplitEmptyRecord: true
  SplitEmptyNamespace: true
AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: None
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false

# Line Breaks and Wrapping
BinPackArguments: false
BinPackParameters: false
PenaltyBreakBeforeFirstCallParameter: 1
PenaltyBreakComment: 10
PenaltyBreakString: 10
PenaltyExcessCharacter: 100
PenaltyReturnTypeOnItsOwnLine: 60

# Pointer Alignment
PointerAlignment: Right
DerivePointerAlignment: false

# Naming and Comments
UseCRLF: false
IncludeBlocks: Preserve
SortIncludes: true
CommentPragmas: "^ IWYU pragma:"
SpacesBeforeTrailingComments: 2

# Miscellaneous
NamespaceIndentation: None
AccessModifierOffset: -4
ConstructorInitializerIndentWidth: 4
EmptyLineBeforeAccessModifier: Always
KeepEmptyLinesAtTheStartOfBlocks: false
MaxEmptyLinesToKeep: 1
ReflowComments: false
SeparateDefinitionBlocks: Always
SpaceAfterCStyleCast: true
SpaceAfterTemplateKeyword: true
SpaceBeforeAssignmentOperators: true
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesInAngles: false
SpacesInContainerLiterals: true
SpacesInParentheses: false
SpacesInSquareBrackets: false

# Custom Adjustments
BreakBeforeBraces: Attach