#############################################################################
#                         _____       _ _                                   #
#                        /  __ \     | (_)                                  #
#                        | /  \/ __ _| |___  __                             #
#                        | |    / _` | | \ \/ /                             #
#                        | \__/\ (_| | | |>  <                              #
#                         \____/\__,_|_|_/_/\_\ inc.                        #
#                                                                           #
#############################################################################
#                                                                           #
#                       copyright 2025 by Calix, Inc.                       #
#                               Petaluma, CA                                #
#                                                                           #
#############################################################################
#
# Author: <PERSON> Li
#
# Purpose: Autoconf config for the libdpif daemon
#
#############################################################################

# Define app version
m4_define( [VER_MAJOR],  m4_esyscmd([grep VER_MAJOR  version.h | sed -e 's/.* //' | tr -d '\n']))
m4_define( [VER_MINOR],  m4_esyscmd([grep VER_MINOR  version.h | sed -e 's/.* //' | tr -d '\n']))
m4_define( [VER_PATCH],  m4_esyscmd([grep VER_PATCH  version.h | sed -e 's/.* //' | tr -d '\n']))
m4_define( [VER_PKG],    m4_esyscmd([grep VER_PKG    version.h | sed -e 's/.* //' | tr -d '\n\"']))
m4_define( [PKG_NAME],   m4_esyscmd([grep PACKAGE_NAME version.h | sed -e 's/.* //' | tr -d '\n\"']))

# Define API version
m4_define([LIB_CURRENT], m4_eval(VER_MAJOR + VER_MINOR))
m4_define([LIB_REVISION], VER_PATCH)
m4_define([LIB_AGE], VER_MINOR)

AC_PREREQ([2.63])
AC_INIT([PKG_NAME], [VER_PKG], [<EMAIL>])
AM_INIT_AUTOMAKE([foreign])
AC_CONFIG_SRCDIR([src/libdpif_main.c])
AC_CONFIG_HEADERS([config.h])
AC_CONFIG_MACRO_DIR([m4])

# Checks for programs.
AC_PROG_CXX
AC_PROG_CC
AC_PROG_CPP
AC_PROG_INSTALL
AC_PROG_LN_S
AC_PROG_LIBTOOL
AC_PROG_MAKE_SET
AC_PROG_RANLIB

# Export library version for linker
VERSION_INFO="LIB_CURRENT:LIB_REVISION:LIB_AGE"
AC_SUBST([VERSION_INFO])

# Checks for libraries.
AC_CHECK_LIB([daemonlib],   [dl_init_daemon_context], , AC_MSG_ERROR([daemonlib is not usable]))
AC_CHECK_LIB([cmd_parser],  [cp_new],                 , AC_MSG_ERROR([cmd_parser is not usable]))

# Checks for header files.
AC_CHECK_HEADERS([inttypes.h limits.h stdlib.h string.h])
AC_CHECK_HEADERS([daemonlib.h],            , AC_MSG_ERROR([missing Daemonlib]))
AC_CHECK_HEADERS([cmd_parser.h],           , AC_MSG_ERROR([missing command parser API]))

# Checks for typedefs, structures, and compiler characteristics.
AC_HEADER_STDBOOL
AC_TYPE_INT32_T
AC_TYPE_UINT16_T
AC_TYPE_UINT32_T
AC_TYPE_UINT64_T
AC_TYPE_UINT8_T

# Remove optimization conditional
AM_CONDITIONAL(RM_OPT, [test x"$RM_OPTIMIZATION" = xtre])

# Checks for library functions.
AC_CHECK_FUNCS([strncasecmp])

AC_CONFIG_FILES([Makefile
                 lib/Makefile
                 src/Makefile])
AC_OUTPUT
