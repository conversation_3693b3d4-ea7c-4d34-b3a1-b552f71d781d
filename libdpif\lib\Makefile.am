#############################################################################
#                         _____       _ _                                   # 
#                        /  __ \     | (_)                                  # 
#                        | /  \/ __ _| |___  __                             #
#                        | |    / _` | | \ \/ /                             #
#                        | \__/\ (_| | | |>  <                              #
#                         \____/\__,_|_|_/_/\_\ inc.                        #
#                                                                           #
#############################################################################
#                                                                           #
#                       copyright 2025 by Calix, Inc.                       #
#                             Santa Barbara, CA                             #
#                                                                           #
#############################################################################
#
# Author: <PERSON> Li
#
# Purpose: Makefile.am for the DPIF Library
#
#############################################################################

include_HEADERS = dpif.h

lib_LTLIBRARIES = libdpif.la

libdpif_la_LDFLAGS = -version-info @VERSION_INFO@ -lrt -lexa_lib -lnl-3 \
     -lnl-genl-3 -lnl-cli-3 -lcalixdb -lrdtsc -ldaemonlib \
     -lrte_eal \
     -lrte_mempool \
     -lrte_mbuf \
     -lrte_ring \
     -lrte_ethdev \
     -lrte_hash \
     -lrte_timer \
     -lrte_net \
     -lrte_net_memif \
     -lpcap

libdpif_la_CFLAGS = $(warning_mask) -D_GNU_SOURCE -Werror -DRTE_FORCE_INTRINSICS \
     $(OPTS) -pthread -I${STAGING_DIR_HOST}/usr/include/libnl3 -DNDEBUG

libdpif_la_SOURCES = dpif_init.c dpif_cli_server.c dpif_rx.c dpif_session.c dpif_sim.c \
       dpif_tx.c dpif_stats.c dpif_worker.c dpif_utils.c dpif_benchmark.c

if RM_OPT
override CFLAGS := $(shell echo $(CFLAGS) | sed "s@-O.@@g" )
OPTS = -DCODECOV
endif