// dpif_cli_server.h
#ifndef DPIF_CLI_SERVER_H
#define DPIF_CLI_SERVER_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Starts the DPIF CLI server thread.
 *
 * The server will listen on a Unix domain socket for client connections.
 *
 * @return 0 on success, negative errno on failure to create the thread.
 */
int dpif_cli_server_start(void);

/**
 * @brief Stops the DPIF CLI server thread.
 *
 * Signals the server thread to terminate and waits for it to join.
 */
void dpif_cli_server_stop(void);

#ifdef __cplusplus
}
#endif

#endif  // DPIF_CLI_SERVER_H