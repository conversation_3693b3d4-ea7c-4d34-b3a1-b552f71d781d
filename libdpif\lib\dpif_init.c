#include <rte_common.h>
#include <rte_eal.h>
#include <rte_errno.h>
#include <rte_ethdev.h>
#include <rte_malloc.h>
#include <rte_timer.h>
#include <stdio.h>  // Include for standard snprintf

#include "dpif_cli_server.h"  // For internal CLI server management
#include "dpif_init.h"
#include "dpif_private.h"  // Contains dpif_global_context_t, etc.
#include "dpif_sim.h"

// Global context instance (defined here)
dpif_global_context_t *g_dpif_ctx = NULL;

// Global snapshot accessible within DPIF
dpif_stats_snapshot_t *g_dpif_stats_snapshot = NULL;

// Registered callbacks
static dpi_device_t s_dpi_callbacks;
static int s_callbacks_registered = 0;

// --- Static EAL Configuration) ---
#define DPIF_INTERNAL_MEMIF_VDEV_NAME "net_memif0"
#define DPIF_INTERNAL_MEMIF_SOCKET_PATH "/run/vpp/memif.sock"
#define DPIF_INTERNAL_MEMIF_ID 0
#define DPIF_INTERNAL_MEMIF_ROLE "role=client"
#define DPIF_INTERNAL_MEMIF_IS_ABSTRACT_SOCKET "socket-abstract=no"
// --- End Static EAL Configuration ---

static dpif_core_config_t dpu_main_core_configs[] = {
    {.lcore_id = 14, .is_rx_thread = 1, .memif_queue_id = 0},
    {.lcore_id = 15, .is_rx_thread = 1, .memif_queue_id = 1},
    {.lcore_id = 16, .is_rx_thread = 1, .memif_queue_id = 2},
    {.lcore_id = 17, .is_rx_thread = 1, .memif_queue_id = 3},
    {.lcore_id = 18, .is_rx_thread = 1, .memif_queue_id = 4},
    {.lcore_id = 19, .is_rx_thread = 1, .memif_queue_id = 5},
    {.lcore_id = 20, .is_rx_thread = 1, .memif_queue_id = 6},
    {.lcore_id = 21, .is_rx_thread = 1, .memif_queue_id = 7},
    {.lcore_id = 22, .is_worker_thread = 1, .worker_group_id = 0},
    {.lcore_id = 23, .is_worker_thread = 1, .worker_group_id = 0},
};

static dpif_core_config_t dpu_n_core_configs[] = {
    {.lcore_id = 6, .is_rx_thread = 1, .memif_queue_id = 0},
    {.lcore_id = 7, .is_rx_thread = 1, .memif_queue_id = 1},
    {.lcore_id = 8, .is_rx_thread = 1, .memif_queue_id = 2},
    {.lcore_id = 9, .is_rx_thread = 1, .memif_queue_id = 3},
    {.lcore_id = 10, .is_rx_thread = 1, .memif_queue_id = 4},
    {.lcore_id = 11, .is_rx_thread = 1, .memif_queue_id = 5},
    {.lcore_id = 12, .is_rx_thread = 1, .memif_queue_id = 6},
    {.lcore_id = 13, .is_rx_thread = 1, .memif_queue_id = 7},
    {.lcore_id = 14, .is_rx_thread = 1, .memif_queue_id = 8},
    {.lcore_id = 15, .is_rx_thread = 1, .memif_queue_id = 9},
    {.lcore_id = 16, .is_rx_thread = 1, .memif_queue_id = 10},
    {.lcore_id = 17, .is_rx_thread = 1, .memif_queue_id = 11},
    {.lcore_id = 18, .is_rx_thread = 1, .memif_queue_id = 12},
    {.lcore_id = 19, .is_rx_thread = 1, .memif_queue_id = 13},
    {.lcore_id = 20, .is_rx_thread = 1, .memif_queue_id = 14},
    {.lcore_id = 21, .is_rx_thread = 1, .memif_queue_id = 15},
    {.lcore_id = 22, .is_worker_thread = 1, .worker_group_id = 0},
    {.lcore_id = 23, .is_worker_thread = 1, .worker_group_id = 0},
};

static dpif_core_config_t sim_main_core_configs[] = {
    {.lcore_id = 3, .is_rx_thread = 1, .memif_queue_id = 0},
    {.lcore_id = 4, .is_rx_thread = 1, .memif_queue_id = 1},
    {.lcore_id = 5, .is_worker_thread = 1, .worker_group_id = 0},
};

static dpif_core_config_t sim_n_core_configs[] = {
    {.lcore_id = 3, .is_rx_thread = 1, .memif_queue_id = 0},
    {.lcore_id = 4, .is_rx_thread = 1, .memif_queue_id = 1},
    {.lcore_id = 5, .is_worker_thread = 1, .worker_group_id = 0},
};

static dpif_core_config_t *default_core_configs = dpu_main_core_configs;
static uint32_t num_core_configs = sizeof(dpu_main_core_configs) / sizeof(dpu_main_core_configs[0]);

static char *dpu_main_args[] = {"dpif",
                                "--no-pci",
                                "-l",
                                "13-23",
                                "-n",
                                "11",
                                "--vdev",
                                "net_memif0,role=client,id=0,socket-abstract=no,socket=/run/vpp/memif.sock"};

static char *dpu_n_args[] = {"dpif",
                             "--no-pci",
                             "-l",
                             "5-23",
                             "-n",
                             "19",
                             "--vdev",
                             "net_memif0,role=client,id=0,socket-abstract=no,socket=/run/vpp/memif.sock"};

static char *sim_main_args[] = {"dpif",
                                "--no-pci",
                                "-l",
                                "2-5",
                                "-n",
                                "4",
                                "--vdev",
                                "net_memif0,role=client,id=0,socket-abstract=no,socket=/run/vpp/memif.sock"};

static char *sim_n_args[] = {"dpif",
                             "--no-pci",
                             "-l",
                             "2-5",
                             "-n",
                             "4",
                             "--vdev",
                             "net_memif0,role=client,id=0,socket-abstract=no,socket=/run/vpp/memif.sock"};

static char **default_args = dpu_main_args;
static int default_argc = sizeof(dpu_main_args) / sizeof(dpu_main_args[0]);

dpif_internal_spec_t g_dpif_spec_settings = {
    /* memif Configuration */
    .memif_conf =
        {
            .dev_name = "net_memif0",
            .socket_path = "/run/vpp/memif.sock",
            .role = 0,
            .id = 0,
            .num_queues = 2 /* Assuming 2 RX threads */
        },

    /* Core/Thread Settings */
    .core_configs = NULL,
    .num_core_configs = 0,

    /* mbuf Pool Settings - OPTIMIZED */
    .mbuf_pool_name = "DPIF_MBUF_POOL_MON",
    .mbuf_count = 32768,      // Increased from 16382 to 32768 for better burst handling
    .mbuf_cache_size = 1024,  // Increased from 256 to 1024 for better cache efficiency

    /* Session Pool Settings - OPTIMIZED */
    .session_pool_name = "DPIF_SESS_POOL_MON",
    .session_count = 1200000,
    .session_cache_size = 1024,       // Increased from 512 to 1024 for better performance
    .session_hash_entries = 1048576,  // Increased from 600000 to 1M for better hash distribution
    .session_timeout_seconds = 120,

    /* Work Pool Settings - OPTIMIZED */
    .work_pool_name = "DPIF_WORK_POOL_MON",
    .work_count = 16384,     // Increased from 8191 to 16384 for better work distribution
    .work_cache_size = 512,  // Increased from 256 to 512 for better cache efficiency

    /* Ring Settings - OPTIMIZED */
    .task_ring_name_prefix = "dpif_task_ring_mon",
    .completion_ring_name_prefix = "dpif_comp_ring_mon",
    .ring_size = 32768,  // Increased from 8192 to 32768 for better burst handling
};

/**
 * @brief Starts the internal CLI server.
 *
 * @return 0 on success, -1 on failure.
 */
static int dpif_cli_server_start_internal(void) {
    DPIF_LOG_INFO("Attempting to start internal CLI server using fixed path.");
    if (dpif_cli_server_start() != 0) {
        DPIF_LOG_ERROR("Failed to start internal DPIF CLI server.");
        return -1;
    }
    DPIF_LOG_INFO("Internal DPIF CLI server started.");
    return 0;
}

/**
 * @brief Stops the internal CLI server.
 */
static void dpif_cli_server_stop_internal(void) {
    DPIF_LOG_INFO("Stopping internal DPIF CLI server...");
    dpif_cli_server_stop();
    DPIF_LOG_INFO("Internal DPIF CLI server stopped.");
}

#if 0
static int build_internal_eal_args(const dpif_app_config_t *app_config,
                                   int *eal_argc_out, char ***eal_argv_out) {
    const int MAX_EAL_ARGS = 32;
    char **eal_args = calloc(MAX_EAL_ARGS, sizeof(char *));
    if (!eal_args) {
        DPIF_LOG_ERROR( "Failed to allocate memory for EAL args array");
        return -ENOMEM;
    }

    int current_arg = 0;

    uint32_t total_threads_needed =
        app_config->num_rx_threads + app_config->num_worker_threads;
    if (total_threads_needed == 0) {
        DPIF_LOG_ERROR(
                 "No RX or Worker threads requested. At least one RX thread is "
                 "required.");
        goto einval_error;
    }

    uint32_t num_total_lcores_for_eal = 1 + total_threads_needed;
    if (num_total_lcores_for_eal > RTE_MAX_LCORE) {
        DPIF_LOG(
            WARNING,
            "Requested more lcores (%u) than RTE_MAX_LCORE (%d). Capping to "
            "RTE_MAX_LCORE.",
            num_total_lcores_for_eal, RTE_MAX_LCORE);
        num_total_lcores_for_eal = RTE_MAX_LCORE;
    }
    if (num_total_lcores_for_eal == 0) {
        DPIF_LOG_ERROR( "Calculated zero total lcores for EAL.");
        goto einval_error;
    }

    char lcore_arg_str[128];
    snprintf(lcore_arg_str, sizeof(lcore_arg_str), "0-%u",
             num_total_lcores_for_eal - 1);
    eal_args[current_arg++] = strdup("-l");
    if (!eal_args[current_arg - 1]) {
        DPIF_LOG_ERROR( "Failed to allocate memory for -l argument");
        goto enomem_error;
    }
    eal_args[current_arg++] = strdup(lcore_arg_str);
    if (!eal_args[current_arg - 1]) {
        DPIF_LOG_ERROR( "Failed to allocate memory for lcore string");
        goto enomem_error;
    }

    char vdev_full_arg_str[256];
    snprintf(vdev_full_arg_str, sizeof(vdev_full_arg_str),
             "%s,socket=%s,id=%d,%s", DPIF_INTERNAL_MEMIF_VDEV_NAME,
             DPIF_INTERNAL_MEMIF_SOCKET_PATH, DPIF_INTERNAL_MEMIF_ID,
             DPIF_INTERNAL_MEMIF_ROLE);
    eal_args[current_arg++] = strdup("--vdev");
    if (!eal_args[current_arg - 1]) {
        DPIF_LOG_ERROR( "Failed to allocate memory for --vdev argument");
        goto enomem_error;
    }
    eal_args[current_arg++] = strdup(vdev_full_arg_str);
    if (!eal_args[current_arg - 1]) {
        DPIF_LOG_ERROR( "Failed to allocate memory for vdev string");
        goto enomem_error;
    }

    if (current_arg >= MAX_EAL_ARGS) {
        DPIF_LOG_ERROR( "Too many internal EAL arguments constructed (max: %d).",
                 MAX_EAL_ARGS);
        goto enomem_error;
    }

    *eal_argc_out = current_arg;
    *eal_argv_out = eal_args;
    DPIF_LOG_DEBUG("Built %d EAL arguments successfully", current_arg);
    return 0;

einval_error:
    for (int i = 0; i < current_arg; ++i) {
        if (eal_args[i]) {
            free(eal_args[i]);
        }
    }
    free(eal_args);
    return -EINVAL;

enomem_error:
    DPIF_LOG_ERROR( "Memory allocation failed during EAL argument construction.");
    for (int i = 0; i < current_arg; ++i) {
        if (eal_args[i]) {
            free(eal_args[i]);
        }
    }
    free(eal_args);
    return -ENOMEM;
}
#endif

/**
 * @brief Registers the DPI application's callback functions with the DPIF library.
 *
 * @param dev Pointer to a dpi_device_t structure containing the callback function pointers.
 * @return 0 on success, -EINVAL if dev is NULL or mandatory callbacks are missing.
 */
int dpi_register_device(const dpi_device_t *dev) {
    if (!dev)
        return -EINVAL;
    if (s_callbacks_registered)
        DPIF_LOG_WARNING("Callbacks already registered.");
    s_dpi_callbacks = *dev;
    if (!s_dpi_callbacks.dpi_session_create || !s_dpi_callbacks.dpi_session_destroy ||
        !s_dpi_callbacks.dpi_session_analyze || !s_dpi_callbacks.dpi_session_work ||
        !s_dpi_callbacks.dpi_session_update) {
        DPIF_LOG_ERROR("Mandatory callbacks missing.");
        s_callbacks_registered = 0;
        return -EINVAL;
    }
    s_callbacks_registered = 1;
    DPIF_LOG_INFO("Callbacks registered.");
    return 0;
}

/**
 * @brief Unregisters the DPI application's callback functions.
 */
void dpi_unregister_device(void) {
    if (s_callbacks_registered) {
        memset(&s_dpi_callbacks, 0, sizeof(s_dpi_callbacks));
        s_callbacks_registered = 0;
        DPIF_LOG_INFO("Callbacks unregistered.");
    }
}

/**
 * @brief Validates the application configuration.
 *
 * @param app_config Pointer to the application configuration.
 * @return 0 on success, negative errno on failure.
 */
static int validate_app_config(const dpif_app_config_t *app_config) {
    if (!app_config) {
        DPIF_LOG_ERROR("Invalid app_config (NULL).");
        return -EINVAL;
    }
    if (!s_callbacks_registered) {
        DPIF_LOG_ERROR("DPI callbacks not registered. Call dpi_register_device() first.");
        return -EINVAL;
    }
    if (g_dpif_ctx) {
        DPIF_LOG_ERROR("DPIF already initialized.");
        return -EALREADY;
    }
    if (app_config->num_rx_threads == 0) {
        DPIF_LOG_ERROR("At least one RX thread must be configured (num_rx_threads > 0).");
        return -EINVAL;
    }
    return 0;
}

/**
 * @brief Initializes the DPDK Environment Abstraction Layer (EAL).
 *
 * @param app_config Pointer to the application configuration (currently unused for EAL args).
 * @return 0 on success, negative value from rte_eal_init on failure.
 */
static int initialize_eal(const dpif_app_config_t *app_config) {
    int ret;

    DPIF_LOG_INFO("Initializing EAL with internally constructed arguments:");
    ret = rte_eal_init(default_argc, default_args);
    if (ret < 0) {
        DPIF_LOG_ERROR("rte_eal_init failed with internal arguments: %d. Check EAL logs "
                       "for details.",
                       ret);
        return ret;
    }
    return 0;
}

/**
 * @brief Sets up the global DPIF context.
 *
 * @param app_config Pointer to the application configuration.
 * @return 0 on success, -ENOMEM on allocation failure.
 */
static int setup_global_context(const dpif_app_config_t *app_config) {
    g_dpif_ctx = rte_zmalloc("DPIF_GLOBAL_CTX", sizeof(dpif_global_context_t), RTE_CACHE_LINE_SIZE);
    if (!g_dpif_ctx) {
        DPIF_LOG_ERROR("Global context allocation failed.");
        return -ENOMEM;
    }

    uint64_t hz = rte_get_timer_hz();
    g_dpif_ctx->periodic_session_update_ticks = 0;
    if (app_config->session_update_interval_seconds > 0 && hz > 0) {
        g_dpif_ctx->periodic_session_update_ticks = hz * app_config->session_update_interval_seconds;
        DPIF_LOG_INFO("Periodic session update interval set to %" PRIu64 " seconds (%" PRIu64 " ticks).",
                      app_config->session_update_interval_seconds,
                      g_dpif_ctx->periodic_session_update_ticks);
    }

    g_dpif_ctx->app_cfg = *app_config;
    g_dpif_ctx->registered_callbacks = s_dpi_callbacks;
    g_dpif_ctx->callbacks_registered = 1;
    g_dpif_ctx->memif_port_id = RTE_MAX_ETHPORTS;
    g_dpif_ctx->eal_initialized_by_dpif = 1;
    g_dpif_ctx->num_rx_cores = app_config->num_rx_threads;
    g_dpif_ctx->num_worker_cores = app_config->num_worker_threads;
    g_dpif_ctx->benchmark_mode = DPIF_BENCHMARK_MODE_DEFAULT;
    DPIF_LOG_INFO(
        "Targeting %u RX threads and %u Worker threads.", g_dpif_ctx->num_rx_cores, g_dpif_ctx->num_worker_cores);
    return 0;
}

/**
 * @brief Configures the DPDK timer subsystem and session timeout ticks.
 *
 * @param app_config Pointer to the application configuration.
 * @return 0 always.
 */
static int configure_timer_subsystem(const dpif_app_config_t *app_config) {
    rte_timer_subsystem_init();
    uint64_t hz = rte_get_timer_hz();
    if (hz == 0) {
        DPIF_LOG_WARNING("Failed to get timer hz (rte_get_timer_hz returned 0). Session "
                         "timeouts might be unreliable or disabled.");
        g_dpif_ctx->session_timeout_ticks = 0;
    } else {
        if (app_config->session_timeout_seconds > 0) {
            g_dpif_ctx->session_timeout_ticks = hz * app_config->session_timeout_seconds;
            DPIF_LOG_INFO("Session timeout set to %u seconds (%" PRIu64 " ticks at %" PRIu64 " Hz).",
                          app_config->session_timeout_seconds,
                          g_dpif_ctx->session_timeout_ticks,
                          hz);
        } else {
            g_dpif_ctx->session_timeout_ticks = 0;
            DPIF_LOG_INFO("Session timeout is disabled (0 seconds).");
        }
    }
    return 0;
}

/**
 * @brief Assigns logical cores to RX and Worker threads based on EAL configuration.
 *
 * @return 0 on success, -ENOSPC if not enough lcores are available.
 */
static int assign_lcores(void) {
    uint32_t i, lcore_id_iter;
    uint32_t lcore_alloc_count = 0;
    unsigned lcore_map_idx = 0;

    for (i = 0; i < RTE_MAX_LCORE; ++i) {
        g_dpif_ctx->rx_lcore_map[i] = RTE_MAX_LCORE;
        g_dpif_ctx->worker_lcore_map[i] = RTE_MAX_LCORE;
        g_dpif_ctx->lcore_to_rx_idx_map[i] = UINT32_MAX;
        g_dpif_ctx->lcore_to_worker_idx_map[i] = UINT32_MAX;
    }

    lcore_map_idx = 0;
    RTE_LCORE_FOREACH_WORKER(lcore_id_iter) {
        if (lcore_map_idx < g_dpif_ctx->num_rx_cores) {
            g_dpif_ctx->rx_lcore_map[lcore_map_idx] = lcore_id_iter;
            g_dpif_ctx->lcore_to_rx_idx_map[lcore_id_iter] = lcore_map_idx;
            DPIF_LOG_INFO("Assigned Lcore %u as RX Core Index %u.", lcore_id_iter, lcore_map_idx);
            lcore_map_idx++;
            lcore_alloc_count++;
        } else {
            break;
        }
    }
    if (lcore_map_idx < g_dpif_ctx->num_rx_cores) {
        DPIF_LOG_ERROR("Not enough enabled worker lcores for %u RX threads (only %u "
                       "assigned). Check EAL lcore configuration.",
                       g_dpif_ctx->num_rx_cores,
                       lcore_map_idx);
        return -ENOSPC;
    }

    lcore_map_idx = 0;
    RTE_LCORE_FOREACH_WORKER(lcore_id_iter) {
        if (g_dpif_ctx->lcore_to_rx_idx_map[lcore_id_iter] != UINT32_MAX) {
            continue;
        }
        if (lcore_map_idx < g_dpif_ctx->num_worker_cores) {
            g_dpif_ctx->worker_lcore_map[lcore_map_idx] = lcore_id_iter;
            g_dpif_ctx->lcore_to_worker_idx_map[lcore_id_iter] = lcore_map_idx;
            DPIF_LOG_INFO("Assigned Lcore %u as Worker Core Index %u.", lcore_id_iter, lcore_map_idx);
            lcore_map_idx++;
            lcore_alloc_count++;
        } else {
            break;
        }
    }
    if (lcore_map_idx < g_dpif_ctx->num_worker_cores) {
        DPIF_LOG_ERROR("Not enough enabled worker lcores for %u Worker threads (only %u "
                       "assigned after RX). Check EAL lcore configuration.",
                       g_dpif_ctx->num_worker_cores,
                       lcore_map_idx);
        return -ENOSPC;
    }
    DPIF_LOG_INFO("Total data plane lcores assigned by DPIF: %u (excluding main lcore).", lcore_alloc_count);
    return 0;
}

/**
 * @brief Creates necessary memory pools (mbuf, session, work).
 *
 * @param app_config Pointer to the application configuration (used for naming prefix).
 * @return 0 on success, -1 on failure.
 */
static int create_memory_pools(const dpif_app_config_t *app_config) {
    char pool_name[RTE_MEMPOOL_NAMESIZE];

    snprintf(pool_name, sizeof(pool_name), "%s",
             g_dpif_spec_settings.mbuf_pool_name);  // Use standard snprintf
    uint16_t priv_data_size = sizeof(dp_metadata_t);
    // OPTIMIZATION: Use NUMA-aware allocation for better performance
    int socket_id = rte_socket_id();
    if (socket_id == SOCKET_ID_ANY) {
        socket_id = 0;  // Default to socket 0 if detection fails
    }

    g_dpif_ctx->mbuf_pool = rte_pktmbuf_pool_create(pool_name,
                                                    g_dpif_spec_settings.mbuf_count,
                                                    g_dpif_spec_settings.mbuf_cache_size,
                                                    priv_data_size,
                                                    RTE_MBUF_DEFAULT_BUF_SIZE,
                                                    socket_id);
    if (!g_dpif_ctx->mbuf_pool) {
        DPIF_LOG_ERROR("Mbuf pool '%s' create failed: %s", pool_name, rte_strerror(rte_errno));
        return -1;
    }
    DPIF_LOG_INFO("Mbuf pool '%s' created.", pool_name);

    snprintf(pool_name, sizeof(pool_name), "%s", g_dpif_spec_settings.session_pool_name);
    // OPTIMIZATION: NUMA-aware session pool with cache line alignment
    g_dpif_ctx->session_pool = rte_mempool_create(pool_name,
                                                  g_dpif_spec_settings.session_count,
                                                  sizeof(dpif_session_t),
                                                  g_dpif_spec_settings.session_cache_size,
                                                  0,
                                                  NULL,
                                                  NULL,
                                                  NULL,
                                                  NULL,
                                                  socket_id,                  // Use same socket as mbuf pool
                                                  MEMPOOL_F_NO_CACHE_ALIGN);  // Sessions are already cache-aligned
    if (!g_dpif_ctx->session_pool) {
        DPIF_LOG_ERROR("Session pool '%s' create failed: %s", pool_name, rte_strerror(rte_errno));
        goto cleanup_mbuf_pool;
    }
    DPIF_LOG_INFO("Session pool '%s' created.", pool_name);

    if (g_dpif_ctx->num_worker_cores > 0) {
        snprintf(pool_name, sizeof(pool_name), "%s", g_dpif_spec_settings.work_pool_name);
        uint32_t work_elt_size = sizeof(struct dpi_work) + MAX_WORK_DATA_SIZE;
        // OPTIMIZATION: NUMA-aware work pool with proper alignment
        g_dpif_ctx->work_pool = rte_mempool_create(pool_name,
                                                   g_dpif_spec_settings.work_count,
                                                   work_elt_size,
                                                   g_dpif_spec_settings.work_cache_size,
                                                   0,
                                                   NULL,
                                                   NULL,
                                                   NULL,
                                                   NULL,
                                                   socket_id,  // Use same socket for consistency
                                                   0);
        if (!g_dpif_ctx->work_pool) {
            DPIF_LOG_ERROR("Work pool '%s' create failed: %s", pool_name, rte_strerror(rte_errno));
            goto cleanup_session_pool;
        }
        DPIF_LOG_INFO("Work pool '%s' created.", pool_name);
    } else {
        g_dpif_ctx->work_pool = NULL;
    }

    return 0;

/* cleanup_work_pool: // This label was unused */
cleanup_session_pool:
    if (g_dpif_ctx->session_pool)
        rte_mempool_free(g_dpif_ctx->session_pool);
cleanup_mbuf_pool:
    if (g_dpif_ctx->mbuf_pool)
        rte_mempool_free(g_dpif_ctx->mbuf_pool);
    return -1;
}

/**
 * @brief Sets up task rings (for RX to Worker communication) and completion rings (for Worker to RX).
 *
 * @param app_config Pointer to the application configuration (used for naming prefix).
 * @return 0 on success, negative errno on failure.
 */
static int setup_task_and_completion_rings(const dpif_app_config_t *app_config) {
    char ring_name[RTE_RING_NAMESIZE];
    int lcore_id_iter;

    if (g_dpif_ctx->num_worker_cores > 0) {
        g_dpif_ctx->all_task_rings =
            rte_calloc("ALL_TASK_RINGS", g_dpif_ctx->num_worker_cores, sizeof(struct rte_ring *), 0);
        g_dpif_ctx->all_completion_rings =
            rte_calloc("ALL_COMP_RINGS", g_dpif_ctx->num_rx_cores, sizeof(struct rte_ring *), 0);
        if (!g_dpif_ctx->all_task_rings || !g_dpif_ctx->all_completion_rings) {
            DPIF_LOG_ERROR("Task/Completion ring array alloc failed.");
            return -ENOMEM;
        }
        g_dpif_ctx->num_total_task_rings = 0;        // Will be incremented
        g_dpif_ctx->num_total_completion_rings = 0;  // Will be incremented

        uint32_t worker_idx_ring = 0;
        for (uint32_t i = 0; i < g_dpif_spec_settings.num_core_configs; ++i) {
            if (g_dpif_spec_settings.core_configs[i].is_worker_thread &&
                rte_lcore_is_enabled(g_dpif_spec_settings.core_configs[i].lcore_id)) {
                lcore_id_iter = g_dpif_spec_settings.core_configs[i].lcore_id;
                snprintf(
                    ring_name, sizeof(ring_name), "%s_w%u", g_dpif_spec_settings.task_ring_name_prefix, lcore_id_iter);
                // OPTIMIZATION: Create ring on worker's NUMA node for better performance
                int worker_socket = rte_lcore_to_socket_id(lcore_id_iter);
                if (worker_socket == SOCKET_ID_ANY) {
                    worker_socket = 0;
                }
                g_dpif_ctx->all_task_rings[worker_idx_ring] = rte_ring_create(ring_name,
                                                                              g_dpif_spec_settings.ring_size,
                                                                              worker_socket,
                                                                              RING_F_SC_DEQ);  // SC_DEQ for worker
                if (!g_dpif_ctx->all_task_rings[worker_idx_ring]) {
                    DPIF_LOG_ERROR("Task ring '%s' create failed: %s", ring_name, rte_strerror(rte_errno));
                    return -rte_errno;
                }
                DPIF_LOG_INFO("Task ring '%s' created for worker on lcore %u.", ring_name, lcore_id_iter);
                g_dpif_ctx->num_total_task_rings++;
                worker_idx_ring++;
            }
        }
        uint32_t rx_idx_ring = 0;
        for (uint32_t i = 0; i < g_dpif_spec_settings.num_core_configs; ++i) {
            if (g_dpif_spec_settings.core_configs[i].is_rx_thread &&
                rte_lcore_is_enabled(g_dpif_spec_settings.core_configs[i].lcore_id)) {
                lcore_id_iter = g_dpif_spec_settings.core_configs[i].lcore_id;
                snprintf(ring_name,
                         sizeof(ring_name),
                         "%s_rx%u",
                         g_dpif_spec_settings.completion_ring_name_prefix,
                         lcore_id_iter);
                g_dpif_ctx->all_completion_rings[rx_idx_ring] = rte_ring_create(
                    ring_name, g_dpif_spec_settings.ring_size, rte_socket_id(), RING_F_SC_DEQ);  // SC_DEQ for RX
                if (!g_dpif_ctx->all_completion_rings[rx_idx_ring]) {
                    DPIF_LOG_ERROR("Completion ring '%s' create failed: %s", ring_name, rte_strerror(rte_errno));
                    return -rte_errno;
                }
                DPIF_LOG_INFO("Completion ring '%s' created for RX on lcore %u.", ring_name, lcore_id_iter);
                g_dpif_ctx->num_total_completion_rings++;
                rx_idx_ring++;
            }
        }
    } else {
        g_dpif_ctx->num_total_task_rings = 0;
        g_dpif_ctx->all_task_rings = NULL;
        g_dpif_ctx->num_total_completion_rings = 0;
        g_dpif_ctx->all_completion_rings = NULL;
    }
    return 0;
}

/**
 * @brief Configures the memif port used by DPIF.
 *
 * @return 0 on success, negative errno on failure.
 */
static int configure_memif_port(void) {
    const char *internal_memif_dev_to_use = DPIF_INTERNAL_MEMIF_VDEV_NAME;
    int ret = rte_eth_dev_get_port_by_name(internal_memif_dev_to_use, &g_dpif_ctx->memif_port_id);
    if (ret != 0) {
        DPIF_LOG_ERROR("Failed to get memif port ID for internally defined device '%s': "
                       "%s. Ensure DPIF's internal EAL config correctly creates this vdev "
                       "with this exact name.",
                       internal_memif_dev_to_use,
                       rte_strerror(-ret));
        return (ret == -ENOENT || ret == -ENODEV) ? -ENODEV : ret;
    }
    if (!rte_eth_dev_is_valid_port(g_dpif_ctx->memif_port_id)) {
        DPIF_LOG_ERROR("Memif port ID %u for device '%s' is invalid after "
                       "get_port_by_name.",
                       g_dpif_ctx->memif_port_id,
                       internal_memif_dev_to_use);
        return -ENODEV;
    }
    DPIF_LOG_INFO("Found internally configured memif device '%s' with port_id %u.",
                  internal_memif_dev_to_use,
                  g_dpif_ctx->memif_port_id);

    struct rte_eth_conf port_conf = {0};
    ret = rte_eth_dev_configure(
        g_dpif_ctx->memif_port_id, g_dpif_ctx->num_rx_cores, g_dpif_ctx->num_rx_cores, &port_conf);
    if (ret < 0) {
        DPIF_LOG_ERROR("Memif port %u configure failed: %s", g_dpif_ctx->memif_port_id, rte_strerror(-ret));
        return ret;
    }

    uint16_t nb_rxd = 4096;  // Increased from 2048 to 4096 for better burst handling
    uint16_t nb_txd = 4096;  // Increased from 2048 to 4096 for better burst handling
    for (uint16_t q_id = 0; q_id < g_dpif_ctx->num_rx_cores; ++q_id) {
        int socket_id = rte_eth_dev_socket_id(g_dpif_ctx->memif_port_id);
        if (socket_id < 0 || socket_id == SOCKET_ID_ANY)
            socket_id = 0;

        ret = rte_eth_rx_queue_setup(g_dpif_ctx->memif_port_id, q_id, nb_rxd, socket_id, NULL, g_dpif_ctx->mbuf_pool);
        if (ret < 0) {
            DPIF_LOG_ERROR("Memif RX queue %u setup failed: %s", q_id, rte_strerror(-ret));
            return ret;
        }
        DPIF_LOG_INFO(
            "Memif RX queue %u (port %u) configured for RX Core Index %u.", q_id, g_dpif_ctx->memif_port_id, q_id);

        ret = rte_eth_tx_queue_setup(g_dpif_ctx->memif_port_id, q_id, nb_txd, socket_id, NULL);
        if (ret < 0) {
            DPIF_LOG_ERROR("Memif TX queue %u setup failed: %s", q_id, rte_strerror(-ret));
            return ret;
        }
    }
    return 0;
}

/**
 * @brief Initializes RX and Worker thread contexts.
 *
 * @return 0 on success, negative errno on failure.
 */
static int initialize_contexts() {
    g_dpif_ctx->rx_contexts =
        rte_calloc("RX_CONTEXTS_ARRAY", RTE_MAX_LCORE, sizeof(dpif_rx_context_t), RTE_CACHE_LINE_SIZE);
    if (!g_dpif_ctx->rx_contexts) {
        DPIF_LOG_ERROR("RX contexts allocation failed.");
        return -ENOMEM;
    }

    for (uint32_t i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
        uint32_t rx_lcore_id = g_dpif_ctx->rx_lcore_map[i];
        dpif_rx_context_t *ctx = &g_dpif_ctx->rx_contexts[rx_lcore_id];

        ctx->lcore_id = rx_lcore_id;
        ctx->memif_port_id = g_dpif_ctx->memif_port_id;
        ctx->memif_queue_id = i;  // Assuming 1-to-1 mapping of RX core index to memif queue ID
        ctx->mbuf_pool = g_dpif_ctx->mbuf_pool;
        ctx->session_pool = g_dpif_ctx->session_pool;
        ctx->work_pool = g_dpif_ctx->work_pool;                    // Workers and RX share the same work pool
        ctx->task_dist_policy = DPIF_TASK_DIST_RX_HASH_TO_WORKER;  // Default policy
        ctx->quit_signal = &g_dpif_ctx->quit_signal;
        ctx->num_workers = g_dpif_ctx->num_worker_cores;
        ctx->worker_rings = g_dpif_ctx->all_task_rings;  // All RX threads can see all worker task rings
        ctx->completion_ring = (g_dpif_ctx->all_completion_rings && i < g_dpif_ctx->num_total_completion_rings)
            ? g_dpif_ctx->all_completion_rings[i]
            : NULL;
        ctx->registered_callbacks = &g_dpif_ctx->registered_callbacks;
        rte_atomic64_init(&ctx->sessions_updated_by_timer);  // Initialize new counter
        rte_timer_init(&ctx->periodic_self_update_timer);    // Initialize new timer

        char hash_name[RTE_HASH_NAMESIZE];
        snprintf(hash_name, sizeof(hash_name), "%s_sess_hash_rx%u", g_dpif_ctx->app_cfg.app_name_prefix, rx_lcore_id);
        struct rte_hash_parameters hash_params = {
            .name = hash_name,
            .entries = g_dpif_spec_settings.session_hash_entries,  // Now 1M entries for better distribution
            .key_len = sizeof(dpi_flow_key_t),                     // Updated for IPv6 compatible key
            .hash_func = rte_jhash,                                // jhash is optimized for network flows
            .hash_func_init_val = 0x12345678,                      // Non-zero init value for better distribution
            .socket_id = rte_lcore_to_socket_id(rx_lcore_id),
            .extra_flag =
                RTE_HASH_EXTRA_FLAGS_TRANS_MEM_SUPPORT  // Enable transactional memory support for better performance
        };
        ctx->session_table = rte_hash_create(&hash_params);
        if (!ctx->session_table) {
            DPIF_LOG_ERROR("Session hash '%s' create failed: %s", hash_name, rte_strerror(rte_errno));
            return -rte_errno;
        }
        DPIF_LOG_INFO("Session hash '%s' for RX Core Index %u (Lcore %u) created (key_len: %u, entries: %u).",
                      hash_name,
                      i,
                      rx_lcore_id,
                      hash_params.key_len,
                      hash_params.entries);

        ctx->max_sessions_this_core = MAX_SESSIONS_PER_RX_CORE_DEFAULT - 1;

        ctx->sessions_by_idx = rte_calloc_socket("SESS_BY_IDX",
                                                 MAX_SESSIONS_PER_RX_CORE_DEFAULT,
                                                 sizeof(dpif_session_t *),
                                                 RTE_CACHE_LINE_SIZE,  // Added alignment
                                                 rte_lcore_to_socket_id(rx_lcore_id));
        if (!ctx->sessions_by_idx) {
            DPIF_LOG_ERROR(
                "RX %u: sessions_by_idx allocate failed (size %u)", rx_lcore_id, ctx->max_sessions_this_core);
            return -ENOMEM;
        }

        char ring_name[RTE_RING_NAMESIZE];
        snprintf(ring_name, sizeof(ring_name), "%s_free_idx_rx%u", g_dpif_ctx->app_cfg.app_name_prefix, rx_lcore_id);
        ctx->free_session_indices_ring = rte_ring_create(ring_name,
                                                         MAX_SESSIONS_PER_RX_CORE_DEFAULT,
                                                         rte_lcore_to_socket_id(rx_lcore_id),
                                                         RING_F_SP_ENQ | RING_F_SC_DEQ);
        if (!ctx->free_session_indices_ring) {
            DPIF_LOG_ERROR("RX %u: free_session_indices_ring create failed", rx_lcore_id);
            rte_free(ctx->sessions_by_idx);
            ctx->sessions_by_idx = NULL;  // Cleanup
            return -ENOMEM;               // Or cleanup and return specific error
        }

        for (uint32_t i = 0; i < ctx->max_sessions_this_core; ++i) {
            if (rte_ring_sp_enqueue(ctx->free_session_indices_ring, (void *) (uintptr_t) i) != 0) {
                DPIF_LOG_ERROR("RX %u: Index %u enqueue free_session_indices_ring failed (ring capacity %u, count %u)",
                               rx_lcore_id,
                               i,
                               rte_ring_get_capacity(ctx->free_session_indices_ring),
                               rte_ring_count(ctx->free_session_indices_ring));
                rte_ring_free(ctx->free_session_indices_ring);
                ctx->free_session_indices_ring = NULL;
                rte_free(ctx->sessions_by_idx);
                ctx->sessions_by_idx = NULL;
                return -EFAULT;
            }
        }
        DPIF_LOG_INFO("RX %u: free_session_indices_ring Init done, capacity %u, current %u",
                      rx_lcore_id,
                      rte_ring_get_capacity(ctx->free_session_indices_ring),
                      rte_ring_count(ctx->free_session_indices_ring));
    }

    if (g_dpif_ctx->num_worker_cores > 0) {
        g_dpif_ctx->worker_contexts =
            rte_calloc("WORKER_CONTEXTS_ARRAY", RTE_MAX_LCORE, sizeof(dpif_worker_context_t), RTE_CACHE_LINE_SIZE);
        if (!g_dpif_ctx->worker_contexts) {
            DPIF_LOG_ERROR("Worker contexts allocation failed.");
            return -ENOMEM;
        }
        for (uint32_t i = 0; i < g_dpif_ctx->num_worker_cores; ++i) {
            uint32_t worker_lcore_id = g_dpif_ctx->worker_lcore_map[i];
            dpif_worker_context_t *wkr_ctx = &g_dpif_ctx->worker_contexts[worker_lcore_id];
            wkr_ctx->lcore_id = worker_lcore_id;
            wkr_ctx->worker_id = i;
            wkr_ctx->work_pool = g_dpif_ctx->work_pool;
            wkr_ctx->quit_signal = &g_dpif_ctx->quit_signal;
            wkr_ctx->registered_callbacks = &g_dpif_ctx->registered_callbacks;
            // Worker polls its own task ring(s)
            if (g_dpif_ctx->all_task_rings && i < g_dpif_ctx->num_total_task_rings) {
                wkr_ctx->task_rings = &g_dpif_ctx->all_task_rings[i];  // Point to its specific ring
                wkr_ctx->num_task_rings = 1;  // Each worker polls one task ring directly assigned
            } else {
                DPIF_LOG_ERROR("Task ring not available for worker index %u (Lcore %u).", i, worker_lcore_id);
                return -EINVAL;
            }
        }
    } else {
        g_dpif_ctx->worker_contexts = NULL;
    }
    return 0;
}

/**
 * @brief Starts the memif port and launches DPIF threads.
 *
 * @return 0 on success, negative errno on failure.
 */
static int start_memif_port_and_threads(void) {
    int ret = rte_eth_dev_start(g_dpif_ctx->memif_port_id);
    if (ret < 0) {
        DPIF_LOG_ERROR("Memif port %u start failed: %s", g_dpif_ctx->memif_port_id, rte_strerror(-ret));
        return ret;
    }
    DPIF_LOG_INFO("Memif port %u started.", g_dpif_ctx->memif_port_id);

    if (dpif_cli_server_start_internal() != 0) {
        DPIF_LOG_WARNING("Internal CLI server failed to start. Continuing without it.");
    }

    dpif_launch_threads();
    return 0;
}

/**
 * @brief Phase 1: Initializes DPIF resources but does not start threads or devices.
 */
int dpif_prepare(const dpif_app_config_t *app_config) {
    int ret;

    ret = validate_app_config(app_config);
    if (ret < 0)
        return ret;

    ret = initialize_eal(app_config);
    if (ret < 0)
        return ret;

    ret = setup_global_context(app_config);
    if (ret < 0)
        goto cleanup_eal;

    g_dpif_stats_snapshot = rte_zmalloc("DPIF_STATS_SNAPSHOT", sizeof(dpif_stats_snapshot_t), 64);
    if (g_dpif_stats_snapshot == NULL) {
        DPIF_LOG_ERROR("Failed to allocate memory for statistics snapshot.");
        goto cleanup_ctx;
    }

    ret = configure_timer_subsystem(app_config);
    if (ret < 0)
        goto cleanup_ctx;

    ret = assign_lcores();
    if (ret < 0)
        goto cleanup_ctx;

    ret = create_memory_pools(app_config);
    if (ret < 0)
        goto cleanup_mbuf_pool;

    ret = setup_task_and_completion_rings(app_config);
    if (ret < 0)
        goto cleanup_work_pool;

    ret = configure_memif_port();
    if (ret < 0)
        goto cleanup_comp_rings;

    ret = initialize_contexts();
    if (ret < 0)
        goto cleanup_thread_ctx;

#if SIMULATE_PACKETS
    dpif_sim_init();  // Initialize the simulation module
#endif

    DPIF_LOG_INFO("DPIF initialized successfully.");
    return 0;

cleanup_thread_ctx:
    if (g_dpif_ctx) {
        if (g_dpif_ctx->rx_contexts) {
            for (uint32_t i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
                uint32_t rlcore = g_dpif_ctx->rx_lcore_map[i];
                if (rlcore < RTE_MAX_LCORE && g_dpif_ctx->rx_contexts[rlcore].session_table) {
                    rte_hash_free(g_dpif_ctx->rx_contexts[rlcore].session_table);
                    g_dpif_ctx->rx_contexts[rlcore].session_table = NULL;
                }
            }  // End if (rlcore < RTE_MAX_LCORE && ...)
        }
        rte_free(g_dpif_ctx->worker_contexts);
        g_dpif_ctx->worker_contexts = NULL;
        rte_free(g_dpif_ctx->rx_contexts);
        g_dpif_ctx->rx_contexts = NULL;
    }
/* cleanup_dev_config: // This label was unused */
cleanup_comp_rings:
    if (g_dpif_ctx && g_dpif_ctx->all_completion_rings) {
        for (uint32_t i = 0; i < g_dpif_ctx->num_total_completion_rings; ++i)
            if (g_dpif_ctx->all_completion_rings[i])
                rte_ring_free(g_dpif_ctx->all_completion_rings[i]);
        rte_free(g_dpif_ctx->all_completion_rings);
        g_dpif_ctx->all_completion_rings = NULL;
    }
    if (g_dpif_ctx && g_dpif_ctx->all_task_rings) {
        for (uint32_t i = 0; i < g_dpif_ctx->num_total_task_rings; ++i)
            if (g_dpif_ctx->all_task_rings[i])
                rte_ring_free(g_dpif_ctx->all_task_rings[i]);
        rte_free(g_dpif_ctx->all_task_rings);
        g_dpif_ctx->all_task_rings = NULL;
    }
cleanup_work_pool:
    if (g_dpif_ctx && g_dpif_ctx->work_pool) {
        rte_mempool_free(g_dpif_ctx->work_pool);
        g_dpif_ctx->work_pool = NULL;
    }
cleanup_mbuf_pool:
    if (g_dpif_ctx && g_dpif_ctx->mbuf_pool) {
        rte_mempool_free(g_dpif_ctx->mbuf_pool);
        g_dpif_ctx->mbuf_pool = NULL;
    }
cleanup_ctx:
    rte_free(g_dpif_ctx);
    g_dpif_ctx = NULL;
cleanup_eal:
    if (g_dpif_ctx && g_dpif_ctx->eal_initialized_by_dpif) {
        DPIF_LOG_DEBUG("Cleaning up EAL due to initialization error.");
        rte_eal_cleanup();
        g_dpif_ctx->eal_initialized_by_dpif = 0;
    }
    return ret;
}

/**
 * @brief Phase 2: Starts the DPIF data plane.
 */
int dpif_start(void) {
    if (!g_dpif_ctx) {
        DPIF_LOG_ERROR("dpif_start called before dpif_prepare. Aborting.");
        return -EFAULT;
    }

    DPIF_LOG_INFO("Starting DPIF data plane threads and device...");

    int ret = start_memif_port_and_threads();
    if (ret < 0) {
        DPIF_LOG_ERROR("Failed to start DPIF data plane.");
        // Consider what cleanup is needed here. Maybe stop threads that did launch.
        return ret;
    }

    DPIF_LOG_INFO("DPIF data plane started successfully.");
    return 0;
}

/**
 * @brief Cleans up DPIF resources (mempools, rings, contexts).
 */
void dpif_cleanup_resources(void) {
    uint32_t i;
    if (!g_dpif_ctx)
        return;

    DPIF_LOG_INFO("Cleaning up DPIF resources...");

    if (g_dpif_ctx->memif_port_id != RTE_MAX_ETHPORTS && rte_eth_dev_is_valid_port(g_dpif_ctx->memif_port_id)) {
        DPIF_LOG_INFO("Stopping and closing memif port %u.", g_dpif_ctx->memif_port_id);
        rte_eth_dev_stop(g_dpif_ctx->memif_port_id);
        rte_eth_dev_close(g_dpif_ctx->memif_port_id);
        g_dpif_ctx->memif_port_id = RTE_MAX_ETHPORTS;
    }

    if (g_dpif_ctx->rx_contexts) {
        for (i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
            uint32_t lcore_id = g_dpif_ctx->rx_lcore_map[i];
            if (lcore_id < RTE_MAX_LCORE &&
                g_dpif_ctx->rx_contexts[lcore_id].session_table) {  // Check lcore_id is valid index
                DPIF_LOG_INFO("Cleaning sessions for RX Core Index %u (Lcore %u)...", i, lcore_id);
                const void *next_key;
                void *next_data;
                uint32_t iter = 0;
                struct rte_hash *h = g_dpif_ctx->rx_contexts[lcore_id].session_table;
                struct rte_mempool *sp = g_dpif_ctx->session_pool;

                while (rte_hash_iterate(h, &next_key, &next_data, &iter) >= 0) {
                    dpif_session_t *session = (dpif_session_t *) next_data;
                    if (session) {
                        rte_timer_stop_sync(&session->timeout_timer);
                        dpif_session_q_cleanup(session);
                        if (sp)
                            rte_mempool_put(sp, session);
                    }
                }
                DPIF_LOG_INFO("Freeing session hash table for RX Core Index %u (Lcore "
                              "%u)...",
                              i,
                              lcore_id);
                rte_hash_free(h);
                g_dpif_ctx->rx_contexts[lcore_id].session_table = NULL;

                if (g_dpif_ctx->rx_contexts[lcore_id].free_session_indices_ring) {
                    rte_ring_free(g_dpif_ctx->rx_contexts[lcore_id].free_session_indices_ring);
                    g_dpif_ctx->rx_contexts[lcore_id].free_session_indices_ring = NULL;
                }
                if (g_dpif_ctx->rx_contexts[lcore_id].sessions_by_idx) {
                    rte_free(g_dpif_ctx->rx_contexts[lcore_id].sessions_by_idx);
                    g_dpif_ctx->rx_contexts[lcore_id].sessions_by_idx = NULL;
                }
            }
        }
    }

    rte_free(g_dpif_ctx->worker_contexts);
    g_dpif_ctx->worker_contexts = NULL;
    rte_free(g_dpif_ctx->rx_contexts);
    g_dpif_ctx->rx_contexts = NULL;

    if (g_dpif_ctx->all_completion_rings) {
        for (i = 0; i < g_dpif_ctx->num_total_completion_rings; ++i)
            if (g_dpif_ctx->all_completion_rings[i])
                rte_ring_free(g_dpif_ctx->all_completion_rings[i]);
        rte_free(g_dpif_ctx->all_completion_rings);
        g_dpif_ctx->all_completion_rings = NULL;
    }
    if (g_dpif_ctx->all_task_rings) {
        for (i = 0; i < g_dpif_ctx->num_total_task_rings; ++i)
            if (g_dpif_ctx->all_task_rings[i])
                rte_ring_free(g_dpif_ctx->all_task_rings[i]);
        rte_free(g_dpif_ctx->all_task_rings);
        g_dpif_ctx->all_task_rings = NULL;
    }

    if (g_dpif_ctx->work_pool) {
        rte_mempool_free(g_dpif_ctx->work_pool);
        g_dpif_ctx->work_pool = NULL;
    }
    if (g_dpif_ctx->session_pool) {
        rte_mempool_free(g_dpif_ctx->session_pool);
        g_dpif_ctx->session_pool = NULL;
    }
    if (g_dpif_ctx->mbuf_pool) {
        rte_mempool_free(g_dpif_ctx->mbuf_pool);
        g_dpif_ctx->mbuf_pool = NULL;
    }

    if (g_dpif_stats_snapshot) {
        rte_free(g_dpif_stats_snapshot);
        g_dpif_stats_snapshot = NULL;
        DPIF_LOG_INFO("Statistics snapshot area freed.");
    }

    DPIF_LOG_INFO("DPIF resource cleanup finished.");
}

/**
 * @brief Stops DPIF threads and cleans up all resources, including EAL.
 */
void dpif_cleanup(void) {
    if (!g_dpif_ctx)
        return;

    DPIF_LOG_INFO("DPIF cleanup initiated...");

    dpif_stop_threads();

    dpif_cli_server_stop_internal();

    dpif_cleanup_resources();

    if (g_dpif_ctx->eal_initialized_by_dpif) {
        DPIF_LOG_INFO("Cleaning up EAL...");
        rte_eal_cleanup();
        g_dpif_ctx->eal_initialized_by_dpif = 0;
    }

    dpi_unregister_device();

    DPIF_LOG_INFO("Freeing global DPIF context...");
    rte_free(g_dpif_ctx);
    g_dpif_ctx = NULL;

    DPIF_LOG_INFO("DPIF cleanup complete.");
}

/**
 * @brief Launches all configured RX and Worker threads.
 */
void dpif_launch_threads(void) {
    uint32_t i;
    int ret_launch;

    // Launch RX threads
    for (i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
        uint32_t lcore_id = g_dpif_ctx->rx_lcore_map[i];
        if (lcore_id == RTE_MAX_LCORE || !rte_lcore_is_enabled(lcore_id)) {
            DPIF_LOG_ERROR("Invalid or disabled Lcore ID %u for RX index %u. Skipping launch.", lcore_id, i);
            continue;
        }
        dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[lcore_id];
        ret_launch = rte_eal_remote_launch((lcore_function_t *) dpif_rx_thread_main, rx_ctx, lcore_id);
        if (ret_launch != 0) {
            DPIF_LOG_ERROR("Failed to launch RX thread on Lcore %u: %s", lcore_id, rte_strerror(-ret_launch));
        } else {
            DPIF_LOG_INFO("Launched RX thread on Lcore %u (RX Index %u)", lcore_id, i);
            // Setup periodic session update timer for this RX core
            if (g_dpif_ctx->periodic_session_update_ticks > 0) {
                rte_timer_reset(&rx_ctx->periodic_self_update_timer,
                                g_dpif_ctx->periodic_session_update_ticks,
                                PERIODICAL,
                                lcore_id,
                                dpif_session_handle_periodic_updates,
                                rx_ctx);
                DPIF_LOG_INFO("Periodic session update timer armed for RX Lcore %u.", lcore_id);
            }
        }
    }

    // Launch Worker threads (if any)
    if (g_dpif_ctx->num_worker_cores > 0) {
        for (i = 0; i < g_dpif_ctx->num_worker_cores; ++i) {
            uint32_t lcore_id = g_dpif_ctx->worker_lcore_map[i];
            if (lcore_id == RTE_MAX_LCORE || !rte_lcore_is_enabled(lcore_id)) {
                DPIF_LOG_ERROR("Invalid or disabled Lcore ID %u for Worker index %u. Skipping launch.", lcore_id, i);
                continue;
            }
            dpif_worker_context_t *wkr_ctx = &g_dpif_ctx->worker_contexts[lcore_id];
            ret_launch = rte_eal_remote_launch((lcore_function_t *) dpif_worker_thread_main, wkr_ctx, lcore_id);
            if (ret_launch != 0) {
                DPIF_LOG_ERROR("Failed to launch Worker thread on Lcore %u: %s", lcore_id, rte_strerror(-ret_launch));
            } else {
                DPIF_LOG_INFO("Launched Worker thread on Lcore %u (Worker Index %u)", lcore_id, i);
                // Worker periodic timer for its own tasks (if any) is removed for now.
                // The app's session_update is now handled by RX threads.
            }
        }
    }
}

/**
 * @brief Signals all DPIF threads to stop.
 */
void dpif_stop(void) {
    if (g_dpif_ctx)
        g_dpif_ctx->quit_signal = 1;
}

/**
 * @brief Waits for all DPIF data plane threads to exit.
 */
void dpif_stop_threads(void) {
    uint32_t lcore_id;
    DPIF_LOG_INFO("Waiting for threads...");
    RTE_LCORE_FOREACH_WORKER(lcore_id) {
        if (rte_eal_wait_lcore(lcore_id) < 0) {
            DPIF_LOG_WARNING("Wait error core %u.", lcore_id);
        }
    }
    DPIF_LOG_INFO("Threads exited.");
}

/**
 * @brief Gets a pointer to a worker's task ring.
 *
 * @param worker_id The index of the worker.
 * @return struct rte_ring* Pointer to the ring, or NULL if not found/invalid.
 */
struct rte_ring *dpif_get_worker_ring(uint32_t worker_id) {
    if (!g_dpif_ctx || !g_dpif_ctx->all_task_rings || worker_id >= g_dpif_ctx->num_total_task_rings)
        return NULL;
    return g_dpif_ctx->all_task_rings[worker_id];
}

/**
 * @brief Gets a pointer to an RX core's completion ring.
 *
 * @param rx_lcore_id The lcore_id of the RX thread.
 * @return struct rte_ring* Pointer to the ring, or NULL if not found/invalid.
 */
struct rte_ring *dpif_get_completion_ring(uint32_t rx_lcore_id) {
    if (!g_dpif_ctx || !g_dpif_ctx->all_completion_rings || rx_lcore_id >= RTE_MAX_LCORE)
        return NULL;
    uint32_t rx_idx = g_dpif_ctx->lcore_to_rx_idx_map[rx_lcore_id];
    if (rx_idx >= g_dpif_ctx->num_total_completion_rings)
        return NULL;
    return g_dpif_ctx->all_completion_rings[rx_idx];
}

void dpif_config_init(int dpu_id, int sim) {
    if (dpu_id > 1) {
        if (sim) {
            default_core_configs = sim_n_core_configs;
            num_core_configs = sizeof(sim_n_core_configs) / sizeof(sim_n_core_configs[0]);
            default_args = sim_n_args;
            default_argc = sizeof(sim_n_args) / sizeof(sim_n_args[0]);
        } else {
            default_core_configs = dpu_n_core_configs;
            num_core_configs = sizeof(dpu_n_core_configs) / sizeof(dpu_n_core_configs[0]);
            default_args = dpu_n_args;
            default_argc = sizeof(dpu_n_args) / sizeof(dpu_n_args[0]);
        }
    } else {
        if (sim) {
            default_core_configs = sim_main_core_configs;
            num_core_configs = sizeof(sim_main_core_configs) / sizeof(sim_main_core_configs[0]);
            default_args = sim_main_args;
            default_argc = sizeof(sim_main_args) / sizeof(sim_main_args[0]);
        } else {
            default_core_configs = dpu_main_core_configs;
            num_core_configs = sizeof(dpu_main_core_configs) / sizeof(dpu_main_core_configs[0]);
            default_args = dpu_main_args;
            default_argc = sizeof(dpu_main_args) / sizeof(dpu_main_args[0]);
        }
    }
    g_dpif_spec_settings.core_configs = default_core_configs;
    g_dpif_spec_settings.num_core_configs = num_core_configs;
}