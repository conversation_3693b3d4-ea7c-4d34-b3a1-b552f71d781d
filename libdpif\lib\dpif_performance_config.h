/**
 * @file dpif_performance_config.h
 * @brief DPIF Performance Optimization Configuration
 * 
 * This file contains optimized configuration parameters for high-performance
 * packet processing in enterprise-grade network security applications.
 * 
 * <AUTHOR> Optimization Team
 * @date 2025
 */

#ifndef DPIF_PERFORMANCE_CONFIG_H
#define DPIF_PERFORMANCE_CONFIG_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * BURST SIZE OPTIMIZATIONS
 * ======================================================================== */

/** @brief Optimized RX burst size for high throughput */
#define DPIF_OPTIMIZED_RX_BURST_SIZE        1024

/** @brief Optimized worker task burst size for batch processing */
#define DPIF_OPTIMIZED_WORKER_BURST_SIZE    128

/** @brief Optimized completion burst size */
#define DPIF_OPTIMIZED_COMPLETION_BURST_SIZE 64

/* ========================================================================
 * MEMORY POOL OPTIMIZATIONS
 * ======================================================================== */

/** @brief Optimized mbuf pool configuration */
typedef struct {
    uint32_t mbuf_count;        /**< Number of mbufs (32768) */
    uint16_t mbuf_cache_size;   /**< Cache size per core (1024) */
    uint16_t mbuf_data_room;    /**< Data room size (2048) */
} dpif_optimized_mbuf_config_t;

/** @brief Optimized session pool configuration */
typedef struct {
    uint32_t session_count;         /**< Total sessions (1.2M) */
    uint16_t session_cache_size;    /**< Cache size per core (1024) */
    uint32_t hash_entries;          /**< Hash table entries (1M) */
    uint32_t timeout_seconds;       /**< Session timeout (120s) */
} dpif_optimized_session_config_t;

/** @brief Optimized work pool configuration */
typedef struct {
    uint32_t work_count;        /**< Work items (16384) */
    uint16_t work_cache_size;   /**< Cache size per core (512) */
    uint32_t max_work_data;     /**< Max work data size (10KB) */
} dpif_optimized_work_config_t;

/* ========================================================================
 * RING BUFFER OPTIMIZATIONS
 * ======================================================================== */

/** @brief Optimized ring buffer configuration */
typedef struct {
    uint32_t task_ring_size;        /**< Task ring size (32768) */
    uint32_t completion_ring_size;  /**< Completion ring size (32768) */
    uint32_t session_ring_size;     /**< Session index ring size (1M) */
    bool use_single_consumer;       /**< Use SC_DEQ optimization */
    bool use_single_producer;       /**< Use SP_ENQ optimization */
} dpif_optimized_ring_config_t;

/* ========================================================================
 * CPU AFFINITY AND NUMA OPTIMIZATIONS
 * ======================================================================== */

/** @brief CPU affinity configuration */
typedef struct {
    bool enable_cpu_isolation;      /**< Isolate CPUs from OS scheduler */
    uint32_t rx_cpu_mask;          /**< CPU mask for RX threads */
    uint32_t worker_cpu_mask;      /**< CPU mask for worker threads */
    uint32_t management_cpu_mask;  /**< CPU mask for management */
    int numa_node;                 /**< Preferred NUMA node (-1 for auto) */
} dpif_cpu_affinity_config_t;

/* ========================================================================
 * CACHE OPTIMIZATION SETTINGS
 * ======================================================================== */

/** @brief Cache optimization configuration */
typedef struct {
    bool enable_prefetch;           /**< Enable data prefetching */
    uint32_t prefetch_distance;     /**< Prefetch distance (2-4 cache lines) */
    bool align_data_structures;     /**< Align to cache line boundaries */
    uint32_t cache_line_size;       /**< Cache line size (64 bytes) */
} dpif_cache_optimization_t;

/* ========================================================================
 * PERFORMANCE TUNING PARAMETERS
 * ======================================================================== */

/** @brief Performance tuning configuration */
typedef struct {
    /* Polling and timing */
    uint32_t poll_interval_us;      /**< Polling interval (50us) */
    uint32_t timer_resolution_us;   /**< Timer resolution (100us) */
    bool adaptive_polling;          /**< Enable adaptive polling */
    
    /* Batch processing */
    bool enable_batch_processing;   /**< Enable batch operations */
    uint32_t batch_timeout_us;      /**< Batch timeout (100us) */
    
    /* Memory management */
    bool enable_huge_pages;         /**< Use huge pages */
    bool enable_numa_awareness;     /**< NUMA-aware allocation */
    
    /* Session management */
    bool lazy_timer_updates;        /**< Lazy session timer updates */
    uint32_t timer_update_interval; /**< Timer update interval (100 packets) */
    
    /* Statistics */
    bool enable_detailed_stats;     /**< Enable detailed statistics */
    uint32_t stats_update_interval; /**< Stats update interval (1s) */
} dpif_performance_tuning_t;

/* ========================================================================
 * DEFAULT OPTIMIZED CONFIGURATIONS
 * ======================================================================== */

/** @brief Default optimized mbuf configuration */
static const dpif_optimized_mbuf_config_t DPIF_DEFAULT_OPTIMIZED_MBUF = {
    .mbuf_count = 32768,
    .mbuf_cache_size = 1024,
    .mbuf_data_room = 2048
};

/** @brief Default optimized session configuration */
static const dpif_optimized_session_config_t DPIF_DEFAULT_OPTIMIZED_SESSION = {
    .session_count = 1200000,
    .session_cache_size = 1024,
    .hash_entries = 1048576,
    .timeout_seconds = 120
};

/** @brief Default optimized work configuration */
static const dpif_optimized_work_config_t DPIF_DEFAULT_OPTIMIZED_WORK = {
    .work_count = 16384,
    .work_cache_size = 512,
    .max_work_data = 10240
};

/** @brief Default optimized ring configuration */
static const dpif_optimized_ring_config_t DPIF_DEFAULT_OPTIMIZED_RING = {
    .task_ring_size = 32768,
    .completion_ring_size = 32768,
    .session_ring_size = 1048576,
    .use_single_consumer = true,
    .use_single_producer = true
};

/** @brief Default performance tuning configuration */
static const dpif_performance_tuning_t DPIF_DEFAULT_PERFORMANCE_TUNING = {
    .poll_interval_us = 50,
    .timer_resolution_us = 100,
    .adaptive_polling = true,
    .enable_batch_processing = true,
    .batch_timeout_us = 100,
    .enable_huge_pages = true,
    .enable_numa_awareness = true,
    .lazy_timer_updates = true,
    .timer_update_interval = 100,
    .enable_detailed_stats = true,
    .stats_update_interval = 1000000  // 1 second in microseconds
};

/* ========================================================================
 * PERFORMANCE OPTIMIZATION FUNCTIONS
 * ======================================================================== */

/**
 * @brief Apply optimized configuration to DPIF
 * @param config Performance configuration to apply
 * @return 0 on success, negative on error
 */
int dpif_apply_performance_config(const dpif_performance_tuning_t *config);

/**
 * @brief Get current performance metrics
 * @param metrics Output buffer for metrics
 * @return 0 on success, negative on error
 */
int dpif_get_performance_metrics(void *metrics);

/**
 * @brief Auto-tune performance parameters based on workload
 * @return 0 on success, negative on error
 */
int dpif_auto_tune_performance(void);

#ifdef __cplusplus
}
#endif

#endif /* DPIF_PERFORMANCE_CONFIG_H */
