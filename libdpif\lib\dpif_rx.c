#include "dpif_private.h"
#include <netinet/in.h>  // For AF_INET, AF_INET6
#include <rte_byteorder.h>
#include <rte_errno.h>
#include <rte_ethdev.h>
#include <rte_ether.h>
#include <rte_ip.h>  // For IPv4 header, IPPROTO_TCP, IPPROTO_UDP
#include <rte_jhash.h>
#include <rte_tcp.h>
#include <rte_udp.h>

#if SIMULATE_PACKETS
#include "dpif_sim.h"  // Include the new simulation header
#endif

#define RX_BURST_SIZE 512
#define COMPLETION_BURST_SIZE 32
#define MISC_CHECK_INTERVAL_US 100  // Interval for checking completion queue and timers
#define RTE_IPV6_VERSION_SHIFT 4
#define RTE_IPV6_VERSION_MASK 0xF0

extern dpif_global_context_t *g_dpif_ctx;
static int dpif_parse_packet_get_flow(struct rte_mbuf *m, struct dpif_flow_info *flow_info);

/**
 * @brief Calculates the target worker ID for a given flow key.
 *
 * @param ctx The RX context.
 * @param key The canonical flow key.
 * @return uint32_t The target worker ID.
 */
static inline uint32_t get_target_worker_id(dpif_rx_context_t *ctx, const dpi_flow_key_t *key) {
    if (!ctx || ctx->num_workers == 0) {
        DPIF_LOG_WARNING("get_target_worker_id: No workers configured or invalid context, defaulting to worker 0.");
        return 0;
    }
    uint32_t hash_val = rte_jhash(key, sizeof(*key), ctx->lcore_id);
    return hash_val % ctx->num_workers;
}

/**
 * @brief Processes messages from the completion queue.
 *
 * @param ctx The RX context.
 */
void dpif_rx_process_completion_queue(dpif_rx_context_t *ctx) {
    void *msg_ptrs[COMPLETION_BURST_SIZE];
    int i;
    uint16_t nb_dequeued;

    if (!ctx || !ctx->completion_ring) {
        DPIF_LOG_ERROR("dpif_rx_process_completion_queue: Invalid context or completion ring.");
        return;
    }

    nb_dequeued = rte_ring_sc_dequeue_burst(ctx->completion_ring, msg_ptrs, COMPLETION_BURST_SIZE, NULL);

    if (nb_dequeued > 0) {
        rte_atomic64_add(&ctx->completion_msgs_processed, nb_dequeued);
        for (i = 0; i < nb_dequeued; ++i) {
            dpif_session_t *session = (dpif_session_t *) msg_ptrs[i];
            if (!session || session->owner_rx_lcore != ctx->lcore_id) {
                DPIF_LOG_DEBUG("RX %u: Ignoring completion msg for session owned by %u (session ptr %p).",
                               ctx->lcore_id,
                               session ? session->owner_rx_lcore : RTE_MAX_LCORE,
                               (void *) session);
                continue;
            }

            session->is_task_running = 0;

            struct rte_mbuf *m_queued = NULL;
            while ((m_queued = dpif_session_q_dequeue_pkt(session)) != NULL) {
                struct dpif_flow_info f_queued;
                // Netlink handling for queued packets:
                // If packets placed on the session queue could also have the outer encapsulation,
                // then dpif_decap_vpp_head should be called here too.
                // For simplicity now, assuming queued packets are already "inner" packets.
                // struct rte_mbuf *inner_m_queued = dpif_decap_vpp_head(m_queued, NULL);
                // if (!inner_m_queued) { continue; }
                // Then parse inner_m_queued.
                // Current code assumes m_queued is ready for dpif_parse_packet_get_flow.

                if (dpif_parse_packet_get_flow(m_queued, &f_queued) != 0) {  // Pass m_queued directly
                    DPIF_LOG_WARNING("RX %u: Failed to parse queued packet for SD %d. Dropping.",
                                     ctx->lcore_id,
                                     session->libdpif_internal_sd);
                    rte_pktmbuf_free(m_queued);
                    rte_atomic64_inc(&ctx->dropped_pkts);
                    continue;
                }

                struct dpi_packet p_queued = {.first_mbuf = m_queued,
                                              .current_mbuf = m_queued,
                                              .direction = f_queued.direction,
                                              .current_offset = 0};
                int analyze_result_queued =
                    ctx->registered_callbacks->dpi_session_analyze(session->libdpif_internal_sd, &p_queued);
                uint16_t queue_to_send_on = ctx->memif_queue_id;
                switch (analyze_result_queued) {
                case DPI_VERDICT_PENDING:
                    // Action: Send NF_ACCEPT for the current packet, keep session alive.
                    DPIF_LOG_DEBUG("RX %u: SD %d: Verdict PENDING. Accepting current packet.",
                                   ctx->lcore_id,
                                   session->libdpif_internal_sd);
                    dpif_issue_verdict(DPIF_MBUF_METADATA(m_queued), 1, queue_to_send_on, DPIF_MSG_PACKET_VERDICT);
                    break;

                case DPI_VERDICT_PERMITTED:
                    // Action: Send NF_ACCEPT. The concept of a "session verdict" vs "packet verdict"
                    // may imply installing a offload rule in VPP for the whole flow
                    DPIF_LOG_DEBUG("RX %u: SD %d: Verdict PERMITTED. Accepting session.",
                                   ctx->lcore_id,
                                   session->libdpif_internal_sd);
                    dpif_issue_verdict(DPIF_MBUF_METADATA(m_queued), 1, queue_to_send_on, DPIF_MSG_SESSION_VERDICT);
                    break;

                case DPI_VERDICT_DROP:
                    // Action: Send NF_DROP verdict and delete the session immediately.
                    DPIF_LOG_DEBUG("RX %u: SD %d: Verdict DROP. Dropping and deleting session.",
                                   ctx->lcore_id,
                                   session->libdpif_internal_sd);
                    dpif_issue_verdict(DPIF_MBUF_METADATA(m_queued), 0, queue_to_send_on, DPIF_MSG_SESSION_VERDICT);
                    dpif_session_delete(ctx, session);
                    break;

                case DPI_VERDICT_ERROR:
                default:  // Also treat any other unknown value as an error.
                    DPIF_LOG_WARNING("RX %u: SD %d: dpi_session_analyze returned error or unknown code %d. Dropping "
                                     "packet.",
                                     ctx->lcore_id,
                                     session->libdpif_internal_sd,
                                     analyze_result_queued);
                    rte_atomic64_inc(&ctx->dropped_pkts);
                    // In case of an analysis error, we drop the packet within libdpif
                    // and do not send any verdict to VPP, letting VPP handle it (e.g., timeout).
                    break;
                }
                rte_pktmbuf_free(m_queued);  // Free the mbuf after processing.
            }
        }
    }
}

/**
 * @brief Enqueues a work item to the appropriate worker thread.
 *
 * @param ctx The RX context.
 * @param session The session associated with the work.
 * @param work The work item to enqueue.
 * @return int 0 on success, negative errno on failure.
 */
int dpif_rx_enqueue_work(dpif_rx_context_t *ctx, dpif_session_t *session, struct dpi_work *work) {
    if (!ctx || !session || !work || !ctx->work_pool || ctx->num_workers == 0) {
        DPIF_LOG_ERROR("dpif_rx_enqueue_work: Invalid arguments or uninitialized resources.");
        if (work && ctx && ctx->work_pool) {
            rte_mempool_put(ctx->work_pool, work);
        }
        return -EINVAL;
    }
    uint32_t target_worker_idx = get_target_worker_id(ctx, &session->key);
    struct rte_ring *target_ring = dpif_get_worker_ring(target_worker_idx);
    if (!target_ring) {
        DPIF_LOG_ERROR("RX %u: No worker ring found for worker_id %u (session SD %d).",
                       ctx->lcore_id,
                       target_worker_idx,
                       session->libdpif_internal_sd);
        rte_mempool_put(ctx->work_pool, work);
        return -ENOENT;
    }
    work->session_ptr = session;
    int ret = rte_ring_mp_enqueue(target_ring, work);
    if (ret != 0) {
        DPIF_LOG_WARNING("RX %u: Failed to enqueue work for SD %d to worker %u: %s.",
                         ctx->lcore_id,
                         session->libdpif_internal_sd,
                         target_worker_idx,
                         rte_strerror(-ret));
        rte_mempool_put(ctx->work_pool, work);
        return ret;
    }
    rte_atomic64_inc(&ctx->tasks_offloaded);
    session->is_task_running = 1;
    DPIF_LOG_DEBUG(
        "RX %u: Enqueued work for SD %d to worker %u.", ctx->lcore_id, session->libdpif_internal_sd, target_worker_idx);
    return 0;
}

// --- Packet Parsing ---

/**
 * @brief Parses an IPv6 packet to extract flow information.
 *
 * Handles IPv6 header and attempts to find TCP/UDP header, skipping known
 * extension headers.
 *
 * @param m The mbuf containing the packet, starting at the Ethernet header.
 * @param ip6_h Pointer to the IPv6 header within the mbuf.
 * @param flow_info Output parameter, pointer to a dpif_flow_info struct to be filled.
 * @return 0 on success, negative value on error.
 */
static int
parse_ipv6_packet_get_l4_info(struct rte_mbuf *m, struct rte_ipv6_hdr *ip6_h, struct dpif_flow_info *flow_info) {
    uint8_t next_proto = ip6_h->proto;
    unsigned char *l4_hdr_ptr = (unsigned char *) (ip6_h + 1);  // Pointer after base IPv6 header
    uint16_t current_offset = sizeof(struct rte_ether_hdr) + sizeof(struct rte_ipv6_hdr);
    int ext_hdrs_processed = 0;
    const int MAX_EXT_HDRS_TO_PARSE = 5;  // Safety limit for extension header chain

    // Loop to skip IPv6 extension headers
    while (ext_hdrs_processed < MAX_EXT_HDRS_TO_PARSE) {
        if (next_proto == IPPROTO_TCP || next_proto == IPPROTO_UDP) {
            break;  // Found L4 protocol
        }
        // Check if we have enough data for a generic extension header (at least 2 bytes for next_header and hdr_ext_len)
        if (rte_pktmbuf_data_len(m) < current_offset + 2) {
            DPIF_LOG_DEBUG("IPv6: Packet too short for extension header fields at offset %u.", current_offset);
            return -EBADMSG;
        }

        struct rte_ipv6_extension_hdr {  // Generic structure to read next_header and hdr_ext_len
            uint8_t next_header;
            uint8_t hdr_ext_len;  // Length of this extension header in 8-octet units, not including first 8 octets
        } *ext_hdr = (struct rte_ipv6_extension_hdr *) l4_hdr_ptr;

        uint16_t ext_hdr_total_len_bytes = (ext_hdr->hdr_ext_len + 1) * 8;

        if (rte_pktmbuf_data_len(m) < current_offset + ext_hdr_total_len_bytes) {
            DPIF_LOG_DEBUG("IPv6: Packet too short for full extension header (type %u) of %u bytes at offset %u.",
                           next_proto,
                           ext_hdr_total_len_bytes,
                           current_offset);
            return -EBADMSG;
        }
        // TODO: Add specific handling for critical extension headers if needed (e.g., Fragment)
        // For now, we just skip them.
        DPIF_LOG_DEBUG(
            "IPv6: Skipping extension header type %u, length %u bytes.", next_proto, ext_hdr_total_len_bytes);

        next_proto = ext_hdr->next_header;
        l4_hdr_ptr += ext_hdr_total_len_bytes;
        current_offset += ext_hdr_total_len_bytes;
        ext_hdrs_processed++;
    }

    if (ext_hdrs_processed >= MAX_EXT_HDRS_TO_PARSE && !(next_proto == IPPROTO_TCP || next_proto == IPPROTO_UDP)) {
        DPIF_LOG_DEBUG("IPv6: Too many extension headers or L4 protocol not found.");
        return -EPROTONOSUPPORT;
    }

    flow_info->proto = next_proto;  // This should now be TCP or UDP

    uint16_t l4_hdr_len_val = 0;
    if (flow_info->proto == IPPROTO_TCP) {
        if (rte_pktmbuf_data_len(m) < current_offset + sizeof(struct rte_tcp_hdr)) {
            DPIF_LOG_DEBUG("IPv6: Packet too short for TCP header at offset %u.", current_offset);
            return -EBADMSG;
        }
        struct rte_tcp_hdr *tcp_h = (struct rte_tcp_hdr *) l4_hdr_ptr;
        uint8_t data_offset_words = tcp_h->data_off >> 4;
        if (data_offset_words < 5 || data_offset_words > 15) {
            DPIF_LOG_WARNING("IPv6: Invalid TCP data offset: %u. Malformed packet.", data_offset_words);
            return -EBADMSG;
        }
        uint16_t declared_tcp_hdr_len_bytes = data_offset_words * 4;
        if (rte_pktmbuf_data_len(m) < current_offset + declared_tcp_hdr_len_bytes) {
            DPIF_LOG_DEBUG("IPv6: Packet too short for declared TCP header length of %u bytes.",
                           declared_tcp_hdr_len_bytes);
            return -EBADMSG;
        }
        l4_hdr_len_val = declared_tcp_hdr_len_bytes;
        flow_info->src_port = tcp_h->src_port;
        flow_info->dst_port = tcp_h->dst_port;
    } else if (flow_info->proto == IPPROTO_UDP) {
        if (rte_pktmbuf_data_len(m) < current_offset + sizeof(struct rte_udp_hdr)) {
            DPIF_LOG_DEBUG("IPv6: Packet too short for UDP header at offset %u.", current_offset);
            return -EBADMSG;
        }
        struct rte_udp_hdr *udp_h = (struct rte_udp_hdr *) l4_hdr_ptr;
        if (rte_be_to_cpu_16(udp_h->dgram_len) < sizeof(struct rte_udp_hdr)) {
            DPIF_LOG_DEBUG("IPv6: Invalid UDP datagram length: %u.", rte_be_to_cpu_16(udp_h->dgram_len));
            return -EBADMSG;
        }
        l4_hdr_len_val = sizeof(struct rte_udp_hdr);
        flow_info->src_port = udp_h->src_port;
        flow_info->dst_port = udp_h->dst_port;
    } else {
        l4_hdr_len_val = 0;
    }

    flow_info->l4_payload_offset = current_offset + l4_hdr_len_val;
    return 0;  // Success
}

/**
 * @brief Parses a packet to extract flow information (5-tuple, protocol, etc.).
 *
 * Handles Ethernet, IPv4, and IPv6 headers, and extracts L4 (TCP/UDP) port information.
 * @param m Pointer to the mbuf containing the packet.
 * @param flow_info Pointer to a dpif_flow_info struct to be filled with parsed information.
 * @return 0 on success, negative value on error (e.g., malformed packet, unsupported protocol).
 */
static int dpif_parse_packet_get_flow(struct rte_mbuf *m, struct dpif_flow_info *flow_info) {
    if (!m || !flow_info) {
        DPIF_LOG_DEBUG("dpif_parse_packet_get_flow: Invalid input.");
        return -EINVAL;
    }
    memset(flow_info, 0, sizeof(struct dpif_flow_info));  // Initialize flow_info

    if (rte_pktmbuf_data_len(m) < sizeof(struct rte_ether_hdr)) {
        DPIF_LOG_DEBUG("dpif_parse_packet_get_flow: Packet too short for Ethernet header (len: %u).",
                       rte_pktmbuf_data_len(m));
        return -EBADMSG;
    }
    struct rte_ether_hdr *eth_h = rte_pktmbuf_mtod(m, struct rte_ether_hdr *);
    uint16_t ether_type = eth_h->ether_type;  // Network byte order

    memcpy(flow_info->src_mac, eth_h->src_addr.addr_bytes, 6);

    if (ether_type == rte_cpu_to_be_16(RTE_ETHER_TYPE_IPV4)) {
        flow_info->address_family = AF_INET;
        struct rte_ipv4_hdr *ip_h = (struct rte_ipv4_hdr *) (eth_h + 1);
        if (rte_pktmbuf_data_len(m) < (sizeof(struct rte_ether_hdr) + sizeof(struct rte_ipv4_hdr))) {
            DPIF_LOG_WARNING("IPv4: Packet too short for minimal IPv4 header.");
            return -EBADMSG;
        }
        uint16_t ip_hdr_len = rte_ipv4_hdr_len(ip_h);
        uint16_t ip_total_len = rte_be_to_cpu_16(ip_h->total_length);
        if (ip_hdr_len < sizeof(struct rte_ipv4_hdr)) {
            DPIF_LOG_WARNING("IPv4: Invalid header length: %u.", ip_hdr_len);
            return -EBADMSG;
        }
        if (rte_pktmbuf_data_len(m) < (sizeof(struct rte_ether_hdr) + ip_hdr_len)) {
            DPIF_LOG_WARNING("IPv4: Packet too short for full header (IHL: %u).", ip_hdr_len);
            return -EBADMSG;
        }

        flow_info->src_ip_u.ipv4_src_ip = ip_h->src_addr;
        flow_info->dst_ip_u.ipv4_dst_ip = ip_h->dst_addr;
        flow_info->proto = ip_h->next_proto_id;

        uint16_t offset_to_l4_header = sizeof(struct rte_ether_hdr) + ip_hdr_len;
        unsigned char *l4_hdr_ptr_ipv4 = rte_pktmbuf_mtod_offset(m, unsigned char *, offset_to_l4_header);
        uint16_t l4_header_length = 0;
        if (flow_info->proto == IPPROTO_TCP) {
            if (rte_pktmbuf_data_len(m) < offset_to_l4_header + sizeof(struct rte_tcp_hdr)) {
                DPIF_LOG_WARNING("IPv4/TCP: Packet too short for minimal TCP header.");
                return -EBADMSG;
            }
            struct rte_tcp_hdr *tcp_h = (struct rte_tcp_hdr *) l4_hdr_ptr_ipv4;
            uint8_t doff = tcp_h->data_off >> 4;
            if (doff < 5 || doff > 15) {
                DPIF_LOG_WARNING("IPv4/TCP: Invalid TCP data offset: %u.", doff);
                return -EBADMSG;
            }
            l4_header_length = doff * 4;
            if (rte_pktmbuf_data_len(m) < offset_to_l4_header + l4_header_length) {
                DPIF_LOG_WARNING("IPv4/TCP: Packet too short for declared TCP header length.");
                return -EBADMSG;
            }
            flow_info->src_port = tcp_h->src_port;
            flow_info->dst_port = tcp_h->dst_port;
            flow_info->l4_payload_len = ip_total_len - ip_hdr_len - l4_header_length;
        } else if (flow_info->proto == IPPROTO_UDP) {
            if (rte_pktmbuf_data_len(m) < offset_to_l4_header + sizeof(struct rte_udp_hdr)) {
                DPIF_LOG_WARNING("IPv4/UDP: Packet too short for UDP header.");
                return -EBADMSG;
            }
            struct rte_udp_hdr *udp_h = (struct rte_udp_hdr *) l4_hdr_ptr_ipv4;
            if (rte_be_to_cpu_16(udp_h->dgram_len) < sizeof(struct rte_udp_hdr)) {
                DPIF_LOG_WARNING("IPv4/UDP: Invalid UDP dgram length: %u.", rte_be_to_cpu_16(udp_h->dgram_len));
                return -EBADMSG;
            }
            l4_header_length = sizeof(struct rte_udp_hdr);
            flow_info->src_port = udp_h->src_port;
            flow_info->dst_port = udp_h->dst_port;
            flow_info->l4_payload_len = rte_be_to_cpu_16(udp_h->dgram_len) - sizeof(struct rte_udp_hdr);
        } else {
            flow_info->src_port = 0;
            flow_info->dst_port = 0;
            l4_header_length = 0;
            flow_info->l4_payload_len = ip_total_len - ip_hdr_len;
        }
        flow_info->l4_payload_offset = offset_to_l4_header + l4_header_length;
    } else if (ether_type == rte_cpu_to_be_16(RTE_ETHER_TYPE_IPV6)) {
        flow_info->address_family = AF_INET6;
        struct rte_ipv6_hdr *ip6_h = (struct rte_ipv6_hdr *) (eth_h + 1);
        if (rte_pktmbuf_data_len(m) < (sizeof(struct rte_ether_hdr) + sizeof(struct rte_ipv6_hdr))) {
            DPIF_LOG_WARNING("IPv6: Packet too short for IPv6 header.");
            return -EBADMSG;
        }
        // Basic validation of IPv6 version
        if (((ip6_h->vtc_flow & RTE_IPV6_VERSION_MASK) >> RTE_IPV6_VERSION_SHIFT) != 6) {
            DPIF_LOG_WARNING("IPv6: Invalid version in vtc_flow: %x",
                             (ip6_h->vtc_flow & RTE_IPV6_VERSION_MASK) >> RTE_IPV6_VERSION_SHIFT);
            return -EBADMSG;
        }

#if RTE_VERSION >= RTE_VERSION_NUM(24, 11, 0, 0)
        memcpy(flow_info->src_ip_u.ipv6_src_ip, ip6_h->src_addr.a, 16);
        memcpy(flow_info->dst_ip_u.ipv6_dst_ip, ip6_h->dst_addr.a, 16);
#else
        memcpy(flow_info->src_ip_u.ipv6_src_ip, ip6_h->src_addr, 16);
        memcpy(flow_info->dst_ip_u.ipv6_dst_ip, ip6_h->dst_addr, 16);
#endif

        // Parse L4 info, skipping extension headers
        if (parse_ipv6_packet_get_l4_info(m, ip6_h, flow_info) != 0) {
            return -EBADMSG;  // Error occurred while parsing L4 or extension headers
        }

        uint16_t ipv6_payload_len = rte_be_to_cpu_16(ip6_h->payload_len);
        uint16_t ext_and_l4_hdr_len =
            flow_info->l4_payload_offset - (sizeof(struct rte_ether_hdr) + sizeof(struct rte_ipv6_hdr));

        if (flow_info->proto == IPPROTO_TCP) {
            flow_info->l4_payload_len = ipv6_payload_len - ext_and_l4_hdr_len;
        } else if (flow_info->proto == IPPROTO_UDP) {
            struct rte_udp_hdr *udp_h = (struct rte_udp_hdr *) rte_pktmbuf_mtod_offset(
                m, void *, flow_info->l4_payload_offset - sizeof(struct rte_udp_hdr));
            flow_info->l4_payload_len = rte_be_to_cpu_16(udp_h->dgram_len) - sizeof(struct rte_udp_hdr);
        } else {
            flow_info->l4_payload_len = ipv6_payload_len - ext_and_l4_hdr_len;
        }
    } else {
        DPIF_LOG_WARNING("dpif_parse_packet_get_flow: Non-IP/IPv6 EtherType: 0x%04x.", rte_be_to_cpu_16(ether_type));
        return -ENOTSUP;  // Not an IP packet we handle for session processing
    }

    // Determine packet direction (common for IPv4 and IPv6 after IPs/ports are populated)
    if (flow_info->address_family == AF_INET) {
        uint32_t src_ip_hbo = rte_be_to_cpu_32(flow_info->src_ip_u.ipv4_src_ip);
        uint32_t dst_ip_hbo = rte_be_to_cpu_32(flow_info->dst_ip_u.ipv4_dst_ip);
        uint16_t src_port_hbo = rte_be_to_cpu_16(flow_info->src_port);
        uint16_t dst_port_hbo = rte_be_to_cpu_16(flow_info->dst_port);
        if (src_ip_hbo < dst_ip_hbo || (src_ip_hbo == dst_ip_hbo && src_port_hbo < dst_port_hbo)) {
            flow_info->direction = 0;
        } else if (src_ip_hbo > dst_ip_hbo || (src_ip_hbo == dst_ip_hbo && src_port_hbo > dst_port_hbo)) {
            flow_info->direction = 1;
        } else {
            flow_info->direction = 0;
        }  // IPs and Ports are identical
    } else {  // AF_INET6
        int ip_cmp = memcmp(flow_info->src_ip_u.ipv6_src_ip, flow_info->dst_ip_u.ipv6_dst_ip, 16);
        uint16_t src_port_hbo = rte_be_to_cpu_16(flow_info->src_port);
        uint16_t dst_port_hbo = rte_be_to_cpu_16(flow_info->dst_port);
        if (ip_cmp < 0 || (ip_cmp == 0 && src_port_hbo < dst_port_hbo)) {
            flow_info->direction = 0;
        } else if (ip_cmp > 0 || (ip_cmp == 0 && src_port_hbo > dst_port_hbo)) {
            flow_info->direction = 1;
        } else {
            flow_info->direction = 0;
        }  // IPs and Ports are identical
    }

    return 0;  // Success
}

/**
 * @brief Processes a packet after its flow information has been parsed.
 *
 * Looks up or creates a session, updates session activity, and calls the
 * application's analyze callback. Handles packet queuing if a task is already running for the session.
 * @param ctx Pointer to the RX thread context.
 * @param m Pointer to the mbuf containing the packet (inner packet after decap if applicable).
 * @param flow_info Pointer to the parsed flow information.
 */
static void dpif_process_parsed_packet(dpif_rx_context_t *ctx, struct rte_mbuf *m, struct dpif_flow_info *flow_info) {
    dpif_session_t *s = NULL;

    s = dpif_session_lookup_or_create(ctx, m, flow_info);

    if (!s) {
        DPIF_LOG_WARNING("RX %u: Session lookup/create failed. Dropping packet.", ctx->lcore_id);
        rte_pktmbuf_free(m);
        rte_atomic64_inc(&ctx->dropped_pkts);
        return;
    }

    rte_atomic32_set(&s->active_flag, 1);
    s->last_active_time = rte_rdtsc();
    if (g_dpif_ctx && g_dpif_ctx->session_timeout_ticks > 0) {
        rte_timer_reset(&s->timeout_timer,
                        g_dpif_ctx->session_timeout_ticks,
                        SINGLE,
                        ctx->lcore_id,
                        dpif_session_timeout_callback,
                        s);
    }

    if (s->inspected) {
        DPIF_LOG_DEBUG("Already inspected, return");
        rte_pktmbuf_free(m);
        return;
    }

    if (likely(s->app_data != NULL)) {
        rte_prefetch0(s->app_data);
    }

    if (s->is_task_running) {
        DPIF_LOG_DEBUG("RX %u: SD %d: Task already running, queueing packet.", ctx->lcore_id, s->libdpif_internal_sd);
        dpif_session_q_enqueue_pkt(s, m, ctx);
    } else {
        DPIF_LOG_DEBUG("RX %u: SD %d: No task running, analyzing packet.", ctx->lcore_id, s->libdpif_internal_sd);
        struct dpi_packet p = {.first_mbuf = m,
                               .current_mbuf = m,
                               .direction = flow_info->direction,
                               .current_offset = flow_info->l4_payload_offset,
                               .total_payload_len = flow_info->l4_payload_len,
                               .bytes_yielded = 0};

        rte_atomic64_inc(&ctx->analyzed_pkts);
        uint64_t t1_analyze = rte_rdtsc();
        int analyze_result = ctx->registered_callbacks->dpi_session_analyze(s->libdpif_internal_sd, &p);
        uint64_t t2_analyze = rte_rdtsc();
        rte_atomic64_add(&ctx->analyze_cycles, t2_analyze - t1_analyze);

        // Update stats for any non-error case, as the packet was analyzed.
        if (analyze_result != DPI_VERDICT_ERROR) {
            if (flow_info->direction == 0) {
                rte_atomic64_add(&s->packets_fwd, 1);
                rte_atomic64_add(&s->bytes_fwd, m->pkt_len);
            } else {
                rte_atomic64_add(&s->packets_rev, 1);
                rte_atomic64_add(&s->bytes_rev, m->pkt_len);
            }
        }

        uint16_t queue_to_send_on = ctx->memif_queue_id;
        switch (analyze_result) {
        case DPI_VERDICT_PENDING:
            // Action: Send NF_ACCEPT for the current packet, keep session alive.
            DPIF_LOG_DEBUG(
                "RX %u: SD %d: Verdict PENDING. Accepting current packet.", ctx->lcore_id, s->libdpif_internal_sd);
            dpif_issue_verdict(DPIF_MBUF_METADATA(m), 1, queue_to_send_on, DPIF_MSG_PACKET_VERDICT);
            break;

        case DPI_VERDICT_PERMITTED:
            // Action: Send NF_ACCEPT. The concept of a "session verdict" vs "packet verdict"
            // may simply installing a offload rule in VPP for the whole flow
            s->inspected = 1;
            DPIF_LOG_DEBUG(
                "RX %u: SD %d: Verdict PERMITTED. Accepting session.", ctx->lcore_id, s->libdpif_internal_sd);
            dpif_issue_verdict(DPIF_MBUF_METADATA(m), 1, queue_to_send_on, DPIF_MSG_SESSION_VERDICT);
            break;

        case DPI_VERDICT_DROP:
            // Action: Send NF_DROP verdict and delete the session immediately.
            s->inspected = 1;
            DPIF_LOG_DEBUG(
                "RX %u: SD %d: Verdict DROP. Dropping and deleting session.", ctx->lcore_id, s->libdpif_internal_sd);
            dpif_issue_verdict(DPIF_MBUF_METADATA(m), 0, queue_to_send_on, DPIF_MSG_SESSION_VERDICT);
            //dpif_session_delete(ctx, s);
            break;

        case DPI_VERDICT_ERROR:
        default:  // Also treat any other unknown value as an error.
            DPIF_LOG_WARNING("RX %u: SD %d: dpi_session_analyze returned error or unknown code %d. Dropping packet.",
                             ctx->lcore_id,
                             s->libdpif_internal_sd,
                             analyze_result);
            rte_atomic64_inc(&ctx->dropped_pkts);
            // In case of an analysis error, we drop the packet within libdpif
            // and do not send any verdict to VPP, letting VPP handle it (e.g., timeout).
            break;
        }

        rte_pktmbuf_free(m);  // Free the mbuf after processing.
    }
}

/**
 * @brief Fills a dpif_flow_info structure from a VPP session destroy message.
 * 
 * @param sess_hdr Pointer to the vpp_dpi_session_hdr_t from the VPP message.
 * @param flow_info Pointer to the dpif_flow_info struct to be populated.
 * @return 0 on success, -EINVAL on failure.
 */
int dpif_flow_info_from_vpp_session_hdr(const vpp_dpi_session_hdr_t *sess_hdr, struct dpif_flow_info *flow_info) {
    if (!sess_hdr || !flow_info) {
        return -EINVAL;
    }

    memset(flow_info, 0, sizeof(struct dpif_flow_info));

    flow_info->proto = sess_hdr->tuple.proto;

    if (sess_hdr->tuple.family == VPP_AF_IP4) {
        flow_info->address_family = AF_INET;

        flow_info->src_ip_u.ipv4_src_ip = rte_bswap32(sess_hdr->tuple.ip4_addr[0]);
        flow_info->dst_ip_u.ipv4_dst_ip = rte_bswap32(sess_hdr->tuple.ip4_addr[1]);
        flow_info->src_port = sess_hdr->tuple.port[0];
        flow_info->dst_port = sess_hdr->tuple.port[1];
    } else if (sess_hdr->tuple.family == VPP_AF_IP6) {
        flow_info->address_family = AF_INET6;

        uint8_t src_ip6_from_vpp[16];
        uint8_t dst_ip6_from_vpp[16];
        memcpy(src_ip6_from_vpp, &sess_hdr->tuple.ip6_addr[0], 16);
        memcpy(dst_ip6_from_vpp, &sess_hdr->tuple.ip6_addr[16], 16);

        const uint32_t *src_ip6_in = (const uint32_t *) src_ip6_from_vpp;
        uint32_t *src_ip6_out = (uint32_t *) flow_info->src_ip_u.ipv6_src_ip;
        src_ip6_out[0] = rte_bswap32(src_ip6_in[0]);
        src_ip6_out[1] = rte_bswap32(src_ip6_in[1]);
        src_ip6_out[2] = rte_bswap32(src_ip6_in[2]);
        src_ip6_out[3] = rte_bswap32(src_ip6_in[3]);

        const uint32_t *dst_ip6_in = (const uint32_t *) dst_ip6_from_vpp;
        uint32_t *dst_ip6_out = (uint32_t *) flow_info->dst_ip_u.ipv6_dst_ip;
        dst_ip6_out[0] = rte_bswap32(dst_ip6_in[0]);
        dst_ip6_out[1] = rte_bswap32(dst_ip6_in[1]);
        dst_ip6_out[2] = rte_bswap32(dst_ip6_in[2]);
        dst_ip6_out[3] = rte_bswap32(dst_ip6_in[3]);

        flow_info->src_port = sess_hdr->tuple.port[0];
        flow_info->dst_port = sess_hdr->tuple.port[1];
    } else {
        return -EINVAL;  // Unsupported address family
    }

    return 0;
}

/**
 * @brief Decapsulates a VPP Netlink encapsulated packet.
 *
 * Parses the outer Ethernet/IP/UDP headers and the Netlink message to extract
 * VPP metadata and the inner packet. Adjusts the mbuf to point to the inner packet.
 * @param ctx Pointer to the RX thread context.
 * @param m Pointer to the mbuf containing the VPP encapsulated packet.
 *             This structure must be allocated by the caller.
 * @return struct rte_mbuf* Pointer to the mbuf now containing the inner packet, or NULL on failure (mbuf is freed on failure).
 */
struct rte_mbuf *dpif_decap_vpp_head(dpif_rx_context_t *ctx, struct rte_mbuf *m) {
    if (!m)
        return NULL;

    dp_metadata_t *meta = DPIF_MBUF_METADATA(m);

    dpif_msg_info_t msg_info;
    memset(&msg_info, 0, sizeof(dpif_msg_info_t));
    memset(meta, 0, sizeof(dp_metadata_t));

    if (dpif_msg_parse(rte_pktmbuf_mtod(m, uint8_t *), rte_pktmbuf_data_len(m), &msg_info) < 0) {
        DPIF_LOG_WARNING("dpif_decap_vpp_head: Failed to parse VPP packet headers with dpif_pkt_parse.");
        rte_pktmbuf_free(m);
        return NULL;
    }

    if (msg_info.msgtype == DPIF_MSG_SESSION_DESTORY) {
        vpp_dpi_session_hdr_t *sess_hdr = (vpp_dpi_session_hdr_t *) (msg_info.next_h);
        struct dpif_flow_info flow_to_find;
        if (dpif_flow_info_from_vpp_session_hdr(sess_hdr, &flow_to_find) == 0) {
            dpi_flow_key_t flow_key;
            memset(&flow_key, 0, sizeof(dpi_flow_key_t));
            canonicalize_flow_key(&flow_to_find, &flow_key);
            dpif_session_t *s = NULL;
            if (rte_hash_lookup_data(ctx->session_table, &flow_key, (void **) &s) >= 0) {
                if (s) {
                    DPIF_LOG_DEBUG(
                        "RX %u: Destroying session SD %d based on VPP request.", ctx->lcore_id, s->libdpif_internal_sd);
                    dpif_session_delete(ctx, s);
                }
            } else {
                DPIF_LOG_DEBUG("RX %u: VPP requested to destroy a session that was not found.", ctx->lcore_id);
#if 0
                // 1. Dump the key we used for lookup
                char key_dump_str[256];
                int offset = 0;
                offset += sprintf(key_dump_str + offset, "  Lookup Key: ");
                for (size_t k = 0; k < sizeof(dpi_flow_key_t); ++k) {
                    offset += sprintf(key_dump_str + offset, "%02x ", ((uint8_t*)&flow_key)[k]);
                }
                DPIF_LOG_WARNING("%s", key_dump_str);

                // 2. Iterate and dump ALL keys currently in the hash table
                DPIF_LOG_WARNING("  --- Dumping all %d keys in hash table ---", rte_hash_count(ctx->session_table));
                const void *next_key_ptr;
                void *next_data_ptr;
                uint32_t iter = 0;
                while (rte_hash_iterate(ctx->session_table, &next_key_ptr, &next_data_ptr, &iter) >= 0) {
                    const dpi_flow_key_t *stored_key = (const dpi_flow_key_t *)next_key_ptr;
                    offset = 0;
                    offset += sprintf(key_dump_str + offset, "  Stored Key: ");
                    for (size_t k = 0; k < sizeof(dpi_flow_key_t); ++k) {
                        offset += sprintf(key_dump_str + offset, "%02x ", ((uint8_t*)stored_key)[k]);
                    }
                    DPIF_LOG_WARNING("%s", key_dump_str);
                }
                DPIF_LOG_WARNING("  --- End of key dump ---");
#endif
            }
        } else {
            DPIF_LOG_WARNING("RX %u: Could not parse 5-tuple from VPP session destroy message.", ctx->lcore_id);
        }
        rte_pktmbuf_free(m);
        return NULL;
    }

    if (msg_info.smac)
        rte_ether_addr_copy((const struct rte_ether_addr *) msg_info.smac, &meta->smac);
    if (msg_info.dmac)
        rte_ether_addr_copy((const struct rte_ether_addr *) msg_info.dmac, &meta->dmac);
    meta->sip = msg_info.sip;
    meta->dip = msg_info.dip;
    meta->sport = msg_info.sport;
    meta->dport = msg_info.dport;
    private_hdr_get(msg_info.private_h, &meta->priv_data);

    uint16_t inner_pkt_len;
    uint8_t *inner_pkt_data_start;

    dpif_packet_hdr_get(
        msg_info.next_h, &meta->in_if_index, &meta->out_if_index, &inner_pkt_len, &inner_pkt_data_start);

    if (inner_pkt_len == 0) {
        DPIF_LOG_WARNING("dpif_decap_vpp_head: payload is empty.");
        rte_pktmbuf_free(m);
        return NULL;
    }

    // --- Change mbuf to include inner packet only ---
    uintptr_t mbuf_start_addr = (uintptr_t) rte_pktmbuf_mtod(m, uint8_t *);
    uintptr_t inner_pkt_start_addr_abs = (uintptr_t) inner_pkt_data_start;
    uint16_t offset_to_inner_pkt = inner_pkt_start_addr_abs - mbuf_start_addr;

    if (rte_pktmbuf_adj(m, offset_to_inner_pkt) == NULL) {
        DPIF_LOG_ERROR("dpif_decap_vpp_head: rte_pktmbuf_adj failed. Offset: %u", offset_to_inner_pkt);
        rte_pktmbuf_free(m);
        return NULL;
    }

    uint16_t current_data_len_after_adj = rte_pktmbuf_data_len(m);
    if (current_data_len_after_adj < inner_pkt_len) {
        DPIF_LOG_ERROR("dpif_decap_vpp_head: Mbuf data length (%u) < inner packet length (%u) after adj.",
                       current_data_len_after_adj,
                       inner_pkt_len);
        rte_pktmbuf_free(m);
        return NULL;
    }

    uint16_t trailing_data_to_trim = current_data_len_after_adj - inner_pkt_len;
    if (trailing_data_to_trim > 0) {
        if (rte_pktmbuf_trim(m, trailing_data_to_trim) != 0) {
            DPIF_LOG_ERROR("dpif_decap_vpp_head: rte_pktmbuf_trim failed. Trim_len: %u", trailing_data_to_trim);
            rte_pktmbuf_free(m);
            return NULL;
        }
    }

    if (rte_pktmbuf_data_len(m) != inner_pkt_len) {
        DPIF_LOG_ERROR("dpif_decap_vpp_head: Final mbuf data_len (%u) != inner_pkt_len (%u).",
                       rte_pktmbuf_data_len(m),
                       inner_pkt_len);
        rte_pktmbuf_free(m);
        return NULL;
    }

    DPIF_LOG_DEBUG("Decap: Successfully extracted inner packet. Length: %u", inner_pkt_len);

    return m;
}

/**
 * @brief Main loop for an RX thread.
 *
 * Polls for packets (either from memif or simulated), processes them, and handles timers/completion queues.
 * @param arg Pointer to the dpif_rx_context_t for this thread.
 * @return 0 on normal exit, negative on error.
 */
int dpif_rx_thread_main(void *arg) {
    dpif_rx_context_t *ctx = (dpif_rx_context_t *) arg;
    struct rte_mbuf *rx_bufs[RX_BURST_SIZE];
    uint16_t nb_rx;
    uint16_t i_pkt_loop;  // Renamed to avoid conflict with loop variable in dpif_rx_process_completion_queue
    uint64_t current_tsc, last_misc_tsc = 0;
    const uint64_t misc_interval_cycles = (rte_get_timer_hz() + US_PER_S - 1) / US_PER_S * MISC_CHECK_INTERVAL_US;
    uint64_t last_stats_update_tsc = 0;
    // Update stats approximately once per second
    const uint64_t stats_update_interval_cycles = rte_get_timer_hz();

    if (ctx->registered_callbacks && ctx->registered_callbacks->dpi_rx_thread_init) {
        int init_ret = ctx->registered_callbacks->dpi_rx_thread_init(ctx->lcore_id, &ctx->rx_thread_data);
        if (init_ret != 0) {
            DPIF_LOG_ERROR("RX thread %u: NSA thread initialization failed: %d", ctx->lcore_id, init_ret);
            return init_ret;
        }
        DPIF_LOG_INFO("RX thread %u: NSA thread-specific data initialized", ctx->lcore_id);
    }

#if !SIMULATE_PACKETS  // Log this only if we are expecting real packets
    DPIF_LOG_INFO("RX thread started on core %u for Memif queue %u.", ctx->lcore_id, ctx->memif_queue_id);
    if (ctx->memif_port_id == RTE_MAX_ETHPORTS) {
        DPIF_LOG_ERROR("RX thread %u: Memif port not initialized and not in simulation mode!", ctx->lcore_id);
        return -1;
    }
#else  // SIMULATE_PACKETS is defined
    DPIF_LOG_INFO("RX thread %u: SIMULATION Mode Active!!!", ctx->lcore_id);
#endif

    DPIF_LOG_INFO("RX thread %u entering main loop.", ctx->lcore_id);

    while (!(*ctx->quit_signal)) {
        nb_rx = 0;

#if SIMULATE_PACKETS
        if (ctx->pcap_replay_request) {
            nb_rx = dpif_sim_pcap_replay_burst(ctx, rx_bufs, RX_BURST_SIZE);
        } else {
            nb_rx = dpif_sim_generate_burst(ctx, rx_bufs, RX_BURST_SIZE);
        }

        if (nb_rx == 0 && !(*ctx->quit_signal)) {
            rte_delay_us_block(5);
        }

#else  // Normal memif operation (SIMULATE_PACKETS == 0)
        if (ctx->memif_port_id != RTE_MAX_ETHPORTS) {
            nb_rx = rte_eth_rx_burst(ctx->memif_port_id, ctx->memif_queue_id, rx_bufs, RX_BURST_SIZE);
        }
#endif

        if (nb_rx > 0) {
            rte_atomic64_add(&ctx->rx_pkts, nb_rx);
            for (i_pkt_loop = 0; i_pkt_loop < nb_rx; ++i_pkt_loop) {
                struct rte_mbuf *original_m = rx_bufs[i_pkt_loop];
                struct rte_mbuf *m_to_process = original_m;
                uint64_t t_bench_start;

                //dpif_dump_dp_encapsulated_packet(original_m, "RX Path Input");
#if !SIMULATE_PACKETS
                // --- VPP Decapsulation Path (Normal Operation) ---
                uint64_t t_decap_start = rte_rdtsc();
                m_to_process = dpif_decap_vpp_head(ctx, m_to_process);
                rte_atomic64_add(&ctx->decap_cycles, rte_rdtsc() - t_decap_start);
                if (!m_to_process) {
                    //DPIF_LOG_DEBUG("RX %u: Packet dropped within decap_vpp_head.", ctx->lcore_id);
                    //rte_atomic64_inc(&ctx->dropped_pkts);
                    continue;
                }
#endif
                // Check if benchmarking is active and call the benchmark function
                if (g_dpif_ctx && g_dpif_ctx->benchmark_mode != DPIF_BENCHMARK_MODE_DEFAULT) {
                    t_bench_start = rte_rdtsc();
                    // Pass the decap'd mbuf and metadata to the benchmark function
                    dpif_benchmark_process_packet(ctx, m_to_process);
                    rte_atomic64_add(&ctx->benchmark_process_cycles, rte_rdtsc() - t_bench_start);
                    // The benchmark function is responsible for freeing the mbuf
                    continue;
                }

                struct dpif_flow_info flow_info;
                if (dpif_parse_packet_get_flow(m_to_process, &flow_info) == 0) {
                    dpif_process_parsed_packet(ctx, m_to_process, &flow_info);
                } else {
                    // Parsing of the (potentially inner) packet failed.
                    DPIF_LOG_DEBUG("RX %u: Inner packet parse failed (after potential Netlink handling). Dropping.",
                                   ctx->lcore_id);
                    rte_pktmbuf_free(m_to_process);  // Free the mbuf that failed parsing
                    rte_atomic64_inc(&ctx->dropped_pkts);
                }
            }
        }

        current_tsc = rte_rdtsc();
        if (unlikely(current_tsc - last_stats_update_tsc > stats_update_interval_cycles)) {
            if (g_dpif_stats_snapshot && ctx->lcore_id < DPIF_MAX_MONITORED_CORES) {
                dpif_core_stats_snapshot_t *my_slot = &g_dpif_stats_snapshot->core_stats[ctx->lcore_id];

                // Atomically consistent snapshot of this core's stats
                my_slot->rx_pkts = rte_atomic64_read(&ctx->rx_pkts);
                my_slot->dropped_pkts = rte_atomic64_read(&ctx->dropped_pkts);
                my_slot->tasks_offloaded = rte_atomic64_read(&ctx->tasks_offloaded);
                my_slot->completion_msgs_processed = rte_atomic64_read(&ctx->completion_msgs_processed);
                my_slot->sessions = ctx->session_table ? rte_hash_count(ctx->session_table) : 0;
                my_slot->session_timeouts = rte_atomic64_read(&ctx->session_timeouts);
                my_slot->updated_sessions_by_timer = rte_atomic64_read(&ctx->sessions_updated_by_timer);

                if (ctx->completion_ring) {
                    my_slot->ring_count = rte_ring_count(ctx->completion_ring);
                    my_slot->ring_capacity = rte_ring_get_capacity(ctx->completion_ring);
                }

                // Ensure all writes are visible before updating the type string (as a final flag)
                rte_smp_wmb();

                my_slot->lcore_id = ctx->lcore_id;
                strncpy(my_slot->type, "RX", sizeof(my_slot->type) - 1);
            }
            last_stats_update_tsc = current_tsc;
        }

        if (current_tsc - last_misc_tsc > misc_interval_cycles) {
            dpif_rx_process_completion_queue(ctx);
            rte_timer_manage();
            last_misc_tsc = current_tsc;
        }

        if (ctx->registered_callbacks && ctx->registered_callbacks->dpi_periodic_maintenance) {
            // Pass the application's own thread-local data to the callback
            ctx->registered_callbacks->dpi_periodic_maintenance(ctx->rx_thread_data);
        }
    }

    // Call NSA RX thread cleanup callback
    if (ctx->registered_callbacks && ctx->registered_callbacks->dpi_rx_thread_cleanup) {
        int cleanup_ret = ctx->registered_callbacks->dpi_rx_thread_cleanup(ctx->lcore_id, ctx->rx_thread_data);
        if (cleanup_ret != 0) {
            DPIF_LOG_WARNING("RX thread %u: NSA thread cleanup failed: %d", ctx->lcore_id, cleanup_ret);
        }
        DPIF_LOG_INFO("RX thread %u: NSA thread-specific data cleaned up", ctx->lcore_id);
    }

    DPIF_LOG_INFO("RX thread %u exiting main loop.", ctx->lcore_id);
    return 0;
}

/**
 * @brief Get thread-specific data for the current RX thread
 * 
 * This function provides a clean API for applications to access their 
 * thread-specific data without directly accessing DPIF internal structures.
 */
void *dpif_get_current_rx_thread_data(void) {
    uint32_t current_lcore = rte_lcore_id();

    // Access global context safely within DPIF module
    if (!g_dpif_ctx || !g_dpif_ctx->rx_contexts) {
        DPIF_LOG_ERROR("DPIF global context not available for lcore %u", current_lcore);
        return NULL;
    }

    dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[current_lcore];
    return rx_ctx->rx_thread_data;
}