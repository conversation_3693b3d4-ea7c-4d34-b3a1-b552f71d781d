/**
 * @file dpif_session_optimization.h
 * @brief DPIF Session Management Optimizations
 * 
 * This file contains optimized session management functions and configurations
 * for high-performance packet processing in enterprise network security applications.
 * 
 * Key optimizations:
 * - Cache-aligned session structure layout
 * - Lazy timer updates to reduce CPU overhead
 * - Prefetching for better cache performance
 * - Optimized hash table configuration
 * - NUMA-aware memory allocation
 * 
 * <AUTHOR> Optimization Team
 * @date 2025
 */

#ifndef DPIF_SESSION_OPTIMIZATION_H
#define DPIF_SESSION_OPTIMIZATION_H

#include <stdint.h>
#include <stdbool.h>
#include <rte_hash.h>
#include <rte_timer.h>
#include <rte_prefetch.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * SESSION OPTIMIZATION CONSTANTS
 * ======================================================================== */

/** @brief Lazy timer update interval (packets) */
#define DPIF_LAZY_TIMER_PACKET_INTERVAL     100

/** @brief Lazy timer update interval (TSC cycles for 10ms) */
#define DPIF_LAZY_TIMER_TIME_INTERVAL_CYCLES (rte_get_timer_hz() / 100)

/** @brief Hash function initialization value for better distribution */
#define DPIF_HASH_INIT_VAL                   0x12345678

/** @brief Prefetch distance for session data */
#define DPIF_SESSION_PREFETCH_DISTANCE       2

/* ========================================================================
 * SESSION PERFORMANCE METRICS
 * ======================================================================== */

/** @brief Session performance metrics */
typedef struct {
    uint64_t total_lookups;         /**< Total session lookups */
    uint64_t cache_hits;            /**< Hash table cache hits */
    uint64_t cache_misses;          /**< Hash table cache misses */
    uint64_t timer_updates_saved;   /**< Timer updates saved by lazy updates */
    uint64_t prefetch_hits;         /**< Successful prefetch operations */
    uint64_t avg_lookup_cycles;     /**< Average lookup time in cycles */
    uint64_t avg_create_cycles;     /**< Average session creation time */
    uint64_t hash_collisions;       /**< Hash table collisions */
    uint32_t active_sessions;       /**< Current active sessions */
    uint32_t session_table_load;    /**< Session table load factor (%) */
} dpif_session_perf_metrics_t;

/* ========================================================================
 * OPTIMIZED SESSION MANAGEMENT FUNCTIONS
 * ======================================================================== */

/**
 * @brief Optimized session lookup with prefetching
 * 
 * This function performs session lookup with optimized prefetching
 * and lazy timer updates to reduce CPU overhead.
 * 
 * @param ctx RX thread context
 * @param flow_key Flow key for lookup
 * @param session Output session pointer
 * @return 0 on success, negative on error
 */
static inline int dpif_session_lookup_optimized(dpif_rx_context_t *ctx,
                                               const dpi_flow_key_t *flow_key,
                                               dpif_session_t **session) {
    // Prefetch hash table data
    rte_prefetch0(ctx->session_table);
    
    int ret = rte_hash_lookup_data(ctx->session_table, flow_key, (void **)session);
    if (ret >= 0) {
        // Prefetch session data immediately
        rte_prefetch0(*session);
        rte_prefetch0((char *)(*session) + RTE_CACHE_LINE_SIZE);
    }
    
    return ret;
}

/**
 * @brief Lazy timer update for session
 * 
 * Updates session timer only when necessary to reduce CPU overhead.
 * 
 * @param session Session to update
 * @param current_tsc Current TSC timestamp
 * @param timeout_ticks Timeout value in TSC ticks
 * @param lcore_id Logical core ID
 * @return true if timer was updated, false otherwise
 */
static inline bool dpif_session_lazy_timer_update(dpif_session_t *session,
                                                 uint64_t current_tsc,
                                                 uint64_t timeout_ticks,
                                                 uint32_t lcore_id) {
    session->packet_count++;
    
    if (session->packet_count % DPIF_LAZY_TIMER_PACKET_INTERVAL == 0 ||
        (current_tsc - session->last_timer_update_tsc) > DPIF_LAZY_TIMER_TIME_INTERVAL_CYCLES) {
        
        rte_timer_reset(&session->timeout_timer,
                       timeout_ticks,
                       SINGLE,
                       lcore_id,
                       dpif_session_timeout_callback,
                       session);
        
        session->last_timer_update_tsc = current_tsc;
        return true;
    }
    
    return false;
}

/**
 * @brief Batch session timer updates
 * 
 * Updates multiple session timers in a batch to improve cache efficiency.
 * 
 * @param sessions Array of sessions to update
 * @param count Number of sessions
 * @param timeout_ticks Timeout value in TSC ticks
 * @param lcore_id Logical core ID
 * @return Number of timers actually updated
 */
int dpif_session_batch_timer_update(dpif_session_t **sessions,
                                   uint32_t count,
                                   uint64_t timeout_ticks,
                                   uint32_t lcore_id);

/**
 * @brief Optimized hash table configuration
 * 
 * Creates an optimized hash table configuration for session management.
 * 
 * @param name Hash table name
 * @param entries Number of hash table entries
 * @param socket_id NUMA socket ID
 * @return Hash table parameters structure
 */
struct rte_hash_parameters dpif_create_optimized_hash_params(const char *name,
                                                           uint32_t entries,
                                                           int socket_id);

/* ========================================================================
 * SESSION CACHE OPTIMIZATION
 * ======================================================================== */

/**
 * @brief Session cache configuration
 */
typedef struct {
    uint32_t cache_size;            /**< Cache size per core */
    uint32_t prefetch_distance;     /**< Prefetch distance */
    bool enable_numa_awareness;     /**< Enable NUMA-aware allocation */
    bool enable_huge_pages;         /**< Use huge pages for session storage */
} dpif_session_cache_config_t;

/**
 * @brief Apply session cache optimizations
 * 
 * @param config Cache configuration
 * @return 0 on success, negative on error
 */
int dpif_apply_session_cache_optimizations(const dpif_session_cache_config_t *config);

/* ========================================================================
 * SESSION PERFORMANCE MONITORING
 * ======================================================================== */

/**
 * @brief Get session performance metrics
 * 
 * @param ctx RX thread context
 * @param metrics Output metrics structure
 * @return 0 on success, negative on error
 */
int dpif_get_session_performance_metrics(dpif_rx_context_t *ctx,
                                        dpif_session_perf_metrics_t *metrics);

/**
 * @brief Reset session performance counters
 * 
 * @param ctx RX thread context
 * @return 0 on success, negative on error
 */
int dpif_reset_session_performance_counters(dpif_rx_context_t *ctx);

/**
 * @brief Auto-tune session management parameters
 * 
 * Automatically adjusts session management parameters based on
 * current workload and performance metrics.
 * 
 * @param ctx RX thread context
 * @return 0 on success, negative on error
 */
int dpif_auto_tune_session_management(dpif_rx_context_t *ctx);

/* ========================================================================
 * SESSION MEMORY LAYOUT OPTIMIZATION
 * ======================================================================== */

/**
 * @brief Optimize session memory layout for cache efficiency
 * 
 * Reorganizes session data structures to minimize cache misses
 * and improve memory access patterns.
 * 
 * @return 0 on success, negative on error
 */
int dpif_optimize_session_memory_layout(void);

/**
 * @brief Validate session structure alignment
 * 
 * Checks that session structures are properly aligned for
 * optimal cache performance.
 * 
 * @return true if properly aligned, false otherwise
 */
bool dpif_validate_session_alignment(void);

/* ========================================================================
 * DEFAULT OPTIMIZED CONFIGURATIONS
 * ======================================================================== */

/** @brief Default optimized session cache configuration */
static const dpif_session_cache_config_t DPIF_DEFAULT_SESSION_CACHE_CONFIG = {
    .cache_size = 1024,
    .prefetch_distance = 2,
    .enable_numa_awareness = true,
    .enable_huge_pages = true
};

#ifdef __cplusplus
}
#endif

#endif /* DPIF_SESSION_OPTIMIZATION_H */
