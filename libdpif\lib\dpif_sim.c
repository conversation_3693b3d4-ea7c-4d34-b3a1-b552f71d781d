#include <arpa/inet.h>
#include <pcap.h>  // Include pcap header
#include <rte_atomic.h>
#include <rte_cycles.h>
#include <rte_ether.h>
#include <rte_ip.h>
#include <rte_mbuf.h>
#include <rte_mempool.h>
#include <rte_random.h>
#include <rte_tcp.h>
#include <rte_udp.h>
#include <time.h>

#include "dpif_private.h"
#include "dpif_sim.h"

// --- Static variables ---
static pcap_t *g_pcap_handles[RTE_MAX_LCORE] = {NULL};
static uint32_t default_sim_pkt_variant_counter = 0;

/**
 * @brief Initializes the packet simulation module.
 */
void dpif_sim_init(void) {
    rte_srand(rte_rdtsc());
    DPIF_LOG_INFO("DPIF Simulation Module Initialized (Raw Packet Mode).");
}

/**
 * @brief Constructs a simple dummy IPv4/TCP or IPv4/UDP raw packet.
 */
static struct rte_mbuf *construct_dummy_ipv4_packet(struct rte_mempool *mbuf_pool,
                                                    uint8_t protocol,
                                                    uint32_t src_ip,
                                                    uint16_t src_port,
                                                    uint32_t dst_ip,
                                                    uint16_t dst_port,
                                                    uint16_t payload_len) {
    struct rte_mbuf *m;
    struct rte_ether_hdr *eth_h;
    struct rte_ipv4_hdr *ip_h;
    unsigned char *l4_hdr_ptr;
    uint16_t pkt_len;
    uint16_t l4_hdr_len;

    m = rte_pktmbuf_alloc(mbuf_pool);
    if (m == NULL) {
        return NULL;
    }

    // Ethernet header
    eth_h = rte_pktmbuf_mtod(m, struct rte_ether_hdr *);
    rte_ether_addr_copy(&(const struct rte_ether_addr) {.addr_bytes = {0x00, 0xAA, 0xBB, 0xCC, 0xDD, 0x01}},
                        &eth_h->src_addr);
    rte_ether_addr_copy(&(const struct rte_ether_addr) {.addr_bytes = {0x00, 0xAA, 0xBB, 0xCC, 0xDD, 0x02}},
                        &eth_h->dst_addr);
    eth_h->ether_type = rte_cpu_to_be_16(RTE_ETHER_TYPE_IPV4);

    // IPv4 header
    ip_h = (struct rte_ipv4_hdr *) (eth_h + 1);
    ip_h->version_ihl = RTE_IPV4_VHL_DEF;
    ip_h->type_of_service = 0;
    l4_hdr_len = (protocol == IPPROTO_TCP ? sizeof(struct rte_tcp_hdr) : sizeof(struct rte_udp_hdr));
    ip_h->total_length = rte_cpu_to_be_16(sizeof(struct rte_ipv4_hdr) + l4_hdr_len + payload_len);
    ip_h->packet_id = rte_cpu_to_be_16(rte_rand() & 0xFFFF);
    ip_h->fragment_offset = 0;
    ip_h->time_to_live = 64;
    ip_h->next_proto_id = protocol;
    ip_h->hdr_checksum = 0;
    ip_h->src_addr = rte_cpu_to_be_32(src_ip);
    ip_h->dst_addr = rte_cpu_to_be_32(dst_ip);
    ip_h->hdr_checksum = rte_ipv4_cksum(ip_h);

    pkt_len = sizeof(struct rte_ether_hdr) + sizeof(struct rte_ipv4_hdr) + l4_hdr_len + payload_len;
    m->data_len = pkt_len;
    m->pkt_len = pkt_len;
    m->l2_len = sizeof(struct rte_ether_hdr);
    m->l3_len = sizeof(struct rte_ipv4_hdr);

    l4_hdr_ptr = (unsigned char *) (ip_h + 1);

    if (protocol == IPPROTO_TCP) {
        struct rte_tcp_hdr *tcp_h = (struct rte_tcp_hdr *) l4_hdr_ptr;
        tcp_h->src_port = rte_cpu_to_be_16(src_port);
        tcp_h->dst_port = rte_cpu_to_be_16(dst_port);
        tcp_h->sent_seq = rte_cpu_to_be_32(rte_rand());
        tcp_h->recv_ack = rte_cpu_to_be_32(rte_rand());
        tcp_h->data_off = (sizeof(struct rte_tcp_hdr) / 4) << 4;
        tcp_h->tcp_flags = RTE_TCP_SYN_FLAG;
        tcp_h->rx_win = rte_cpu_to_be_16(65535);
        tcp_h->cksum = 0;
        tcp_h->tcp_urp = 0;
        l4_hdr_ptr = (unsigned char *) (tcp_h + 1);
        m->l4_len = sizeof(struct rte_tcp_hdr);
    } else if (protocol == IPPROTO_UDP) {
        struct rte_udp_hdr *udp_h = (struct rte_udp_hdr *) l4_hdr_ptr;
        udp_h->src_port = rte_cpu_to_be_16(src_port);
        udp_h->dst_port = rte_cpu_to_be_16(dst_port);
        udp_h->dgram_len = rte_cpu_to_be_16(sizeof(struct rte_udp_hdr) + payload_len);
        udp_h->dgram_cksum = 0;
        l4_hdr_ptr = (unsigned char *) (udp_h + 1);
        m->l4_len = sizeof(struct rte_udp_hdr);
    } else {
        DPIF_LOG_ERROR("construct_dummy_packet: Unsupported L4 protocol %u", protocol);
        rte_pktmbuf_free(m);
        return NULL;
    }

    for (uint16_t k = 0; k < payload_len; ++k) {
        l4_hdr_ptr[k] = (char) (k & 0xFF);
    }

    return m;
}

/**
 * @brief Constructs a dummy IPv6 packet (TCP or UDP).
 */
static struct rte_mbuf *construct_dummy_ipv6_packet(struct rte_mempool *mbuf_pool,
                                                    uint8_t l4_protocol,
                                                    const uint8_t src_ip6[16],
                                                    uint16_t src_port,
                                                    const uint8_t dst_ip6[16],
                                                    uint16_t dst_port,
                                                    uint16_t payload_len) {
    struct rte_mbuf *m;
    struct rte_ether_hdr *eth_h;
    struct rte_ipv6_hdr *ip6_h;
    unsigned char *l4_hdr_ptr;
    uint16_t pkt_len;
    uint16_t l4_hdr_len;

    m = rte_pktmbuf_alloc(mbuf_pool);
    if (m == NULL) {
        return NULL;
    }

    eth_h = rte_pktmbuf_mtod(m, struct rte_ether_hdr *);
    rte_ether_addr_copy(&(const struct rte_ether_addr) {.addr_bytes = {0x00, 0xAA, 0xBB, 0xCC, 0xDD, 0x03}},
                        &eth_h->src_addr);
    rte_ether_addr_copy(&(const struct rte_ether_addr) {.addr_bytes = {0x00, 0xAA, 0xBB, 0xCC, 0xDD, 0x04}},
                        &eth_h->dst_addr);
    eth_h->ether_type = rte_cpu_to_be_16(RTE_ETHER_TYPE_IPV6);

    ip6_h = (struct rte_ipv6_hdr *) (eth_h + 1);
    ip6_h->vtc_flow = rte_cpu_to_be_32(6 << 28);  // Version 6
    l4_hdr_len = (l4_protocol == IPPROTO_TCP ? sizeof(struct rte_tcp_hdr) : sizeof(struct rte_udp_hdr));
    ip6_h->payload_len = rte_cpu_to_be_16(l4_hdr_len + payload_len);
    ip6_h->proto = l4_protocol;
    ip6_h->hop_limits = 64;

#if RTE_VERSION >= RTE_VERSION_NUM(24, 11, 0, 0)
        memcpy(ip6_h->src_addr.a, src_ip6, 16);
        memcpy(ip6_h->dst_addr.a, dst_ip6, 16);
#else
        memcpy(ip6_h->src_addr, src_ip6, 16);
        memcpy(ip6_h->dst_addr, dst_ip6, 16);
#endif

    pkt_len = sizeof(struct rte_ether_hdr) + sizeof(struct rte_ipv6_hdr) + l4_hdr_len + payload_len;
    m->data_len = pkt_len;
    m->pkt_len = pkt_len;
    m->l2_len = sizeof(struct rte_ether_hdr);
    m->l3_len = sizeof(struct rte_ipv6_hdr);

    l4_hdr_ptr = (unsigned char *) (ip6_h + 1);

    if (l4_protocol == IPPROTO_TCP) {
        struct rte_tcp_hdr *tcp_h = (struct rte_tcp_hdr *) l4_hdr_ptr;
        tcp_h->src_port = rte_cpu_to_be_16(src_port);
        tcp_h->dst_port = rte_cpu_to_be_16(dst_port);
        tcp_h->sent_seq = rte_cpu_to_be_32(rte_rand());
        tcp_h->recv_ack = rte_cpu_to_be_32(rte_rand());
        tcp_h->data_off = (sizeof(struct rte_tcp_hdr) / 4) << 4;
        tcp_h->tcp_flags = RTE_TCP_SYN_FLAG;
        tcp_h->rx_win = rte_cpu_to_be_16(65535);
        tcp_h->cksum = 0;
        tcp_h->tcp_urp = 0;
        l4_hdr_ptr = (unsigned char *) (tcp_h + 1);
        m->l4_len = sizeof(struct rte_tcp_hdr);
    } else if (l4_protocol == IPPROTO_UDP) {
        struct rte_udp_hdr *udp_h = (struct rte_udp_hdr *) l4_hdr_ptr;
        udp_h->src_port = rte_cpu_to_be_16(src_port);
        udp_h->dst_port = rte_cpu_to_be_16(dst_port);
        udp_h->dgram_len = rte_cpu_to_be_16(sizeof(struct rte_udp_hdr) + payload_len);
        udp_h->dgram_cksum = 0;
        l4_hdr_ptr = (unsigned char *) (udp_h + 1);
        m->l4_len = sizeof(struct rte_udp_hdr);
    } else {
        rte_pktmbuf_free(m);
        return NULL;
    }

    for (uint16_t k = 0; k < payload_len; ++k) {
        l4_hdr_ptr[k] = (char) (k & 0xFF);
    }

    return m;
}

/**
 * @brief Generates a burst of synthetic raw packets based on CLI parameters or defaults.
 * This version randomizes between IPv4 and IPv6 for default generation.
 */
uint16_t dpif_sim_generate_burst(dpif_rx_context_t *rx_ctx, struct rte_mbuf **rx_bufs, uint16_t max_burst_size) {
    if (rte_atomic32_read(&rx_ctx->inject_packet_count) <= 0) {
        return 0;
    }

    uint16_t nb_generated = 0;
    int32_t packets_to_inject = rte_atomic32_read(&rx_ctx->inject_packet_count);
    int32_t cap_for_burst = RTE_MIN(packets_to_inject, (int32_t) max_burst_size);

    for (int k = 0; k < cap_for_burst; ++k) {
        struct rte_mbuf *m = NULL;
        if (rx_ctx->inject_params_set) {
            // --- Generate with Custom Parameters from CLI ---
            if (rx_ctx->inject_address_family == AF_INET6) {
                m = construct_dummy_ipv6_packet(rx_ctx->mbuf_pool,
                                                rx_ctx->inject_proto,
                                                rx_ctx->inject_src_ip_u.ipv6_src_ip,
                                                rx_ctx->inject_src_port,
                                                rx_ctx->inject_dst_ip_u.ipv6_dst_ip,
                                                rx_ctx->inject_dst_port,
                                                rx_ctx->inject_payload_len);
            } else {  // Default to IPv4
                m = construct_dummy_ipv4_packet(rx_ctx->mbuf_pool,
                                                rx_ctx->inject_proto,
                                                rx_ctx->inject_src_ip_u.ipv4_src_ip,
                                                rx_ctx->inject_src_port,
                                                rx_ctx->inject_dst_ip_u.ipv4_dst_ip,
                                                rx_ctx->inject_dst_port,
                                                rx_ctx->inject_payload_len);
            }
        } else {
            default_sim_pkt_variant_counter++;
            // Randomly choose between IPv4 and IPv6 (e.g., 70% IPv4, 30% IPv6)
            if (rte_rand() % 10 < 7) {
                // Generate IPv4
                uint8_t r_proto = (default_sim_pkt_variant_counter % 2 == 0) ? IPPROTO_TCP : IPPROTO_UDP;
                uint32_t r_sip = 0xC0A80001 + (default_sim_pkt_variant_counter % 254);
                uint32_t r_dip = 0x0A000001 + ((default_sim_pkt_variant_counter / 100) % 254);
                m = construct_dummy_ipv4_packet(rx_ctx->mbuf_pool, r_proto, r_sip, 10000, r_dip, 80, 128);
            } else {
                // Generate IPv6
                uint8_t r_proto = (default_sim_pkt_variant_counter % 2 == 0) ? IPPROTO_TCP : IPPROTO_UDP;
                uint8_t s_ip6[16] = {
                    0x20, 0x01, 0x0d, 0xb8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, (uint8_t) (rte_rand() % 255)};
                uint8_t d_ip6[16] = {
                    0x20, 0x01, 0x0d, 0xb8, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, (uint8_t) (rte_rand() % 255)};
                uint16_t r_sport = 10000 + (rte_rand() % 50000);
                uint16_t r_dport = (rte_rand() % 2 == 0) ? 443 : 8080;
                m = construct_dummy_ipv6_packet(rx_ctx->mbuf_pool, r_proto, s_ip6, r_sport, d_ip6, r_dport, 128);
            }
        }

        if (m) {
            rx_bufs[nb_generated++] = m;
        } else {
            DPIF_LOG_INFO("Lcore %u: Failed to construct dummy packet.", rx_ctx->lcore_id);
            break;
        }
    }

    if (nb_generated > 0) {
        rte_atomic32_sub(&rx_ctx->inject_packet_count, nb_generated);
    }
    return nb_generated;
}

/**
 * @brief Injects a burst of packets by replaying a PCAP file.
 *
 * This function reads packets from a pre-opened PCAP file and injects them
 * into the processing pipeline as rte_mbufs. It handles the lifecycle of the
 * pcap handle, opening it upon request and closing it when the replay is
 * complete (either by exhausting the packet count or reaching the end of the file).
 *
 * @param rx_ctx The context of the RX thread, containing the replay request,
 *               file path, packet count, and mbuf pool.
 * @param rx_bufs An output array to store pointers to the generated mbufs.
 * @param max_burst_size The maximum number of packets to generate in this call.
 * @return uint16_t The number of packets actually generated.
 */
uint16_t dpif_sim_pcap_replay_burst(dpif_rx_context_t *rx_ctx, struct rte_mbuf **rx_bufs, uint16_t max_burst_size) {
    uint32_t lcore_id = rx_ctx->lcore_id;

    // --- Part 1: Open PCAP file on first request ---
    if (g_pcap_handles[lcore_id] == NULL && rx_ctx->pcap_replay_request) {
        char errbuf[PCAP_ERRBUF_SIZE];
        g_pcap_handles[lcore_id] = pcap_open_offline(rx_ctx->pcap_filepath, errbuf);
        if (g_pcap_handles[lcore_id] == NULL) {
            DPIF_LOG_ERROR("Lcore %u: Could not open pcap file '%s': %s", lcore_id, rx_ctx->pcap_filepath, errbuf);
            rx_ctx->pcap_replay_request = 0; // Abort the request
            return 0;
        }
        DPIF_LOG_DEBUG("Lcore %u: Started replaying PCAP file '%s'", lcore_id, rx_ctx->pcap_filepath);
    }

    // --- Part 2: Early exit if no active replay ---
    // This condition handles cases where there's no active handle or the request was cancelled.
    if (g_pcap_handles[lcore_id] == NULL || !rx_ctx->pcap_replay_request) {
        // If there's a lingering handle but the request is off, clean up.
        if (g_pcap_handles[lcore_id] != NULL) {
            DPIF_LOG_INFO("Lcore %u: Replay request cancelled, closing PCAP handle.", lcore_id);
            pcap_close(g_pcap_handles[lcore_id]);
            g_pcap_handles[lcore_id] = NULL;
        }
        return 0;
    }

    // --- Part 3: Main packet generation loop ---
    pcap_t *pcap_handle = g_pcap_handles[lcore_id];
    uint16_t nb_generated = 0;

    while (nb_generated < max_burst_size) {
        // Check if we have injected the requested number of packets.
        if (rte_atomic32_read(&rx_ctx->pcap_packets_to_inject) <= 0) {
            break; // Stop generating if count is met.
        }

        struct pcap_pkthdr *header;
        const u_char *pkt_data;
        int ret = pcap_next_ex(pcap_handle, &header, &pkt_data);

        if (ret == 1) { // Successfully read a packet
            struct rte_mbuf *m = rte_pktmbuf_alloc(rx_ctx->mbuf_pool);
            if (unlikely(m == NULL)) {
                DPIF_LOG_ERROR("Lcore %u: Mbuf allocation failed during PCAP replay.", lcore_id);
                break; // Stop burst on allocation failure
            }
            char *mbuf_data = rte_pktmbuf_append(m, header->caplen);
            if (unlikely(mbuf_data == NULL)) {
                DPIF_LOG_ERROR("Lcore %u: rte_pktmbuf_append failed for %u bytes.", lcore_id, header->caplen);
                rte_pktmbuf_free(m);
                break; // Stop burst on append failure
            }
            rte_memcpy(mbuf_data, pkt_data, header->caplen);
            rx_bufs[nb_generated++] = m;
            rte_atomic32_dec(&rx_ctx->pcap_packets_to_inject);
        } else { // ret is not 1, indicating EOF or an error.
            // This is a terminal condition for the PCAP file.
            DPIF_LOG_DEBUG("Lcore %u: Reached end of PCAP file or encountered read error (pcap_next_ex ret: %d). Cleaning up.", lcore_id, ret);
            pcap_close(pcap_handle);
            g_pcap_handles[lcore_id] = NULL;
            rx_ctx->pcap_replay_request = 0; // Reset the request state.
            rte_atomic32_set(&rx_ctx->pcap_packets_to_inject, 0); // Clear remaining count.
            return nb_generated; // Return any packets generated in this final burst.
        }
    }

    // --- Part 4: Cleanup after successfully injecting the requested count ---
    // This block is now only reached if the while loop exits because the injection count
    // reached zero, but the PCAP file might still have more packets.
    if (rte_atomic32_read(&rx_ctx->pcap_packets_to_inject) <= 0 && g_pcap_handles[lcore_id] != NULL) {
        DPIF_LOG_DEBUG("Lcore %u: Finished injecting requested number of packets. Closing PCAP handle.", lcore_id);
        pcap_close(pcap_handle);
        g_pcap_handles[lcore_id] = NULL;
        rx_ctx->pcap_replay_request = 0; // Mark the replay as complete.
    }

    return nb_generated;
}