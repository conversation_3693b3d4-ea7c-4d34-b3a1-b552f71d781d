// File: dpif_sim.h
#ifndef DPIF_SIM_H
#define DPIF_SIM_H

#include "dpif_private.h"  // For dpif_rx_context_t structure
#include <rte_mbuf.h>

// How many dummy packets to generate per "burst" if injection count is high
#define DUMMY_PKT_BATCH_SIZE 4  // Not directly used by CLI inject, but can be a guide
// Interval between generating bursts (microseconds) - not currently used in CLI inject logic directly
#define DUMMY_PKT_INTERVAL_US 1000

/**
 * @brief (Optional) Initializes the packet simulation module.
 *
 * Can be used to seed random number generators or initialize static counters
 * if they are not self-initializing (e.g. not using rte_atomic for all).
 */
void dpif_sim_init(void);

/**
 * @brief Generates a burst of simulated packets, potentially with VPP encapsulation.
 *
 * This function is called by the RX thread when SIMULATE_PACKETS is enabled.
 * It checks CLI injection commands via the rx_ctx and generates packets.
 *
 * @param rx_ctx The RX thread context, providing mbuf_pool and injection parameters.
 * It's passed to access rx_ctx->inject_packet_count,
 * rx_ctx->inject_params_set, rx_ctx->inject_*, and rx_ctx->mbuf_pool.
 * @param rx_bufs Output array to store pointers to generated mbufs.
 * @param max_burst_size Maximum number of packets to generate in this call.
 * @return uint16_t The number of packets actually generated and placed in rx_bufs.
 */
uint16_t dpif_sim_generate_burst(dpif_rx_context_t *rx_ctx, struct rte_mbuf **rx_bufs, uint16_t max_burst_size);

/**
 * @brief Replays packets from a PCAP file.
 *
 * This function reads packets from a PCAP file specified in the rx_ctx,
 * allocates mbufs, copies the packet data, and returns them in a burst.
 * It is called by the RX thread when a PCAP replay is requested.
 *
 * @param rx_ctx The RX thread context, containing PCAP file path and state.
 * @param bufs Output array to store pointers to generated mbufs.
 * @param max_burst_size Maximum number of packets to generate in this call.
 * @return The number of packets actually read and placed in bufs.
 */
uint16_t dpif_sim_pcap_replay_burst(dpif_rx_context_t *rx_ctx, struct rte_mbuf **bufs, uint16_t max_burst_size);

#endif  // DPIF_SIM_H