#include "dpif_stats.h"
#include "dpif_private.h"  // For accessing dpif_rx_context_t's sessions_updated_by_timer
#include <rte_memcpy.h>
#include <rte_ring.h>  // For rte_ring_count
#include <string.h>    // For memset

extern dpif_global_context_t *g_dpif_ctx;
extern dpif_stats_snapshot_t *g_dpif_stats_snapshot;

/**
 * @brief Retrieves global DPIF statistics.
 *
 * @param stats Pointer to a dpif_global_stats_t structure to be filled.
 * @return 0 on success, -EINVAL if g_dpif_ctx or stats is NULL.
 */
int dpif_get_global_stats(dpif_global_stats_t *stats) {
    if (!g_dpif_ctx || !stats)
        return -EINVAL;
    memset(stats, 0, sizeof(dpif_global_stats_t));
    uint32_t lcore_id;

    // Accumulate stats from RX cores
    if (g_dpif_ctx->rx_contexts) {
        for (uint32_t i = 0; i < g_dpif_ctx->num_rx_cores; ++i) {
            lcore_id = g_dpif_ctx->rx_lcore_map[i];
            // Check if this lcore_id is valid and its context is initialized
            if (lcore_id < RTE_MAX_LCORE && g_dpif_ctx->rx_contexts[lcore_id].lcore_id == lcore_id &&
                g_dpif_ctx->rx_contexts[lcore_id].session_table != NULL) {  // Ensure context is for an active RX core

                dpif_rx_context_t *rx_ctx = &g_dpif_ctx->rx_contexts[lcore_id];
                stats->total_rx_pkts += rte_atomic64_read(&rx_ctx->rx_pkts);
                stats->total_dropped_pkts += rte_atomic64_read(&rx_ctx->dropped_pkts);
                stats->total_tasks_offloaded += rte_atomic64_read(&rx_ctx->tasks_offloaded);
                stats->total_completion_msgs += rte_atomic64_read(&rx_ctx->completion_msgs_processed);
                stats->total_sessions_count += rte_hash_count(rx_ctx->session_table);
                stats->total_session_timeouts += rte_atomic64_read(&rx_ctx->session_timeouts);
                stats->total_updated_sessions_by_timer += rte_atomic64_read(&rx_ctx->sessions_updated_by_timer);
                stats->total_decap_cycles += rte_atomic64_read(&rx_ctx->decap_cycles);
                stats->total_benchmark_process_cycles += rte_atomic64_read(&rx_ctx->benchmark_process_cycles);
            }
        }
    }

    // Accumulate stats from Worker cores
    if (g_dpif_ctx->worker_contexts) {
        for (uint32_t i = 0; i < g_dpif_ctx->num_worker_cores; ++i) {
            lcore_id = g_dpif_ctx->worker_lcore_map[i];
            // Check if this lcore_id is valid and its context is initialized
            if (lcore_id < RTE_MAX_LCORE && g_dpif_ctx->worker_contexts[lcore_id].lcore_id == lcore_id &&
                g_dpif_ctx->worker_contexts[lcore_id].task_rings !=
                    NULL) {  // Ensure context is for an active Worker core

                dpif_worker_context_t *wkr_ctx = &g_dpif_ctx->worker_contexts[lcore_id];
                stats->total_processed_tasks += rte_atomic64_read(&wkr_ctx->processed_tasks);
                // Worker cores no longer handle periodic session updates via their own timer,
                // so total_updated_sessions_by_timer is solely from RX cores.
            }
        }
    }
    return 0;
}

/**
 * @brief Retrieves statistics for a specific Lcore.
 *
 * @param lcore_id The ID of the Lcore.
 * @param stats Pointer to a dpif_core_stats_t structure to be filled.
 * @return 0 on success, -EINVAL if g_dpif_ctx, stats is NULL, or lcore_id is invalid.
 */
int dpif_get_core_stats(uint32_t lcore_id, dpif_core_stats_t *stats) {
    if (!g_dpif_ctx || !stats || lcore_id >= RTE_MAX_LCORE)
        return -EINVAL;
    memset(stats, 0, sizeof(dpif_core_stats_t));
    stats->lcore_id = lcore_id;

    // Check if it's an RX core used by DPIF
    if (g_dpif_ctx->rx_contexts &&
        g_dpif_ctx->lcore_to_rx_idx_map[lcore_id] != UINT32_MAX &&  // Check if lcore is mapped as an RX core
        g_dpif_ctx->rx_contexts[lcore_id].lcore_id == lcore_id &&   // Double check context integrity
        g_dpif_ctx->rx_contexts[lcore_id].session_table != NULL) {
        dpif_rx_context_t *ctx = &g_dpif_ctx->rx_contexts[lcore_id];
        strncpy(stats->type, "RX", sizeof(stats->type) - 1);
        stats->type[sizeof(stats->type) - 1] = '\0';  // Ensure null termination

        stats->rx_pkts = rte_atomic64_read(&ctx->rx_pkts);
        stats->dropped_pkts = rte_atomic64_read(&ctx->dropped_pkts);
        stats->tasks_offloaded = rte_atomic64_read(&ctx->tasks_offloaded);
        stats->completion_msgs_processed = rte_atomic64_read(&ctx->completion_msgs_processed);
        stats->session_lookup_cycles = rte_atomic64_read(&ctx->session_lookup_cycles);
        stats->analyze_cycles = rte_atomic64_read(&ctx->analyze_cycles);
        stats->analyzed_pkts = rte_atomic64_read(&ctx->analyzed_pkts);
        stats->offload_cycles = rte_atomic64_read(&ctx->offload_cycles);
        stats->decap_cycles = rte_atomic64_read(&ctx->decap_cycles);
        stats->benchmark_process_cycles = rte_atomic64_read(&ctx->benchmark_process_cycles);
        stats->sessions = rte_hash_count(ctx->session_table);
        stats->session_timeouts = rte_atomic64_read(&ctx->session_timeouts);
        stats->updated_sessions_by_timer = rte_atomic64_read(&ctx->sessions_updated_by_timer);  // Get from RX context

        if (ctx->completion_ring)
            stats->completion_ring_count = rte_ring_count(ctx->completion_ring);
        else
            stats->completion_ring_count = 0;
        return 0;
    }
    // Check if it's a Worker core used by DPIF
    else if (g_dpif_ctx->worker_contexts &&
             g_dpif_ctx->lcore_to_worker_idx_map[lcore_id] !=
                 UINT32_MAX &&  // Check if lcore is mapped as a Worker core
             g_dpif_ctx->worker_contexts[lcore_id].lcore_id == lcore_id &&
             g_dpif_ctx->worker_contexts[lcore_id].task_rings != NULL) {
        dpif_worker_context_t *ctx = &g_dpif_ctx->worker_contexts[lcore_id];
        strncpy(stats->type, "Worker", sizeof(stats->type) - 1);
        stats->type[sizeof(stats->type) - 1] = '\0';

        stats->processed_tasks = rte_atomic64_read(&ctx->processed_tasks);
        stats->updated_sessions_by_timer = 0;  // Workers no longer perform this task
        stats->task_processing_cycles = rte_atomic64_read(&ctx->task_processing_cycles);
        stats->notify_cycles = rte_atomic64_read(&ctx->notify_cycles);

        if (ctx->task_rings && ctx->num_task_rings > 0 && ctx->task_rings[0]) {  // Assuming one ring per worker
            stats->task_ring_count = rte_ring_count(ctx->task_rings[0]);
        } else {
            stats->task_ring_count = 0;
        }
        return 0;
    }
    // If not an active RX or Worker core for DPIF (could be main lcore or unused)
    else {
        // Check if it's the main lcore (usually not a worker lcore)
        if (lcore_id == rte_get_main_lcore()) {
            strncpy(stats->type, "Main", sizeof(stats->type) - 1);
        } else {
            strncpy(stats->type, "Idle/Other", sizeof(stats->type) - 1);
        }
        stats->type[sizeof(stats->type) - 1] = '\0';
        // For Idle or Main lcore, most DPIF-specific stats will be 0 or not applicable.
        return 0;  // Return 0 to indicate stats struct is filled (with type "Idle/Other" or "Main")
                   // Or return -ENOENT if we only want to report for DPIF data plane cores.
                   // Current CLI logic expects 0 for success.
    }
}

/**
 * @brief Safely retrieves a snapshot of the current DPIF statistics.
 */
int dpif_get_stats_snapshot(dpif_stats_snapshot_t *snapshot) {
    if (unlikely(snapshot == NULL)) {
        return -EINVAL;
    }
    if (unlikely(g_dpif_stats_snapshot == NULL)) {
        // DPIF not yet initialized or already cleaned up
        return -EFAULT;
    }

    // A memory barrier ensures that we see the latest writes from other cores.
    rte_smp_rmb();

    // Fast copy of the entire snapshot structure.
    rte_memcpy(snapshot, g_dpif_stats_snapshot, sizeof(dpif_stats_snapshot_t));

    return 0;
}