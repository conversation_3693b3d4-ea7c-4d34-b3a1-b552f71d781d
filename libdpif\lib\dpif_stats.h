#ifndef DPIF_STATS_H
#define DPIF_STATS_H

#include "dpif_private.h"
#include <stdint.h>

/**
 * @brief Global DPIF statistics.
 */
typedef struct {
    uint64_t total_rx_pkts;                    ///< Total packets received by all RX cores.
    uint64_t total_dropped_pkts;               ///< Total packets dropped by all RX cores.
    uint64_t total_tasks_offloaded;            ///< Total tasks offloaded to worker threads.
    uint64_t total_completion_msgs;            ///< Total completion messages processed by RX cores.
    uint64_t total_processed_tasks;            ///< Total tasks processed by worker threads.
    uint64_t total_updated_sessions_by_timer;  ///< Total sessions updated by periodic RX timers.
    uint64_t total_sessions_count;             ///< Current total number of active sessions.
    uint64_t total_session_timeouts;           ///< Total number of sessions timed out.
    uint64_t total_decap_cycles;               ///< Total cycles for VPP header decapsulation.
    uint64_t total_benchmark_process_cycles;   ///< Total cycles for benchmark processing.
} dpif_global_stats_t;

/**
 * @brief Per-Lcore DPIF statistics.
 */
typedef struct {
    uint32_t lcore_id;                   ///< The Lcore ID.
    char type[16];                       ///< Type of core (e.g., "RX", "Worker", "Main", "Idle/Other").
    uint64_t rx_pkts;                    ///< Packets received (if RX core).
    uint64_t dropped_pkts;               ///< Packets dropped (if RX core).
    uint64_t tasks_offloaded;            ///< Tasks offloaded (if RX core).
    uint64_t completion_msgs_processed;  ///< Completion messages processed (if RX core).
    uint64_t session_lookup_cycles;      ///< Cycles for session lookup (if RX core).
    uint64_t analyze_cycles;             ///< Cycles for session analysis (if RX core).
    uint64_t analyzed_pkts;
    uint64_t offload_cycles;             ///< Cycles for offloading tasks (if RX core).
    uint64_t decap_cycles;               ///< Cycles for VPP header decapsulation (if RX core).
    uint64_t benchmark_process_cycles;   ///< Cycles for benchmark processing (if RX core).
    uint64_t sessions;                   ///< Current active sessions on this core (if RX core).
    uint64_t session_timeouts;           ///< Sessions timed out on this core (if RX core).
    uint64_t processed_tasks;            ///< Tasks processed (if Worker core).
    uint64_t updated_sessions_by_timer;  ///< Sessions updated by periodic timer (if RX core).
    uint64_t task_processing_cycles;     ///< Cycles for task processing (if Worker core).
    uint64_t notify_cycles;              ///< Cycles for notify completion (if Worker core).
    uint32_t task_ring_count;            ///< Current count in task ring (if Worker core).
    uint32_t completion_ring_count;      ///< Current count in completion ring (if RX core).
} dpif_core_stats_t;

// --- Internal Function Prototypes (for stats and session info, used by CLI) ---

/// @see dpif_stats.c
int dpif_get_global_stats(dpif_global_stats_t *stats);
/// @see dpif_stats.c
int dpif_get_core_stats(uint32_t lcore_id, dpif_core_stats_t *stats);

// The function prototype for getting all session info.
/// @see dpif_session.c
int dpif_get_all_session_info(dpif_session_info_t *sessions_info_arr, uint32_t arr_max_size, uint32_t *filled_count);

#endif  // DPIF_STATS_H
