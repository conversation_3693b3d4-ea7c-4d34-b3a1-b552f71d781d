#include "dpif_private.h"
#include <rte_timer.h>

#define WORKER_TASK_BURST_SIZE 32

/**
 * @brief Notifies the owning RX thread that a task for a session is complete.
 *
 * @param ctx The worker context.
 * @param session The session for which the task is complete.
 * @return int 0 on success, negative errno on failure (e.g., ring full).
 */
int dpif_worker_notify_completion(dpif_worker_context_t *ctx, dpif_session_t *session) {
    if (!session)
        return -EINVAL;
    uint32_t target_rx_lcore = session->owner_rx_lcore;
    struct rte_ring *comp_ring = dpif_get_completion_ring(target_rx_lcore);
    if (!comp_ring) {
        DPIF_LOG_ERROR("Worker %u: No completion ring for RX %u.", ctx->lcore_id, target_rx_lcore);
        return -ENOENT;
    }
    int ret = rte_ring_mp_enqueue(comp_ring, (void *) session);
    if (ret != 0) {
        DPIF_LOG_WARNING("Worker %u: Failed completion enqueue sd %d to RX %u: %s",
                         ctx->lcore_id,
                         session->libdpif_internal_sd,
                         target_rx_lcore,
                         rte_strerror(-ret));
        return ret;
    }
    return 0;
}

/**
 * @brief Main loop for a worker thread.
 *
 * Dequeues work items from its task ring(s), processes them using the application's callback, and notifies completion.
 * @param arg Pointer to the dpif_worker_context_t for this thread.
 * @return 0 on normal exit.
 */
int dpif_worker_thread_main(void *arg) {
    dpif_worker_context_t *ctx = (dpif_worker_context_t *) arg;
    struct dpi_work *work_bufs[WORKER_TASK_BURST_SIZE];
    uint16_t nb_dequeued, i, j;
    uint64_t current_tsc, next_timer_tsc = 0;
    uint64_t last_stats_update_tsc = 0;
    const uint64_t stats_update_interval_cycles = rte_get_timer_hz();

    DPIF_LOG_INFO("Worker thread started on core %u (ID %u).", ctx->lcore_id, ctx->worker_id);

    while (!(*ctx->quit_signal)) {
        int work_done = 0;
        for (i = 0; i < ctx->num_task_rings; ++i) {
            nb_dequeued =
                rte_ring_sc_dequeue_burst(ctx->task_rings[i], (void **) work_bufs, WORKER_TASK_BURST_SIZE, NULL);
            if (nb_dequeued > 0) {
                work_done = 1;
                uint64_t t0 = rte_rdtsc();
                rte_atomic64_add(&ctx->processed_tasks, nb_dequeued);
                for (j = 0; j < nb_dequeued; ++j) {
                    struct dpi_work *w = work_bufs[j];
                    dpif_session_t *s = w->session_ptr;
                    if (!s) {
                        rte_mempool_put(ctx->work_pool, w);
                        continue;
                    }
                    ctx->registered_callbacks->dpi_session_work(s->libdpif_internal_sd, w);
                    uint64_t t1 = rte_rdtsc();
                    dpif_worker_notify_completion(ctx, s);
                    rte_atomic64_add(&ctx->notify_cycles, rte_rdtsc() - t1);
                    rte_mempool_put(ctx->work_pool, w);
                }
                rte_atomic64_add(&ctx->task_processing_cycles, rte_rdtsc() - t0);
            }
        }

        current_tsc = rte_rdtsc();
        if (unlikely(current_tsc - last_stats_update_tsc > stats_update_interval_cycles)) {
             if (g_dpif_stats_snapshot && ctx->lcore_id < DPIF_MAX_MONITORED_CORES) {
                dpif_core_stats_snapshot_t *my_slot = &g_dpif_stats_snapshot->core_stats[ctx->lcore_id];

                my_slot->processed_tasks = rte_atomic64_read(&ctx->processed_tasks);
                
                if (ctx->task_rings && ctx->num_task_rings > 0 && ctx->task_rings[0]) {
                    my_slot->ring_count = rte_ring_count(ctx->task_rings[0]);
                    my_slot->ring_capacity = rte_ring_get_capacity(ctx->task_rings[0]);
                }

                rte_smp_wmb();

                my_slot->lcore_id = ctx->lcore_id;
                strncpy(my_slot->type, "Worker", sizeof(my_slot->type) - 1);
             }
             last_stats_update_tsc = current_tsc;
        }

        if (current_tsc >= next_timer_tsc) {
            //rte_timer_manage();                    // Handle session timeouts etc.
            next_timer_tsc = current_tsc + (rte_get_timer_hz() / 2000);  // Check roughly every 0.5ms
            work_done = 1;
        }

        if (!work_done) {
            rte_delay_us_block(1);
        }
    }
    DPIF_LOG_INFO("Worker thread on core %u exiting.", ctx->lcore_id);
    return 0;
}
