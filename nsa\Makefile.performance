# NSA Performance Optimization Makefile
# Builds the enhanced performance monitoring and optimization tools

CC = gcc
CFLAGS = -Wall -Wextra -O3 -g -std=c99
CFLAGS += -march=native -mtune=native
CFLAGS += -DNSA_ENABLE_PROFILING
CFLAGS += -D_GNU_SOURCE

# DPDK flags (adjust paths as needed)
DPDK_CFLAGS = -I/usr/local/include/dpdk
DPDK_LDFLAGS = -L/usr/local/lib -ldpdk -lpthread -lnuma -ldl

# Include paths
INCLUDES = -I./src -I../libdpif/lib

# Libraries
LIBS = -lm -lrt -lpthread -lncurses

# Source files for performance tools
PERF_SRCS = src/nsa_performance_analyzer.c \
           src/nsa_benchmark_suite.c \
           src/nsa_profiler.c \
           src/nsa_pml_optimization.c \
           src/nsa_enhanced_monitor.c

# Object files
PERF_OBJS = $(PERF_SRCS:.c=.o)

# Targets
TARGETS = nsa_benchmark nsa_enhanced_monitor nsa_profiler_tool

.PHONY: all clean performance install

all: $(TARGETS)

# Performance analyzer library
libnsa_performance.a: $(PERF_OBJS)
	@echo "Creating performance library..."
	ar rcs $@ $^

# Benchmark tool
nsa_benchmark: src/nsa_benchmark_main.c libnsa_performance.a
	@echo "Building benchmark tool..."
	$(CC) $(CFLAGS) $(INCLUDES) $(DPDK_CFLAGS) -o $@ $< -L. -lnsa_performance $(DPDK_LDFLAGS) $(LIBS)

# Enhanced monitor
nsa_enhanced_monitor: src/nsa_enhanced_monitor.c libnsa_performance.a
	@echo "Building enhanced monitor..."
	$(CC) $(CFLAGS) $(INCLUDES) $(DPDK_CFLAGS) -DSTANDALONE_MONITOR -o $@ $< -L. -lnsa_performance $(DPDK_LDFLAGS) $(LIBS)

# Profiler tool
nsa_profiler_tool: src/nsa_profiler_main.c libnsa_performance.a
	@echo "Building profiler tool..."
	$(CC) $(CFLAGS) $(INCLUDES) $(DPDK_CFLAGS) -o $@ $< -L. -lnsa_performance $(DPDK_LDFLAGS) $(LIBS)

# Object file compilation
%.o: %.c
	@echo "Compiling $<..."
	$(CC) $(CFLAGS) $(INCLUDES) $(DPDK_CFLAGS) -c $< -o $@

# Performance test suite
performance: all
	@echo "Running performance test suite..."
	./scripts/performance_test_suite.sh --test all --duration 30

# Stress test
stress: all
	@echo "Running stress test..."
	./nsa_benchmark --type stress --duration 60 --target-pps 200000

# Benchmark suite
benchmark: all
	@echo "Running benchmark suite..."
	./nsa_benchmark --type all --duration 60

# Profile analysis
profile: all
	@echo "Running profiling analysis..."
	./nsa_profiler_tool --duration 30 --mode hybrid

# Install targets
install: all
	@echo "Installing performance tools..."
	mkdir -p /usr/local/bin
	cp $(TARGETS) /usr/local/bin/
	mkdir -p /usr/local/lib
	cp libnsa_performance.a /usr/local/lib/
	mkdir -p /usr/local/include/nsa
	cp src/nsa_performance_analyzer.h /usr/local/include/nsa/
	cp src/nsa_benchmark_suite.h /usr/local/include/nsa/
	cp src/nsa_profiler.h /usr/local/include/nsa/
	cp src/nsa_pml_optimization.h /usr/local/include/nsa/

# Clean targets
clean:
	@echo "Cleaning build artifacts..."
	rm -f $(PERF_OBJS)
	rm -f $(TARGETS)
	rm -f libnsa_performance.a
	rm -f *.log
	rm -rf performance_results/

# Development targets
debug: CFLAGS += -DDEBUG -O0
debug: all

release: CFLAGS += -DNDEBUG -O3
release: all

# Documentation
docs:
	@echo "Generating documentation..."
	doxygen Doxyfile

# Code analysis
analyze:
	@echo "Running static analysis..."
	cppcheck --enable=all --std=c99 src/

# Memory check
memcheck: all
	@echo "Running memory check..."
	valgrind --tool=memcheck --leak-check=full ./nsa_benchmark --test throughput --duration 10

# Performance profiling with perf
perf-profile: all
	@echo "Running perf profiling..."
	perf record -g ./nsa_benchmark --test throughput --duration 30
	perf report

# Help target
help:
	@echo "NSA Performance Optimization Makefile"
	@echo ""
	@echo "Targets:"
	@echo "  all          - Build all performance tools"
	@echo "  performance  - Run complete performance test suite"
	@echo "  stress       - Run stress test"
	@echo "  benchmark    - Run benchmark suite"
	@echo "  profile      - Run profiling analysis"
	@echo "  install      - Install tools to system"
	@echo "  clean        - Clean build artifacts"
	@echo "  debug        - Build debug version"
	@echo "  release      - Build optimized release version"
	@echo "  docs         - Generate documentation"
	@echo "  analyze      - Run static code analysis"
	@echo "  memcheck     - Run memory leak check"
	@echo "  perf-profile - Run performance profiling with perf"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Examples:"
	@echo "  make all                    # Build everything"
	@echo "  make performance           # Run full test suite"
	@echo "  make stress               # Run stress test"
	@echo "  make CFLAGS=-O2 release   # Custom optimization"

# Dependencies
src/nsa_performance_analyzer.o: src/nsa_performance_analyzer.h src/nsa.h
src/nsa_benchmark_suite.o: src/nsa_benchmark_suite.h src/nsa.h
src/nsa_profiler.o: src/nsa_profiler.h src/nsa.h
src/nsa_pml_optimization.o: src/nsa_pml_optimization.h src/nsa.h
src/nsa_enhanced_monitor.o: src/nsa_performance_analyzer.h src/nsa.h
