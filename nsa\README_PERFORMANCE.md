# NSA Performance Optimization Tools

This document describes the enhanced performance monitoring, analysis, and optimization tools for the NSA (Network Security Application) project.

## Overview

The performance optimization suite includes:

- **Performance Analyzer**: Real-time bottleneck detection and optimization recommendations
- **Benchmark Suite**: Comprehensive performance testing and validation
- **Profiler**: Function-level performance analysis and hot path identification
- **Enhanced Monitor**: Advanced monitoring dashboard with real-time metrics
- **PML Optimization**: Deep packet inspection engine optimizations

## Building the Performance Tools

### Prerequisites

- GCC 7.0 or later
- DPDK 20.11 or later
- ncurses development libraries
- pthread support
- NUMA libraries (optional but recommended)

### Build Commands

```bash
# Build all performance tools
make -f Makefile.performance all

# Build specific tools
make -f Makefile.performance nsa_benchmark
make -f Makefile.performance nsa_enhanced_monitor
make -f Makefile.performance nsa_profiler_tool

# Build with debug symbols
make -f Makefile.performance debug

# Build optimized release version
make -f Makefile.performance release
```

## Performance Tools Usage

### 1. NSA Benchmark Tool

The benchmark tool provides comprehensive performance testing capabilities.

#### Basic Usage

```bash
# Run throughput benchmark
./nsa_benchmark --type throughput --duration 60 --target-pps 100000

# Run latency benchmark
./nsa_benchmark --type latency --duration 60 --target-pps 50000

# Run stress test
./nsa_benchmark --type stress --duration 300 --target-pps 200000

# Run complete benchmark suite
./nsa_benchmark --type all --output benchmark_results.txt
```

#### Advanced Options

```bash
# Custom configuration
./nsa_benchmark \
    --type throughput \
    --duration 120 \
    --target-pps 500000 \
    --threads 8 \
    --output detailed_results.txt \
    --verbose

# Regression testing
./nsa_benchmark \
    --type throughput \
    --baseline baseline_results.txt \
    --duration 60
```

### 2. NSA Profiler Tool

The profiler provides detailed function-level performance analysis.

#### Basic Usage

```bash
# Sampling profiler (default)
./nsa_profiler_tool --mode sampling --duration 30

# Instrumentation profiler
./nsa_profiler_tool --mode instrumentation --duration 60

# Hybrid profiler with hot path analysis
./nsa_profiler_tool --mode hybrid --duration 30 --hot-paths --functions
```

#### Advanced Analysis

```bash
# High-frequency sampling
./nsa_profiler_tool \
    --mode sampling \
    --sample-rate 5000 \
    --duration 60 \
    --output profile_report.txt

# Function-level analysis
./nsa_profiler_tool \
    --mode instrumentation \
    --functions \
    --hot-paths \
    --verbose
```

### 3. Enhanced Monitor

The enhanced monitor provides a real-time performance dashboard.

#### Usage

```bash
# Start interactive monitor
./nsa_enhanced_monitor

# Run in daemon mode
./nsa_enhanced_monitor --daemon --output monitor.log
```

#### Monitor Features

- **Overview Tab**: System-wide performance metrics
- **Performance Tab**: Detailed latency and throughput analysis
- **Bottlenecks Tab**: Real-time bottleneck detection
- **Recommendations Tab**: Optimization suggestions
- **Trends Tab**: Performance trend analysis

### 4. Performance Test Suite Script

The automated test suite provides comprehensive testing workflows.

#### Basic Usage

```bash
# Run all tests with defaults
./scripts/performance_test_suite.sh

# Custom test configuration
./scripts/performance_test_suite.sh \
    --test throughput \
    --duration 120 \
    --pps 200000 \
    --sessions 50000

# Stress testing
./scripts/performance_test_suite.sh \
    --test stress \
    --duration 300 \
    --pps 500000

# Regression testing
./scripts/performance_test_suite.sh \
    --test regression \
    --baseline baseline_results.json
```

## Performance Optimization Features

### 1. Data Plane Optimizations

- **Increased Burst Sizes**: RX burst: 512→1024, Worker burst: 32→128
- **Memory Pool Optimization**: mbuf pool: 16K→32K, cache size: 256→1024
- **Ring Buffer Enhancement**: Ring size: 8K→32K for better burst handling
- **Queue Depth Optimization**: RX/TX queues: 2K→4K descriptors

### 2. Session Management Optimizations

- **Lazy Timer Updates**: Reduces timer operations by 90%
- **Cache-Friendly Layout**: Session structures aligned to cache lines
- **Hash Table Optimization**: 1M entries with optimized hash function
- **Prefetching**: Strategic data prefetching for better cache performance

### 3. PML Engine Optimizations

- **Early Detection**: Stop analysis after application identification
- **Safe Application Bypass**: Skip rule evaluation for known safe apps
- **Context Caching**: Cache PML analysis results
- **Analysis Limits**: Limit packets/bytes analyzed per flow

### 4. Memory and NUMA Optimizations

- **NUMA-Aware Allocation**: Memory pools bound to NUMA nodes
- **Cache Line Alignment**: Critical data structures aligned to 64-byte boundaries
- **Huge Pages Support**: Optimized memory allocation patterns

## Performance Monitoring

### Key Metrics Tracked

1. **Throughput Metrics**
   - Packets per second (PPS)
   - Bits per second (BPS)
   - Peak throughput
   - Packet loss rate

2. **Latency Metrics**
   - Average, min, max latency
   - Percentiles (P50, P95, P99, P99.9)
   - Latency jitter

3. **Resource Utilization**
   - CPU utilization per core
   - Memory utilization
   - Cache hit rates
   - NUMA efficiency

4. **Session Metrics**
   - Active sessions
   - Session setup/teardown rates
   - Session table load
   - Average session lifetime

5. **DPI Performance**
   - DPI processing time
   - Applications detected per second
   - Threats detected per second
   - Early detection rate

### Bottleneck Detection

The system automatically detects:

- **CPU Bottlenecks**: High CPU utilization (>85%)
- **Memory Bottlenecks**: High memory usage (>90%)
- **Queue Bottlenecks**: High queue utilization (>80%)
- **Latency Bottlenecks**: High processing latency (>100μs)
- **Cache Bottlenecks**: Low cache hit rates (<95%)

### Optimization Recommendations

The system provides automatic recommendations for:

- CPU affinity optimization
- Memory pool tuning
- Queue size adjustments
- DPI engine optimization
- Cache performance improvements

## Expected Performance Improvements

Based on the implemented optimizations:

| Optimization Category | Short-term | Medium-term | Long-term |
|----------------------|------------|-------------|-----------|
| **Session Management** | 25-40% | 50-70% | 80-100% |
| **DPI Processing** | 30-50% | 60-80% | 100-150% |
| **Memory Access** | 15-25% | 30-40% | 50-70% |
| **Overall Throughput** | 30-50% | 100-150% | 200-300% |

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Ensure DPDK is properly installed
   - Check that all dependencies are available
   - Verify compiler version compatibility

2. **Runtime Errors**
   - Ensure sufficient memory and huge pages
   - Check CPU isolation and affinity settings
   - Verify DPDK driver compatibility

3. **Performance Issues**
   - Check system resource availability
   - Verify NUMA configuration
   - Review CPU frequency scaling settings

### Debug Mode

Build with debug symbols for troubleshooting:

```bash
make -f Makefile.performance debug
```

Run with verbose output:

```bash
./nsa_benchmark --verbose
./nsa_profiler_tool --verbose
```

## Integration with Existing Code

The performance tools are designed to integrate seamlessly with the existing NSA codebase:

1. **Header Files**: Include the appropriate headers in your code
2. **Initialization**: Call initialization functions during startup
3. **Monitoring**: Use the performance analyzer for real-time monitoring
4. **Profiling**: Enable profiling macros for detailed analysis

### Example Integration

```c
#include "nsa_performance_analyzer.h"
#include "nsa_profiler.h"

int main() {
    // Initialize performance tools
    nsa_performance_analyzer_init();
    nsa_profiler_init(NSA_PROFILE_HYBRID, 1000);
    
    // Your application code here
    
    // Cleanup
    nsa_profiler_cleanup();
    nsa_performance_analyzer_cleanup();
    
    return 0;
}
```

## Contributing

When contributing performance improvements:

1. Run the benchmark suite before and after changes
2. Document performance impact in commit messages
3. Update this README if adding new tools or features
4. Ensure all tests pass with the new optimizations

## License

This performance optimization suite is part of the NSA project and follows the same licensing terms.
