[Unit]
Description=nsa service
After=network.target
Before=arc-mgr.service

[Service]
# PrivateNetwork means DM only access lo
# PrivateNetwork=yes
# DM has its own tmp directory, others has no access to /tmp
# PrivateTmp=yes
Environment="ASAN_OPTIONS=detect_odr_violation=0:strict_string_checks=1:detect_stack_use_after_return=1:check_initialization_order=1:strict_init_order=1:detect_leaks=1 "
Environment="UBSAN_OPTIONS=print_stacktrace=1:print_summary=1:halt_on_error=0"
# avoid access /root
InaccessibleDirectories=/root
# Sets the working directory for executed processes
WorkingDirectory=/var/log
# write data into /mnt/rw is not permitted
ReadOnlyDirectories=/mnt/rw
Type=forking
RemainAfterExit=yes
ExecStart=/usr/bin/nsad -l3
ExecStop=
Restarts=yes

[Install]
WantedBy=multi-user.target
Alias=nsa
