
## <summary>policy for nsad</summary>

########################################
## <summary>
##	Execute nsad_exec_t in the nsad domain.
## </summary>
## <param name="domain">
## <summary>
##	Domain allowed to transition.
## </summary>
## </param>
#
interface(`nsad_domtrans',`
	gen_require(`
		type nsad_t, nsad_exec_t;
	')

	corecmd_search_bin($1)
	domtrans_pattern($1, nsad_exec_t, nsad_t)
')

######################################
## <summary>
##	Execute nsad in the caller domain.
## </summary>
## <param name="domain">
##	<summary>
##	Domain allowed access.
##	</summary>
## </param>
#
interface(`nsad_exec',`
	gen_require(`
		type nsad_exec_t;
	')

	corecmd_search_bin($1)
	can_exec($1, nsad_exec_t)
')
