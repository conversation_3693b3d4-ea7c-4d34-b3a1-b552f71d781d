policy_module(nsad, 1.0.0)

########################################
#
# Declarations
#

type nsad_t;
type nsad_exec_t;
init_daemon_domain(nsad_t, nsad_exec_t)

permissive nsad_t;

########################################
#
# nsad local policy
#
allow nsad_t self:fifo_file rw_fifo_file_perms;
allow nsad_t self:unix_stream_socket create_stream_socket_perms;

domain_use_interactive_fds(nsad_t)

files_read_etc_files(nsad_t)

miscfiles_read_localization(nsad_t)
