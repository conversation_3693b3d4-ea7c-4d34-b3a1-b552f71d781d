#############################################################################
#                         _____       _ _                                   # 
#                        /  __ \     | (_)                                  # 
#                        | /  \/ __ _| |___  __                             #
#                        | |    / _` | | \ \/ /                             #
#                        | \__/\ (_| | | |>  <                              #
#                         \____/\__,_|_|_/_/\_\ inc.                        #
#                                                                           #
#############################################################################
#                                                                           #
#                       copyright 2025 by Calix, Inc.                       #
#                               Petaluma, CA                                #
#                                                                           #
#############################################################################
#
# Author: <PERSON> Li
#
# Purpose: Makefile.am for the nsa sources
#
#############################################################################

bin_PROGRAMS = nsad

nsad_SOURCES = nsa_main.c nsa_cli.c nsa_helper_thread.c nsa_cdb.c nsa_session.c nsa_monitor.c nsa_pml.c nsa_log.c

PLATFORM_FLAGS = $(if $(findstring aegis,$(YOCTO_MACHINE)),-DPLATFORM_AEGIS=1)
PLATFORM_FLAGS += $(if $(findstring smbsim,$(YOCTO_MACHINE)),-DPLATFORM_SMBSIM=1)

nsad_CFLAGS = -Werror $(warning_mask) $(SANITIZER_FLAGS) -g $(COV_CCFLAGS_nsa) -pthread -I$(top_srcdir)/include $(PLATFORM_FLAGS)

PML_LIBRARY = $(if $(findstring aegis,$(YOCTO_MACHINE)),$(top_srcdir)/lib/arm/libpml.a, \
                $(if $(findstring smbsim,$(YOCTO_MACHINE)),$(top_srcdir)/lib/x86/libpml.a))

nsad_LDFLAGS = $(SANITIZER_FLAGS) -lrt -lrdtsc -lcalixdb -lexa_lib -ldaemonlib -lcmd_parser -lvalidate -lsl_files -lipc \
    -ldpif \
    -lrte_eal \
    -lrte_mempool \
    -lrte_ring \
    $(PML_LIBRARY)

bin_PROGRAMS += nsatop
nsatop_SOURCES = nsa_monitor_client.c
nsatop_CFLAGS = -Werror $(warning_mask) $(SANITIZER_FLAGS) -g -pthread -I$(top_srcdir)
nsatop_LDFLAGS = $(SANITIZER_FLAGS) -lncurses -lpthread -lrt

techlogdir = $(bindir)/scripts/techlog
techlog_SCRIPTS = nsad-techlog

# Code Coverage Support
if RM_OPT
override CFLAGS := $(shell echo $(CFLAGS) | sed "s@-O.@@g" )
endif
