/**
 * @file nsa_benchmark_main.c
 * @brief NSA Benchmark Tool Main Program
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <getopt.h>
#include <unistd.h>

#include "nsa_benchmark_suite.h"
#include "nsa_performance_analyzer.h"
#include "nsa_logging.h"

/* Command line options */
static struct option long_options[] = {
    {"help",        no_argument,       0, 'h'},
    {"type",        required_argument, 0, 't'},
    {"duration",    required_argument, 0, 'd'},
    {"target-pps",  required_argument, 0, 'p'},
    {"threads",     required_argument, 0, 'T'},
    {"output",      required_argument, 0, 'o'},
    {"verbose",     no_argument,       0, 'v'},
    {"profile",     no_argument,       0, 'P'},
    {"baseline",    required_argument, 0, 'b'},
    {0, 0, 0, 0}
};

static void print_usage(const char *program_name) {
    printf("NSA Benchmark Tool\n");
    printf("Usage: %s [OPTIONS]\n\n", program_name);
    printf("Options:\n");
    printf("  -h, --help              Show this help message\n");
    printf("  -t, --type TYPE         Benchmark type: throughput, latency, stress, all\n");
    printf("  -d, --duration SEC      Test duration in seconds (default: 60)\n");
    printf("  -p, --target-pps PPS    Target packets per second (default: 100000)\n");
    printf("  -T, --threads NUM       Number of worker threads (default: 4)\n");
    printf("  -o, --output FILE       Output report file\n");
    printf("  -v, --verbose           Verbose output\n");
    printf("  -P, --profile           Enable profiling\n");
    printf("  -b, --baseline FILE     Baseline results for regression test\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s --type throughput --duration 120 --target-pps 200000\n", program_name);
    printf("  %s --type stress --duration 300 --threads 8\n", program_name);
    printf("  %s --type all --output benchmark_results.txt\n", program_name);
}

int main(int argc, char *argv[]) {
    int opt;
    int option_index = 0;
    
    /* Default parameters */
    char *benchmark_type = "throughput";
    uint32_t duration = 60;
    uint64_t target_pps = 100000;
    uint32_t num_threads = 4;
    char *output_file = NULL;
    char *baseline_file = NULL;
    bool verbose = false;
    bool enable_profiling = false;
    
    /* Parse command line arguments */
    while ((opt = getopt_long(argc, argv, "ht:d:p:T:o:vPb:", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'h':
                print_usage(argv[0]);
                return 0;
                
            case 't':
                benchmark_type = optarg;
                break;
                
            case 'd':
                duration = atoi(optarg);
                if (duration == 0) {
                    fprintf(stderr, "Invalid duration: %s\n", optarg);
                    return 1;
                }
                break;
                
            case 'p':
                target_pps = atoll(optarg);
                if (target_pps == 0) {
                    fprintf(stderr, "Invalid target PPS: %s\n", optarg);
                    return 1;
                }
                break;
                
            case 'T':
                num_threads = atoi(optarg);
                if (num_threads == 0 || num_threads > 64) {
                    fprintf(stderr, "Invalid number of threads: %s\n", optarg);
                    return 1;
                }
                break;
                
            case 'o':
                output_file = optarg;
                break;
                
            case 'v':
                verbose = true;
                break;
                
            case 'P':
                enable_profiling = true;
                break;
                
            case 'b':
                baseline_file = optarg;
                break;
                
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    printf("NSA Benchmark Tool Starting...\n");
    printf("Type: %s, Duration: %us, Target PPS: %lu, Threads: %u\n",
           benchmark_type, duration, target_pps, num_threads);
    
    /* Initialize benchmark suite */
    if (nsa_benchmark_suite_init() != 0) {
        fprintf(stderr, "Failed to initialize benchmark suite\n");
        return 1;
    }
    
    /* Initialize performance analyzer if profiling enabled */
    if (enable_profiling) {
        if (nsa_performance_analyzer_init() != 0) {
            fprintf(stderr, "Failed to initialize performance analyzer\n");
            nsa_benchmark_suite_cleanup();
            return 1;
        }
    }
    
    int result = 0;
    
    if (strcmp(benchmark_type, "all") == 0) {
        /* Run comprehensive benchmark suite */
        nsa_benchmark_results_t results[10];
        uint32_t actual_results = 0;
        
        printf("Running comprehensive benchmark suite...\n");
        
        if (nsa_run_benchmark_suite(results, 10, &actual_results) == 0) {
            printf("Benchmark suite completed successfully!\n");
            printf("Executed %u benchmarks\n", actual_results);
            
            /* Generate summary report */
            for (uint32_t i = 0; i < actual_results; i++) {
                printf("\nBenchmark %u: %s\n", i + 1, results[i].config.name);
                printf("  Throughput: %lu PPS\n", results[i].throughput.packets_per_second);
                printf("  Latency: %lu ns avg\n", results[i].latency.avg_ns);
                printf("  Score: %.1f/100\n", results[i].overall_score);
                printf("  Status: %s\n", results[i].passed_validation ? "PASS" : "FAIL");
            }
        } else {
            fprintf(stderr, "Benchmark suite failed\n");
            result = 1;
        }
        
    } else if (strcmp(benchmark_type, "stress") == 0) {
        /* Run stress test */
        nsa_benchmark_results_t results;
        
        printf("Running stress test...\n");
        
        if (nsa_run_stress_test(target_pps, duration, &results) == 0) {
            printf("Stress test completed successfully!\n");
            printf("Results:\n");
            printf("  Throughput: %lu PPS\n", results.throughput.packets_per_second);
            printf("  Latency: %lu ns avg\n", results.latency.avg_ns);
            printf("  Peak PPS: %lu\n", results.throughput.peak_pps);
            printf("  Dropped packets: %lu\n", results.throughput.dropped_packets);
            printf("  Score: %.1f/100\n", results.overall_score);
        } else {
            fprintf(stderr, "Stress test failed\n");
            result = 1;
        }
        
    } else {
        /* Run single benchmark */
        nsa_benchmark_config_t config = {0};
        nsa_benchmark_results_t results;
        
        /* Set up configuration */
        if (strcmp(benchmark_type, "throughput") == 0) {
            config.type = NSA_BENCHMARK_THROUGHPUT;
            strcpy(config.name, "Throughput Benchmark");
            strcpy(config.description, "Maximum throughput test");
        } else if (strcmp(benchmark_type, "latency") == 0) {
            config.type = NSA_BENCHMARK_LATENCY;
            strcpy(config.name, "Latency Benchmark");
            strcpy(config.description, "Low latency test");
        } else {
            fprintf(stderr, "Unknown benchmark type: %s\n", benchmark_type);
            result = 1;
            goto cleanup;
        }
        
        config.duration_seconds = duration;
        config.target_pps = target_pps;
        config.num_threads = num_threads;
        config.load_pattern = NSA_LOAD_CONSTANT;
        config.traffic_profile = NSA_TRAFFIC_MIXED_WEB;
        config.packet_size_min = 64;
        config.packet_size_max = 1518;
        config.min_throughput_pps = target_pps * 0.8;
        config.max_latency_us = 100;
        
        printf("Running %s benchmark...\n", config.name);
        
        if (nsa_run_benchmark(&config, &results) == 0) {
            printf("Benchmark completed successfully!\n");
            printf("Results:\n");
            printf("  Throughput: %lu PPS\n", results.throughput.packets_per_second);
            printf("  Latency: %lu ns avg (min: %lu, max: %lu)\n", 
                   results.latency.avg_ns, results.latency.min_ns, results.latency.max_ns);
            printf("  P95 Latency: %lu ns\n", results.latency.p95_ns);
            printf("  P99 Latency: %lu ns\n", results.latency.p99_ns);
            printf("  Sessions: %u created, %u destroyed\n",
                   results.sessions.total_sessions_created,
                   results.sessions.total_sessions_destroyed);
            printf("  Score: %.1f/100\n", results.overall_score);
            printf("  Validation: %s\n", results.passed_validation ? "PASS" : "FAIL");
            
            /* Generate detailed report if output file specified */
            if (output_file) {
                char report_buffer[8192];
                if (nsa_generate_performance_report(&results, report_buffer, sizeof(report_buffer)) == 0) {
                    FILE *fp = fopen(output_file, "w");
                    if (fp) {
                        fprintf(fp, "%s", report_buffer);
                        fclose(fp);
                        printf("Detailed report saved to: %s\n", output_file);
                    } else {
                        fprintf(stderr, "Failed to write report to: %s\n", output_file);
                    }
                }
            }
            
        } else {
            fprintf(stderr, "Benchmark failed\n");
            result = 1;
        }
    }
    
cleanup:
    /* Cleanup */
    if (enable_profiling) {
        nsa_performance_analyzer_cleanup();
    }
    nsa_benchmark_suite_cleanup();
    
    printf("Benchmark tool finished.\n");
    return result;
}
