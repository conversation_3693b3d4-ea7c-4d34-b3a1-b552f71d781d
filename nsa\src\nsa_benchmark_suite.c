/**
 * @file nsa_benchmark_suite.c
 * @brief NSA Benchmarking and Stress Testing Implementation
 */

#include "nsa_benchmark_suite.h"
#include "nsa.h"
#include "nsa_logging.h"
#include <pthread.h>
#include <rte_cycles.h>
#include <rte_malloc.h>
#include <rte_random.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

/* Global benchmark context */
static struct {
  bool initialized;
  bool running;
  pthread_t *worker_threads;
  uint32_t num_threads;
  nsa_benchmark_config_t current_config;
  nsa_benchmark_results_t current_results;
  uint64_t start_tsc;
  uint64_t end_tsc;
} g_benchmark_ctx = {0};

/* Thread-local benchmark data */
typedef struct {
  uint32_t thread_id;
  uint64_t packets_sent;
  uint64_t packets_received;
  uint64_t bytes_sent;
  uint64_t bytes_received;
  uint64_t total_latency_ns;
  uint64_t min_latency_ns;
  uint64_t max_latency_ns;
  uint32_t sessions_created;
  uint32_t sessions_destroyed;
  bool thread_running;
} benchmark_thread_data_t;

static benchmark_thread_data_t *g_thread_data = NULL;

/**
 * @brief Initialize the benchmark suite
 */
int nsa_benchmark_suite_init(void) {
  if (g_benchmark_ctx.initialized) {
    NSA_LOG_WARNING("Benchmark suite already initialized");
    return 0;
  }

  memset(&g_benchmark_ctx, 0, sizeof(g_benchmark_ctx));
  g_benchmark_ctx.initialized = true;
  g_benchmark_ctx.running = false;

  NSA_LOG_INFO("Benchmark suite initialized successfully");
  return 0;
}

/**
 * @brief Cleanup the benchmark suite
 */
void nsa_benchmark_suite_cleanup(void) {
  if (!g_benchmark_ctx.initialized) {
    return;
  }

  if (g_benchmark_ctx.running) {
    NSA_LOG_WARNING("Stopping running benchmark before cleanup");
    g_benchmark_ctx.running = false;
  }

  if (g_benchmark_ctx.worker_threads) {
    rte_free(g_benchmark_ctx.worker_threads);
    g_benchmark_ctx.worker_threads = NULL;
  }

  if (g_thread_data) {
    rte_free(g_thread_data);
    g_thread_data = NULL;
  }

  g_benchmark_ctx.initialized = false;
  NSA_LOG_INFO("Benchmark suite cleaned up");
}

/**
 * @brief Generate synthetic packet data
 */
static void generate_packet_data(uint8_t *buffer, uint32_t size,
                                 nsa_traffic_profile_t profile) {
  switch (profile) {
  case NSA_TRAFFIC_HTTP:
    // Generate HTTP-like traffic
    snprintf((char *)buffer, size,
             "GET /index.html HTTP/1.1\r\nHost: example.com\r\n"
             "User-Agent: NSA-Benchmark/1.0\r\n\r\n");
    break;

  case NSA_TRAFFIC_HTTPS:
    // Generate encrypted-like random data
    for (uint32_t i = 0; i < size; i++) {
      buffer[i] = rte_rand() & 0xFF;
    }
    // Add TLS-like header
    buffer[0] = 0x16; // TLS Handshake
    buffer[1] = 0x03; // TLS 1.2
    buffer[2] = 0x03;
    break;

  case NSA_TRAFFIC_DNS:
    // Generate DNS query
    memset(buffer, 0, size);
    buffer[0] = 0x12;
    buffer[1] = 0x34; // Transaction ID
    buffer[2] = 0x01;
    buffer[3] = 0x00; // Flags
    buffer[4] = 0x00;
    buffer[5] = 0x01; // Questions
    // Add domain name
    strcpy((char *)&buffer[12], "\x07example\x03com\x00");
    break;

  default:
    // Random data
    for (uint32_t i = 0; i < size; i++) {
      buffer[i] = rte_rand() & 0xFF;
    }
    break;
  }
}

/**
 * @brief Calculate load for current time based on pattern
 */
static uint64_t calculate_current_load(nsa_load_pattern_t pattern,
                                       uint64_t base_pps, uint64_t max_pps,
                                       uint64_t elapsed_seconds,
                                       uint64_t total_duration) {
  double progress = (double)elapsed_seconds / total_duration;

  switch (pattern) {
  case NSA_LOAD_CONSTANT:
    return base_pps;

  case NSA_LOAD_RAMP_UP:
    return base_pps + (uint64_t)((max_pps - base_pps) * progress);

  case NSA_LOAD_RAMP_DOWN:
    return max_pps - (uint64_t)((max_pps - base_pps) * progress);

  case NSA_LOAD_SPIKE:
    // Spike at 50% of duration
    if (progress > 0.4 && progress < 0.6) {
      return max_pps;
    }
    return base_pps;

  case NSA_LOAD_BURST:
    // Burst every 10% of duration
    if (((uint64_t)(progress * 10)) % 2 == 0) {
      return max_pps;
    }
    return base_pps;

  case NSA_LOAD_SINE_WAVE:
    return base_pps + (uint64_t)((max_pps - base_pps) *
                                 (sin(progress * 2 * M_PI) + 1) / 2);

  case NSA_LOAD_RANDOM:
    return base_pps + (rte_rand() % (max_pps - base_pps));

  default:
    return base_pps;
  }
}

/**
 * @brief Benchmark worker thread
 */
static void *benchmark_worker_thread(void *arg) {
  benchmark_thread_data_t *data = (benchmark_thread_data_t *)arg;
  uint8_t packet_buffer[2048];
  uint64_t last_second_tsc = rte_rdtsc();
  uint64_t packets_this_second = 0;
  uint64_t target_pps_per_thread = g_benchmark_ctx.current_config.target_pps /
                                   g_benchmark_ctx.current_config.num_threads;

  NSA_LOG_INFO("Benchmark worker thread %u started, target PPS: %lu",
               data->thread_id, target_pps_per_thread);

  data->thread_running = true;
  data->min_latency_ns = UINT64_MAX;
  data->max_latency_ns = 0;

  while (g_benchmark_ctx.running && data->thread_running) {
    uint64_t current_tsc = rte_rdtsc();
    uint64_t elapsed_seconds =
        (current_tsc - g_benchmark_ctx.start_tsc) / rte_get_timer_hz();

    // Check if test duration exceeded
    if (elapsed_seconds >= g_benchmark_ctx.current_config.duration_seconds) {
      break;
    }

    // Calculate current load based on pattern
    uint64_t current_target_pps = calculate_current_load(
        g_benchmark_ctx.current_config.load_pattern, target_pps_per_thread,
        g_benchmark_ctx.current_config.max_pps /
            g_benchmark_ctx.current_config.num_threads,
        elapsed_seconds, g_benchmark_ctx.current_config.duration_seconds);

    // Rate limiting - check if we should send a packet this second
    if ((current_tsc - last_second_tsc) >= rte_get_timer_hz()) {
      // New second, reset counters
      last_second_tsc = current_tsc;
      packets_this_second = 0;
    }

    if (packets_this_second >= current_target_pps) {
      // Rate limit reached, sleep briefly
      usleep(1000); // 1ms
      continue;
    }

    // Generate packet
    uint32_t packet_size =
        g_benchmark_ctx.current_config.packet_size_min +
        (rte_rand() % (g_benchmark_ctx.current_config.packet_size_max -
                       g_benchmark_ctx.current_config.packet_size_min + 1));

    generate_packet_data(packet_buffer, packet_size,
                         g_benchmark_ctx.current_config.traffic_profile);

    // Simulate packet processing (this would be actual NSA processing in real
    // implementation)
    uint64_t process_start = rte_rdtsc();

    // Simulate DPI processing delay
    volatile uint32_t dummy = 0;
    for (int i = 0; i < 1000; i++) {
      dummy += packet_buffer[i % packet_size];
    }

    uint64_t process_end = rte_rdtsc();
    uint64_t latency_cycles = process_end - process_start;
    uint64_t latency_ns = (latency_cycles * 1000000000ULL) / rte_get_timer_hz();

    // Update statistics
    data->packets_sent++;
    data->packets_received++;
    data->bytes_sent += packet_size;
    data->bytes_received += packet_size;
    data->total_latency_ns += latency_ns;

    if (latency_ns < data->min_latency_ns) {
      data->min_latency_ns = latency_ns;
    }
    if (latency_ns > data->max_latency_ns) {
      data->max_latency_ns = latency_ns;
    }

    packets_this_second++;

    // Simulate session creation/destruction
    if ((data->packets_sent % 100) == 0) {
      data->sessions_created++;
    }
    if ((data->packets_sent % 150) == 0) {
      data->sessions_destroyed++;
    }
  }

  data->thread_running = false;
  NSA_LOG_INFO(
      "Benchmark worker thread %u completed. Packets: %lu, Avg latency: %lu ns",
      data->thread_id, data->packets_sent,
      data->packets_sent > 0 ? data->total_latency_ns / data->packets_sent : 0);

  return NULL;
}

/**
 * @brief Aggregate results from all worker threads
 */
static void aggregate_benchmark_results(nsa_benchmark_results_t *results) {
  memset(results, 0, sizeof(nsa_benchmark_results_t));

  // Copy configuration
  memcpy(&results->config, &g_benchmark_ctx.current_config,
         sizeof(nsa_benchmark_config_t));

  // Set timing info
  results->start_time =
      time(NULL) - g_benchmark_ctx.current_config.duration_seconds;
  results->end_time = time(NULL);
  results->actual_duration_seconds =
      g_benchmark_ctx.current_config.duration_seconds;
  results->completed_successfully = true;

  // Aggregate thread data
  uint64_t total_latency_sum = 0;
  uint64_t total_packets = 0;
  uint64_t min_latency = UINT64_MAX;
  uint64_t max_latency = 0;

  for (uint32_t i = 0; i < g_benchmark_ctx.num_threads; i++) {
    benchmark_thread_data_t *data = &g_thread_data[i];

    results->throughput.total_packets += data->packets_sent;
    results->throughput.total_bytes += data->bytes_sent;
    results->sessions.total_sessions_created += data->sessions_created;
    results->sessions.total_sessions_destroyed += data->sessions_destroyed;

    total_latency_sum += data->total_latency_ns;
    total_packets += data->packets_sent;

    if (data->min_latency_ns < min_latency) {
      min_latency = data->min_latency_ns;
    }
    if (data->max_latency_ns > max_latency) {
      max_latency = data->max_latency_ns;
    }
  }

  // Calculate throughput metrics
  if (results->actual_duration_seconds > 0) {
    results->throughput.packets_per_second =
        results->throughput.total_packets / results->actual_duration_seconds;
    results->throughput.bits_per_second =
        (results->throughput.total_bytes * 8) /
        results->actual_duration_seconds;
  }

  // Calculate latency metrics
  if (total_packets > 0) {
    results->latency.avg_ns = total_latency_sum / total_packets;
    results->latency.min_ns = min_latency;
    results->latency.max_ns = max_latency;

    // Simplified percentile calculation (would need proper histogram in real
    // implementation)
    results->latency.p50_ns = results->latency.avg_ns;
    results->latency.p95_ns = results->latency.avg_ns * 2;
    results->latency.p99_ns = results->latency.avg_ns * 3;
    results->latency.p999_ns = results->latency.max_ns;
  }

  // Calculate performance scores
  double throughput_ratio = (double)results->throughput.packets_per_second /
                            g_benchmark_ctx.current_config.target_pps;
  results->throughput_score =
      (throughput_ratio > 1.0) ? 100.0 : throughput_ratio * 100.0;

  double latency_ratio = (double)g_benchmark_ctx.current_config.max_latency_us *
                         1000 / results->latency.avg_ns;
  results->latency_score =
      (latency_ratio > 1.0) ? 100.0 : latency_ratio * 100.0;

  results->overall_score =
      (results->throughput_score + results->latency_score) / 2.0;

  // Validation
  results->throughput_passed =
      results->throughput.packets_per_second >=
      g_benchmark_ctx.current_config.min_throughput_pps;
  results->latency_passed =
      results->latency.avg_ns <=
      (g_benchmark_ctx.current_config.max_latency_us * 1000);
  results->passed_validation =
      results->throughput_passed && results->latency_passed;
}

/**
 * @brief Run a single benchmark
 */
int nsa_run_benchmark(const nsa_benchmark_config_t *config,
                      nsa_benchmark_results_t *results) {
  if (!g_benchmark_ctx.initialized) {
    NSA_LOG_ERROR("Benchmark suite not initialized");
    return -1;
  }

  if (!config || !results) {
    NSA_LOG_ERROR("Invalid parameters");
    return -1;
  }

  if (g_benchmark_ctx.running) {
    NSA_LOG_ERROR("Another benchmark is already running");
    return -1;
  }

  NSA_LOG_INFO("Starting benchmark: %s", config->name);
  NSA_LOG_INFO("Duration: %us, Target PPS: %lu, Threads: %u",
               config->duration_seconds, config->target_pps,
               config->num_threads);

  // Copy configuration
  memcpy(&g_benchmark_ctx.current_config, config,
         sizeof(nsa_benchmark_config_t));
  g_benchmark_ctx.num_threads = config->num_threads;

  // Allocate thread data
  g_thread_data =
      rte_zmalloc("benchmark_threads",
                  sizeof(benchmark_thread_data_t) * config->num_threads,
                  RTE_CACHE_LINE_SIZE);
  if (!g_thread_data) {
    NSA_LOG_ERROR("Failed to allocate thread data");
    return -1;
  }

  // Allocate worker threads
  g_benchmark_ctx.worker_threads =
      rte_zmalloc("worker_threads", sizeof(pthread_t) * config->num_threads,
                  RTE_CACHE_LINE_SIZE);
  if (!g_benchmark_ctx.worker_threads) {
    NSA_LOG_ERROR("Failed to allocate worker threads");
    rte_free(g_thread_data);
    return -1;
  }

  // Initialize thread data
  for (uint32_t i = 0; i < config->num_threads; i++) {
    memset(&g_thread_data[i], 0, sizeof(benchmark_thread_data_t));
    g_thread_data[i].thread_id = i;
  }

  // Start benchmark
  g_benchmark_ctx.running = true;
  g_benchmark_ctx.start_tsc = rte_rdtsc();

  // Create worker threads
  for (uint32_t i = 0; i < config->num_threads; i++) {
    if (pthread_create(&g_benchmark_ctx.worker_threads[i], NULL,
                       benchmark_worker_thread, &g_thread_data[i]) != 0) {
      NSA_LOG_ERROR("Failed to create worker thread %u", i);
      g_benchmark_ctx.running = false;
      return -1;
    }
  }

  // Wait for completion
  for (uint32_t i = 0; i < config->num_threads; i++) {
    pthread_join(g_benchmark_ctx.worker_threads[i], NULL);
  }

  g_benchmark_ctx.end_tsc = rte_rdtsc();
  g_benchmark_ctx.running = false;

  // Aggregate results
  aggregate_benchmark_results(results);

  // Cleanup
  rte_free(g_benchmark_ctx.worker_threads);
  rte_free(g_thread_data);
  g_benchmark_ctx.worker_threads = NULL;
  g_thread_data = NULL;

  NSA_LOG_INFO("Benchmark completed successfully");
  NSA_LOG_INFO("Results: %lu PPS, %lu ns avg latency, Score: %.1f",
               results->throughput.packets_per_second, results->latency.avg_ns,
               results->overall_score);

  return 0;
}

/**
 * @brief Run a comprehensive benchmark suite
 */
int nsa_run_benchmark_suite(nsa_benchmark_results_t *results,
                            uint32_t max_results, uint32_t *actual_results) {
  if (!results || !actual_results) {
    NSA_LOG_ERROR("Invalid parameters");
    return -1;
  }

  *actual_results = 0;

  // Define benchmark configurations
  nsa_benchmark_config_t configs[] = {
      {.type = NSA_BENCHMARK_THROUGHPUT,
       .name = "Throughput Benchmark",
       .description = "Maximum throughput test with constant load",
       .duration_seconds = 60,
       .warmup_seconds = 10,
       .cooldown_seconds = 5,
       .load_pattern = NSA_LOAD_CONSTANT,
       .target_pps = 100000,
       .max_pps = 200000,
       .concurrent_sessions = 10000,
       .max_sessions = 50000,
       .traffic_profile = NSA_TRAFFIC_MIXED_WEB,
       .packet_size_min = 64,
       .packet_size_max = 1518,
       .flow_duration_min_ms = 1000,
       .flow_duration_max_ms = 30000,
       .num_threads = 4,
       .cpu_affinity_mask = 0xF,
       .min_throughput_pps = 80000,
       .max_latency_us = 100,
       .max_packet_loss_ppm = 1000,
       .max_memory_usage_mb = 2048,
       .max_cpu_utilization = 85},
      {.type = NSA_BENCHMARK_LATENCY,
       .name = "Latency Benchmark",
       .description = "Low latency test with moderate load",
       .duration_seconds = 60,
       .warmup_seconds = 10,
       .cooldown_seconds = 5,
       .load_pattern = NSA_LOAD_CONSTANT,
       .target_pps = 50000,
       .max_pps = 50000,
       .concurrent_sessions = 5000,
       .max_sessions = 10000,
       .traffic_profile = NSA_TRAFFIC_HTTP,
       .packet_size_min = 64,
       .packet_size_max = 512,
       .flow_duration_min_ms = 100,
       .flow_duration_max_ms = 5000,
       .num_threads = 2,
       .cpu_affinity_mask = 0x3,
       .min_throughput_pps = 45000,
       .max_latency_us = 50,
       .max_packet_loss_ppm = 100,
       .max_memory_usage_mb = 1024,
       .max_cpu_utilization = 70},
      {.type = NSA_BENCHMARK_SESSION_CAPACITY,
       .name = "Session Capacity Test",
       .description = "Maximum concurrent sessions test",
       .duration_seconds = 120,
       .warmup_seconds = 15,
       .cooldown_seconds = 10,
       .load_pattern = NSA_LOAD_RAMP_UP,
       .target_pps = 75000,
       .max_pps = 150000,
       .concurrent_sessions = 100000,
       .max_sessions = 200000,
       .traffic_profile = NSA_TRAFFIC_MIXED_WEB,
       .packet_size_min = 64,
       .packet_size_max = 1024,
       .flow_duration_min_ms = 10000,
       .flow_duration_max_ms = 60000,
       .num_threads = 6,
       .cpu_affinity_mask = 0x3F,
       .min_throughput_pps = 60000,
       .max_latency_us = 200,
       .max_packet_loss_ppm = 5000,
       .max_memory_usage_mb = 4096,
       .max_cpu_utilization = 90}};

  uint32_t num_configs = sizeof(configs) / sizeof(configs[0]);
  uint32_t results_count = 0;

  NSA_LOG_INFO("Starting comprehensive benchmark suite with %u tests",
               num_configs);

  for (uint32_t i = 0; i < num_configs && results_count < max_results; i++) {
    NSA_LOG_INFO("Running benchmark %u/%u: %s", i + 1, num_configs,
                 configs[i].name);

    if (nsa_run_benchmark(&configs[i], &results[results_count]) == 0) {
      results_count++;
      NSA_LOG_INFO("Benchmark %s completed successfully", configs[i].name);
    } else {
      NSA_LOG_ERROR("Benchmark %s failed", configs[i].name);
    }

    // Brief pause between tests
    sleep(2);
  }

  *actual_results = results_count;
  NSA_LOG_INFO("Benchmark suite completed. %u/%u tests successful",
               results_count, num_configs);

  return (results_count == num_configs) ? 0 : -1;
}

/**
 * @brief Run stress test with specified parameters
 */
int nsa_run_stress_test(uint64_t target_pps, uint32_t duration_seconds,
                        nsa_benchmark_results_t *results) {
  if (!results) {
    NSA_LOG_ERROR("Invalid parameters");
    return -1;
  }

  nsa_benchmark_config_t stress_config = {
      .type = NSA_BENCHMARK_CPU_STRESS,
      .name = "Stress Test",
      .description = "High load stress test",
      .duration_seconds = duration_seconds,
      .warmup_seconds = 10,
      .cooldown_seconds = 5,
      .load_pattern = NSA_LOAD_BURST,
      .target_pps = target_pps,
      .max_pps = target_pps * 2,
      .concurrent_sessions = target_pps / 10,
      .max_sessions = target_pps / 5,
      .traffic_profile = NSA_TRAFFIC_MIXED_WEB,
      .packet_size_min = 64,
      .packet_size_max = 1518,
      .flow_duration_min_ms = 100,
      .flow_duration_max_ms = 10000,
      .num_threads = 8,
      .cpu_affinity_mask = 0xFF,
      .min_throughput_pps = target_pps / 2,
      .max_latency_us = 1000,
      .max_packet_loss_ppm = 10000,
      .max_memory_usage_mb = 8192,
      .max_cpu_utilization = 95};

  NSA_LOG_INFO("Starting stress test: %lu PPS for %u seconds", target_pps,
               duration_seconds);

  return nsa_run_benchmark(&stress_config, results);
}

/**
 * @brief Check performance regression
 */
bool nsa_check_performance_regression(
    const nsa_benchmark_results_t *baseline_results,
    const nsa_benchmark_results_t *current_results,
    double regression_threshold) {
  if (!baseline_results || !current_results) {
    NSA_LOG_ERROR("Invalid parameters");
    return false;
  }

  // Check throughput regression
  double throughput_change =
      ((double)current_results->throughput.packets_per_second -
       baseline_results->throughput.packets_per_second) /
      baseline_results->throughput.packets_per_second * 100.0;

  // Check latency regression
  double latency_change = ((double)current_results->latency.avg_ns -
                           baseline_results->latency.avg_ns) /
                          baseline_results->latency.avg_ns * 100.0;

  NSA_LOG_INFO("Performance comparison:");
  NSA_LOG_INFO("  Throughput change: %.2f%%", throughput_change);
  NSA_LOG_INFO("  Latency change: %.2f%%", latency_change);

  // Regression detected if throughput decreased or latency increased beyond
  // threshold
  bool throughput_regression = throughput_change < -regression_threshold;
  bool latency_regression = latency_change > regression_threshold;

  if (throughput_regression) {
    NSA_LOG_WARNING("Throughput regression detected: %.2f%% decrease",
                    -throughput_change);
  }

  if (latency_regression) {
    NSA_LOG_WARNING("Latency regression detected: %.2f%% increase",
                    latency_change);
  }

  return !(throughput_regression || latency_regression);
}

/**
 * @brief Generate performance report
 */
int nsa_generate_performance_report(const nsa_benchmark_results_t *results,
                                    char *report_buffer, size_t buffer_size) {
  if (!results || !report_buffer || buffer_size == 0) {
    return -1;
  }

  int written = snprintf(
      report_buffer, buffer_size,
      "NSA Performance Benchmark Report\n"
      "================================\n\n"
      "Test Configuration:\n"
      "  Name: %s\n"
      "  Type: %d\n"
      "  Duration: %u seconds\n"
      "  Target PPS: %lu\n"
      "  Threads: %u\n\n"
      "Results:\n"
      "  Completion: %s\n"
      "  Actual Duration: %u seconds\n\n"
      "Throughput Metrics:\n"
      "  Total Packets: %lu\n"
      "  Total Bytes: %lu\n"
      "  Packets/Second: %lu\n"
      "  Bits/Second: %lu\n"
      "  Peak PPS: %lu\n"
      "  Dropped Packets: %lu\n"
      "  Packet Loss Rate: %.3f%%\n\n"
      "Latency Metrics:\n"
      "  Average: %lu ns\n"
      "  Minimum: %lu ns\n"
      "  Maximum: %lu ns\n"
      "  P95: %lu ns\n"
      "  P99: %lu ns\n"
      "  P99.9: %lu ns\n\n"
      "Session Metrics:\n"
      "  Sessions Created: %u\n"
      "  Sessions Destroyed: %u\n"
      "  Peak Concurrent: %u\n"
      "  Average Concurrent: %u\n\n"
      "Performance Scores:\n"
      "  Overall Score: %.1f/100\n"
      "  Throughput Score: %.1f/100\n"
      "  Latency Score: %.1f/100\n\n"
      "Validation Results:\n"
      "  Overall: %s\n"
      "  Throughput: %s\n"
      "  Latency: %s\n",

      results->config.name, results->config.type,
      results->config.duration_seconds, results->config.target_pps,
      results->config.num_threads,

      results->completed_successfully ? "SUCCESS" : "FAILED",
      results->actual_duration_seconds,

      results->throughput.total_packets, results->throughput.total_bytes,
      results->throughput.packets_per_second,
      results->throughput.bits_per_second, results->throughput.peak_pps,
      results->throughput.dropped_packets, results->throughput.packet_loss_rate,

      results->latency.avg_ns, results->latency.min_ns, results->latency.max_ns,
      results->latency.p95_ns, results->latency.p99_ns,
      results->latency.p999_ns,

      results->sessions.total_sessions_created,
      results->sessions.total_sessions_destroyed,
      results->sessions.peak_concurrent_sessions,
      results->sessions.avg_concurrent_sessions,

      results->overall_score, results->throughput_score, results->latency_score,

      results->passed_validation ? "PASS" : "FAIL",
      results->throughput_passed ? "PASS" : "FAIL",
      results->latency_passed ? "PASS" : "FAIL");

  return (written > 0 && written < buffer_size) ? 0 : -1;
}
