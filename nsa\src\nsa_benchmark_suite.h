/**
 * @file nsa_benchmark_suite.h
 * @brief Comprehensive NSA Benchmarking and Stress Testing Suite
 * 
 * This file contains advanced benchmarking and stress testing tools
 * for performance validation and optimization verification.
 * 
 * Key features:
 * - Comprehensive performance benchmarks
 * - Stress testing with configurable load patterns
 * - Latency and throughput measurement
 * - Performance regression testing
 * - Automated performance validation
 * - Load generation and traffic simulation
 * 
 * <AUTHOR> Optimization Team
 * @date 2025
 */

#ifndef NSA_BENCHMARK_SUITE_H
#define NSA_BENCHMARK_SUITE_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * BENCHMARK CONFIGURATION
 * ======================================================================== */

#define NSA_BENCHMARK_MAX_DURATION_SEC  3600    /**< Maximum benchmark duration */
#define NSA_BENCHMARK_MAX_THREADS       64      /**< Maximum benchmark threads */
#define NSA_BENCHMARK_MAX_SCENARIOS     32      /**< Maximum test scenarios */

/** @brief Benchmark types */
typedef enum {
    NSA_BENCHMARK_THROUGHPUT = 0,       /**< Throughput benchmark */
    NSA_BENCHMARK_LATENCY,              /**< Latency benchmark */
    NSA_BENCHMARK_SESSION_CAPACITY,     /**< Session capacity benchmark */
    NSA_BENCHMARK_DPI_PERFORMANCE,      /**< DPI performance benchmark */
    NSA_BENCHMARK_MEMORY_STRESS,        /**< Memory stress test */
    NSA_BENCHMARK_CPU_STRESS,           /**< CPU stress test */
    NSA_BENCHMARK_MIXED_WORKLOAD,       /**< Mixed workload test */
    NSA_BENCHMARK_REGRESSION,           /**< Performance regression test */
    NSA_BENCHMARK_COUNT
} nsa_benchmark_type_t;

/** @brief Load pattern types */
typedef enum {
    NSA_LOAD_CONSTANT = 0,              /**< Constant load */
    NSA_LOAD_RAMP_UP,                   /**< Ramping up load */
    NSA_LOAD_RAMP_DOWN,                 /**< Ramping down load */
    NSA_LOAD_SPIKE,                     /**< Spike load pattern */
    NSA_LOAD_BURST,                     /**< Burst load pattern */
    NSA_LOAD_SINE_WAVE,                 /**< Sine wave load pattern */
    NSA_LOAD_RANDOM,                    /**< Random load pattern */
    NSA_LOAD_COUNT
} nsa_load_pattern_t;

/** @brief Traffic profile types */
typedef enum {
    NSA_TRAFFIC_HTTP = 0,               /**< HTTP traffic */
    NSA_TRAFFIC_HTTPS,                  /**< HTTPS traffic */
    NSA_TRAFFIC_DNS,                    /**< DNS traffic */
    NSA_TRAFFIC_MIXED_WEB,              /**< Mixed web traffic */
    NSA_TRAFFIC_P2P,                    /**< P2P traffic */
    NSA_TRAFFIC_STREAMING,              /**< Streaming traffic */
    NSA_TRAFFIC_MALICIOUS,              /**< Malicious traffic */
    NSA_TRAFFIC_ENCRYPTED,              /**< Encrypted traffic */
    NSA_TRAFFIC_COUNT
} nsa_traffic_profile_t;

/* ========================================================================
 * BENCHMARK CONFIGURATION STRUCTURES
 * ======================================================================== */

/** @brief Benchmark configuration */
typedef struct {
    nsa_benchmark_type_t type;
    char name[128];
    char description[256];
    
    /* Duration and timing */
    uint32_t duration_seconds;
    uint32_t warmup_seconds;
    uint32_t cooldown_seconds;
    
    /* Load configuration */
    nsa_load_pattern_t load_pattern;
    uint64_t target_pps;                /**< Target packets per second */
    uint64_t max_pps;                   /**< Maximum packets per second */
    uint32_t concurrent_sessions;       /**< Concurrent sessions */
    uint32_t max_sessions;              /**< Maximum sessions */
    
    /* Traffic configuration */
    nsa_traffic_profile_t traffic_profile;
    uint32_t packet_size_min;           /**< Minimum packet size */
    uint32_t packet_size_max;           /**< Maximum packet size */
    uint32_t flow_duration_min_ms;      /**< Minimum flow duration */
    uint32_t flow_duration_max_ms;      /**< Maximum flow duration */
    
    /* Threading */
    uint32_t num_threads;
    uint32_t cpu_affinity_mask;
    
    /* Validation thresholds */
    uint64_t min_throughput_pps;        /**< Minimum acceptable throughput */
    uint32_t max_latency_us;            /**< Maximum acceptable latency */
    uint32_t max_packet_loss_ppm;       /**< Maximum packet loss (parts per million) */
    uint32_t max_memory_usage_mb;       /**< Maximum memory usage */
    uint32_t max_cpu_utilization;       /**< Maximum CPU utilization */
    
} nsa_benchmark_config_t;

/* ========================================================================
 * BENCHMARK RESULTS
 * ======================================================================== */

/** @brief Latency statistics */
typedef struct {
    uint64_t min_ns;                    /**< Minimum latency */
    uint64_t max_ns;                    /**< Maximum latency */
    uint64_t avg_ns;                    /**< Average latency */
    uint64_t p50_ns;                    /**< 50th percentile */
    uint64_t p95_ns;                    /**< 95th percentile */
    uint64_t p99_ns;                    /**< 99th percentile */
    uint64_t p999_ns;                   /**< 99.9th percentile */
    double jitter_ns;                   /**< Latency jitter */
    double stddev_ns;                   /**< Standard deviation */
} nsa_latency_stats_t;

/** @brief Throughput statistics */
typedef struct {
    uint64_t total_packets;             /**< Total packets processed */
    uint64_t total_bytes;               /**< Total bytes processed */
    uint64_t packets_per_second;        /**< Average packets per second */
    uint64_t bits_per_second;           /**< Average bits per second */
    uint64_t peak_pps;                  /**< Peak packets per second */
    uint64_t peak_bps;                  /**< Peak bits per second */
    uint64_t dropped_packets;           /**< Dropped packets */
    double packet_loss_rate;            /**< Packet loss rate (%) */
} nsa_throughput_stats_t;

/** @brief Resource utilization statistics */
typedef struct {
    uint32_t peak_memory_usage_mb;      /**< Peak memory usage */
    uint32_t avg_memory_usage_mb;       /**< Average memory usage */
    uint32_t peak_cpu_utilization;      /**< Peak CPU utilization */
    uint32_t avg_cpu_utilization;       /**< Average CPU utilization */
    uint32_t cache_hit_rate;            /**< Cache hit rate */
    uint32_t numa_efficiency;           /**< NUMA efficiency */
} nsa_resource_stats_t;

/** @brief Session statistics */
typedef struct {
    uint32_t total_sessions_created;    /**< Total sessions created */
    uint32_t total_sessions_destroyed;  /**< Total sessions destroyed */
    uint32_t peak_concurrent_sessions;  /**< Peak concurrent sessions */
    uint32_t avg_concurrent_sessions;   /**< Average concurrent sessions */
    double avg_session_duration_ms;     /**< Average session duration */
    uint32_t session_setup_rate;        /**< Session setup rate */
    uint32_t session_teardown_rate;     /**< Session teardown rate */
} nsa_session_stats_t;

/** @brief DPI performance statistics */
typedef struct {
    uint64_t total_packets_analyzed;    /**< Total packets analyzed */
    uint64_t total_flows_classified;    /**< Total flows classified */
    uint64_t total_threats_detected;    /**< Total threats detected */
    uint32_t classification_accuracy;   /**< Classification accuracy (%) */
    uint32_t false_positive_rate;       /**< False positive rate (%) */
    uint32_t avg_analysis_time_ns;      /**< Average analysis time */
    uint32_t early_detection_rate;      /**< Early detection rate (%) */
} nsa_dpi_stats_t;

/** @brief Comprehensive benchmark results */
typedef struct {
    nsa_benchmark_config_t config;      /**< Benchmark configuration */
    
    /* Execution info */
    time_t start_time;                  /**< Benchmark start time */
    time_t end_time;                    /**< Benchmark end time */
    uint32_t actual_duration_seconds;   /**< Actual duration */
    bool completed_successfully;        /**< Completion status */
    char error_message[256];            /**< Error message if failed */
    
    /* Performance statistics */
    nsa_latency_stats_t latency;
    nsa_throughput_stats_t throughput;
    nsa_resource_stats_t resources;
    nsa_session_stats_t sessions;
    nsa_dpi_stats_t dpi;
    
    /* Validation results */
    bool passed_validation;             /**< Overall validation result */
    bool throughput_passed;             /**< Throughput validation */
    bool latency_passed;                /**< Latency validation */
    bool resource_passed;               /**< Resource validation */
    bool stability_passed;              /**< Stability validation */
    
    /* Performance score */
    double overall_score;               /**< Overall performance score (0-100) */
    double throughput_score;            /**< Throughput score */
    double latency_score;               /**< Latency score */
    double efficiency_score;            /**< Resource efficiency score */
    
} nsa_benchmark_results_t;

/* ========================================================================
 * BENCHMARK SUITE FUNCTIONS
 * ======================================================================== */

/**
 * @brief Initialize the benchmark suite
 * @return 0 on success, negative on error
 */
int nsa_benchmark_suite_init(void);

/**
 * @brief Cleanup the benchmark suite
 */
void nsa_benchmark_suite_cleanup(void);

/**
 * @brief Run a single benchmark
 * @param config Benchmark configuration
 * @param results Output results structure
 * @return 0 on success, negative on error
 */
int nsa_run_benchmark(const nsa_benchmark_config_t *config, nsa_benchmark_results_t *results);

/**
 * @brief Run a comprehensive benchmark suite
 * @param results Array of results (one per benchmark)
 * @param max_results Maximum number of results
 * @param actual_results Output number of actual results
 * @return 0 on success, negative on error
 */
int nsa_run_benchmark_suite(nsa_benchmark_results_t *results, 
                           uint32_t max_results, 
                           uint32_t *actual_results);

/**
 * @brief Run stress test with specified parameters
 * @param target_pps Target packets per second
 * @param duration_seconds Test duration
 * @param results Output results
 * @return 0 on success, negative on error
 */
int nsa_run_stress_test(uint64_t target_pps, 
                       uint32_t duration_seconds, 
                       nsa_benchmark_results_t *results);

/**
 * @brief Run performance regression test
 * @param baseline_results Baseline performance results
 * @param current_results Current performance results
 * @param regression_threshold Regression threshold (%)
 * @return true if no regression detected, false otherwise
 */
bool nsa_check_performance_regression(const nsa_benchmark_results_t *baseline_results,
                                     const nsa_benchmark_results_t *current_results,
                                     double regression_threshold);

/**
 * @brief Generate performance report
 * @param results Benchmark results
 * @param report_buffer Output buffer for report
 * @param buffer_size Size of output buffer
 * @return 0 on success, negative on error
 */
int nsa_generate_performance_report(const nsa_benchmark_results_t *results,
                                   char *report_buffer,
                                   size_t buffer_size);

#ifdef __cplusplus
}
#endif

#endif /* NSA_BENCHMARK_SUITE_H */
