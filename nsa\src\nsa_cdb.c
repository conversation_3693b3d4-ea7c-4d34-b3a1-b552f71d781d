/**
 * @file nsa_cdb.c
 * @brief NSA Database Operations Implementation
 *
 * This file contains the implementation of NSA database operations
 * using the CDB (Configuration Database) framework for session
 * enrichment data storage and retrieval.
 *
 * <AUTHOR>
 * @date 2025
 * @copyright Calix Inc.
 */
#include "nsa_cdb.h"
#include "nsa.h"
#include "nsa_pml.h"
#include <arpa/inet.h>
#include <netinet/in.h>
#include <sys/socket.h>

typedef cdb_error (*nsa_cdb_trigger_handler_t)(cdb_table_h hTbl,
                                               cdb_trig_type type,
                                               const cdb_fields_t *pSelFlds,
                                               void *pCurRec,
                                               const void *pOldRec,
                                               void *pArg,
                                               cdb_rec_id rec_id);

int nsa_cdb_subscribe_to_secmgr_table(nsa_cdb_handle_t *cdb_handle,
                                      const char *remote_table_name,
                                      const char *local_table_name_suffix,
                                      cdb_schema_field_t *schema,
                                      const char *primary_key,
                                      nsa_cdb_trigger_handler_t trigger_func);

/**
 * @brief Trigger for the 'signature_update' table.
 * Placeholder for future generic signature updates.
 */
cdb_error nsa_signature_update_trigger(cdb_table_h hTbl,
                                       cdb_trig_type type,
                                       const cdb_fields_t *pSelFlds,
                                       void *pCurRec,
                                       const void *pOldRec,
                                       void *pArg,
                                       cdb_rec_id rec_id) {
    if (type != CDB_TRIG_AFT_INSERT && type != CDB_TRIG_AFT_UPDATE) {
        return CDB_ERROR_OK;
    }

    signature_bundle_t *classify_info = (signature_bundle_t *) pCurRec;

    static char classify_path[PATH_MAX] = {0};

    strncpy(classify_path, classify_info->path, PATH_MAX - 1);
    classify_path[PATH_MAX - 1] = '\0';

    if (classify_path[0] != '\0') {
        NSA_LOG_DEBUG("Triggering signature bundle hot-reload with rules='%s'",
                      classify_path[0] ? classify_path : "N/A");
        int ret = nsa_pml_hot_reload(classify_path, NULL);
        if (ret == 0) {
            nsa_log_event_simple("[Update] Signature Bundle Update SUCCESS with file '%s' [MD5: %s]",
                        classify_info->path, classify_info->md5);
        } else {
            nsa_log_event_simple("[Update] Signature Bundle Update FAILURE for '%s'. Error: %d",
                        classify_info->type, ret);
        }
        classify_path[0] = '\0';
    }

    return CDB_ERROR_OK;
}

/**
 * @brief Trigger for the 'signature_ruleset_update' table.
 * This function initiates the hot-reload process for PML rule files.
 */
cdb_error nsa_ruleset_update_trigger(cdb_table_h hTbl,
                                     cdb_trig_type type,
                                     const cdb_fields_t *pSelFlds,
                                     void *pCurRec,
                                     const void *pOldRec,
                                     void *pArg,
                                     cdb_rec_id rec_id) {
    if (type != CDB_TRIG_AFT_INSERT && type != CDB_TRIG_AFT_UPDATE) {
        return CDB_ERROR_OK;
    }

    signature_ruleset_t *ruleset_info = (signature_ruleset_t *) pCurRec;

    static char rules_path[PATH_MAX] = {0};

    strncpy(rules_path, ruleset_info->path, PATH_MAX - 1);
    rules_path[PATH_MAX - 1] = '\0';

    if (rules_path[0] != '\0') {
        NSA_LOG_DEBUG("Triggering ruleset hot-reload with rules='%s'", rules_path[0] ? rules_path : "N/A");
        int ret = nsa_pml_hot_reload(NULL, rules_path);
        if (ret == 0) {
            nsa_log_event_simple("[Update] Ruleset Bundle Update SUCCESS with file '%s' [MD5: %s]",
                        ruleset_info->path, ruleset_info->md5);
        } else {
            nsa_log_event_simple("[Update] Ruleset Bundle Update FAILURE for '%s'. Error: %d",
                        ruleset_info->type, ret);
        }
        rules_path[0] = '\0';
    }

    return CDB_ERROR_OK;
}

/**
 * @brief Generic helper function to set up a subscription to a table from secmgrd.
 */
int nsa_cdb_subscribe_to_secmgr_table(nsa_cdb_handle_t *cdb_handle,
                                      const char *remote_table_name,
                                      const char *local_table_name_suffix,
                                      cdb_schema_field_t *schema,
                                      const char *primary_key,
                                      nsa_cdb_trigger_handler_t trigger_func) {
    cdb_table_h hSubscribedTbl;
    cdb_error ret;
    char local_table_name[64];
    snprintf(local_table_name, sizeof(local_table_name), "nsecmgr_%s_copy", local_table_name_suffix);

    // 1. Create local table
    ret = cdb_table_create2(cdb_handle->sub_db_ram, &hSubscribedTbl, local_table_name, schema, 0, primary_key, 0);
    if (ret != CDB_ERROR_OK && ret != CDB_ERROR_EXISTS) {
        NSA_LOG_ERROR("Failed to create local table '%s': %s", local_table_name, CDB_ERRMSG(ret));
        return -1;
    }

    // 2. Create triggers
    char trig_name[64];
    snprintf(trig_name, sizeof(trig_name), "trig_ins_%s", local_table_name_suffix);
    cdb_trigger_create2(hSubscribedTbl, trig_name, CDB_TRIG_AFT_INSERT, trigger_func, NULL, 0);
    snprintf(trig_name, sizeof(trig_name), "trig_upd_%s", local_table_name_suffix);
    cdb_trigger_create2(hSubscribedTbl, trig_name, CDB_TRIG_AFT_UPDATE, trigger_func, NULL, 0);

    // 3. Create pubsub handle and subscribe
    cdb_pub_h hPub;
    const char *publisher_node_name = "nsecmgrd";
    const char *remote_db_name = "nsecmgr_db";

    ret = cdb_pubsub_create(
        &hPub, publisher_node_name, NULL, remote_table_name, NULL, remote_db_name, NSA_CDB_DB_NAME, NULL, 0);
    if (ret != CDB_ERROR_OK) {
        NSA_LOG_ERROR("Failed to create pubsub handle for secmgrd: %s", CDB_ERRMSG(ret));
        return -1;
    }
    // CDB_EXPORT cdb_error cdb_pubsub_subscribe (cdb_pub_h hPubHdl, const char *subname, const char *pubtbl, const char *subtbl, const char *filter, const char *fields, uint32_t flags);
    ret = cdb_pubsub_subscribe(hPub, remote_table_name, remote_table_name, local_table_name, NULL, NULL, 0);
    if (ret != CDB_ERROR_OK) {
        NSA_LOG_ERROR("Failed to subscribe to table '%s': %s", remote_db_name, CDB_ERRMSG(ret));
        return -1;
    }

    // 4. Start sync
    cdb_pubsub_sync(hPub, NULL, CDB_SYNC_AUTO, NULL, NULL, 0);

    NSA_LOG_INFO("Successfully subscribed to secmgrd table '%s'.", remote_table_name);
    return 0;
}

/**
 * @brief Initialize enriched session CDB table
 *
 * Creates the CDB table for storing enriched session information
 * with appropriate schema and indexing.
 *
 * @param[in] cdb_handle  Pointer to NSA CDB handle structure
 * @return Table handle on success, NULL on failure
 */
static cdb_table_h nsa_enriched_session_table_init(nsa_cdb_handle_t *cdb_handle) {
    cdb_error ret = CDB_ERROR_OK;

    if (cdb_handle == NULL) {
        NSA_LOG_ERROR("Invalid CDB handle for session table initialization");
        return NULL;
    }

#undef CDB_STRUCT_NAME
#define CDB_STRUCT_NAME nsa_enriched_session
    cdb_schema_field_t session_schema[] = {NSA_ENRICHED_SESSION_SCHEMA()};

    ret = cdb_table_create2(cdb_handle->sub_db_ram,
                            &cdb_handle->enriched_session,
                            NSA_ENRICHED_SESSION_CDB_TBL_NAME,
                            session_schema,
                            1100000, /* Initial table size */
                            NSA_ENRICHED_SESSION_SCHEMA_KEY,
                            0); /* Flags */

    if ((ret != CDB_ERROR_OK) && (ret != CDB_ERROR_EXISTS)) {
        NSA_LOG_ERROR("Failed to create enriched session CDB table: %s", CDB_ERRMSG(ret));
        return NULL;
    }

    if (ret == CDB_ERROR_EXISTS) {
        NSA_LOG_INFO("Enriched session CDB table already exists, using existing table");
    } else {
        NSA_LOG_INFO("Enriched session CDB table created successfully");
    }

    return cdb_handle->enriched_session;
}

/**
 * @brief Initialize NSA CDB database
 *
 * Creates the main CDB database instance for NSA operations.
 * Uses RAM-based storage for high performance.
 *
 * @param[in] cdb_handle  Pointer to NSA CDB handle structure
 * @return Database handle on success, NULL on failure
 */
static cdb_db_h nsa_cdb_database_init(nsa_cdb_handle_t *cdb_handle) {
    cdb_error ret = CDB_ERROR_OK;

    if (cdb_handle == NULL) {
        NSA_LOG_ERROR("Invalid CDB handle for database initialization");
        return NULL;
    }

    NSA_LOG_INFO("Initializing NSA CDB database: %s", NSA_CDB_DB_NAME);

    ret = cdb_database_create2(&cdb_handle->sub_db_ram,
                               NSA_CDB_DB_NAME,
                               NULL,                /* Database path (NULL for RAM) */
                               CDB_DB_TYPE_RAMDISK, /* RAM-based for performance */
                               (cdb_db_flag) 0);    /* No special flags */

    if ((ret != CDB_ERROR_OK) && (ret != CDB_ERROR_EXISTS)) {
        NSA_LOG_ERROR("Failed to create NSA CDB database: %s", CDB_ERRMSG(ret));
        return NULL;
    }

    if (ret == CDB_ERROR_EXISTS) {
        NSA_LOG_INFO("NSA CDB database already exists, using existing database");
    } else {
        NSA_LOG_INFO("NSA CDB database created successfully");
    }

    return cdb_handle->sub_db_ram;
}

/**
 * @brief Initialize NSA CDB handle and all database components
 *
 * Sets up the complete CDB infrastructure for NSA including database
 * creation, table initialization, and platform-specific initialization.
 *
 * @param[in] root  Pointer to NSA root context
 * @return 0 on success, negative error code on failure
 */
int nsa_cdb_init(nsa_root_t *root) {
    nsa_cdb_handle_t *cdb_handle = NULL;

    if (root == NULL) {
        NSA_LOG_ERROR("Invalid root context for CDB initialization");
        return -1;
    }

    NSA_LOG_INFO("Initializing NSA CDB subsystem");

    cdb_handle = &root->cdb_handle;

    /* Initialize main database */
    cdb_handle->sub_db_ram = nsa_cdb_database_init(cdb_handle);
    if (cdb_handle->sub_db_ram == NULL) {
        NSA_LOG_ERROR("Failed to initialize NSA CDB database");
        return -1;
    }

    /* Initialize enriched session table */
    if (nsa_enriched_session_table_init(cdb_handle) == NULL) {
        NSA_LOG_ERROR("Failed to initialize enriched session table");
        return -1;
    }

    /* Initialize CDB platform components */
    NSA_LOG_DEBUG("Initializing CDB platform components");
    dl_cdb_init();

    /* Initialize publisher/subscriber subsystem */
    NSA_LOG_DEBUG("Initializing CDB pub/sub subsystem");
    cdb_pubsub_init(NULL, NULL, NULL, NULL, NULL);

#undef CDB_STRUCT_NAME
#define CDB_STRUCT_NAME signature_bundle_t
    cdb_schema_field_t bundle_schema[] = {SIGNATURE_BUNDLE_UPDATE_SCHEMA()};

    // --- Setup subscription to the first table ---
    if (nsa_cdb_subscribe_to_secmgr_table(
            &root->cdb_handle, "signature_update", "sig_update", bundle_schema, "type", nsa_signature_update_trigger) !=
        0) {
        NSA_LOG_ERROR("Failed to subscribe to nsecmgrd.signature_update table.");
        return -1;
    }

#undef CDB_STRUCT_NAME
#define CDB_STRUCT_NAME signature_ruleset_t
    cdb_schema_field_t ruleset_schema[] = {SIGNATURE_RULESET_UPDATE_SCHEMA()};

    // --- Setup subscription to the second table ---
    if (nsa_cdb_subscribe_to_secmgr_table(&root->cdb_handle,
                                          "signature_ruleset_update",
                                          "ruleset_update",
                                          ruleset_schema,
                                          "type",
                                          nsa_ruleset_update_trigger) != 0) {
        NSA_LOG_ERROR("Failed to subscribe to secmgrd.signature_ruleset_update table.");
        return -1;
    }

    NSA_LOG_INFO("NSA CDB subsystem initialized successfully");
    return 0;
}

/**
 * @brief Add enriched session data to CDB
 *
 * Inserts a new enriched session record into the CDB database.
 *
 * @param[in] event  Pointer to NSA event containing session data
 * @return 0 on success, negative error code on failure
 */
int nsa_cdb_enriched_session_add(nsa_event_t *event) {
    cdb_error ret = CDB_ERROR_OK;
    nsa_cdb_handle_t *cdb_handle = NULL;
    nsa_enriched_session rich = {0};
    struct dpif_flow_info *flow_info = NULL;

    if (event == NULL) {
        NSA_LOG_ERROR("Invalid event for session add operation");
        return -1;
    }

    if (g_nsa_root == NULL) {
        NSA_LOG_ERROR("Global NSA context is NULL");
        return -1;
    }

    cdb_handle = &g_nsa_root->cdb_handle;
    if (cdb_handle->enriched_session == NULL) {
        NSA_LOG_ERROR("Enriched session table not initialized");
        return -1;
    }

    NSA_LOG_DEBUG("Adding enriched session data for session ID: %d", event->session_id);

    flow_info = (struct dpif_flow_info *) event->payload;
    rich.session_id = event->session_id;
    rich.vpp_sid = flow_info->vpp_sid;

    if (flow_info->address_family == AF_INET) {
        inet_ntop(AF_INET, &flow_info->src_ip_u.ipv4_src_ip, rich.src_ip, INET6_ADDRSTRLEN);
        inet_ntop(AF_INET, &flow_info->dst_ip_u.ipv4_dst_ip, rich.dst_ip, INET6_ADDRSTRLEN);
    } else if (flow_info->address_family == AF_INET6) {
        inet_ntop(AF_INET6, flow_info->src_ip_u.ipv6_src_ip, rich.src_ip, INET6_ADDRSTRLEN);
        inet_ntop(AF_INET6, flow_info->dst_ip_u.ipv6_dst_ip, rich.dst_ip, INET6_ADDRSTRLEN);
    } else {
        strncpy(rich.src_ip, "Unknown AF", INET6_ADDRSTRLEN);
        strncpy(rich.dst_ip, "Unknown AF", INET6_ADDRSTRLEN);
    }

    rich.sport = ntohs(flow_info->src_port);
    rich.dport = ntohs(flow_info->dst_port);
    rich.proto = flow_info->proto;
    memcpy(&rich.sdev_mac, flow_info->src_mac, 6);

    ret = cdb_record_insert(cdb_handle->enriched_session,
                            (void *) &rich,
                            NULL,                /* No transaction */
                            NULL,                /* No callback */
                            CDB_RECORD_REPLACE); /* replace or insert */

    if (ret != CDB_ERROR_OK) {
        NSA_LOG_ERROR("Failed to insert enriched session record: %s", cdb_errmsg(ret));
        return -1;
    }

    NSA_LOG_DEBUG("Successfully added enriched session data");
    return 0;
}

/**
 * @brief Delete enriched session data from CDB
 *
 * Removes an enriched session record from the CDB database.
 *
 * @param[in] event  Pointer to NSA event containing session ID
 * @return 0 on success, negative error code on failure
 */
int nsa_cdb_enriched_session_delete(nsa_event_t *event) {
    cdb_error ret = CDB_ERROR_OK;
    nsa_cdb_handle_t *cdb_handle = NULL;
    nsa_enriched_session session = {0};

    if (event == NULL) {
        NSA_LOG_ERROR("Invalid event for session delete operation");
        return -1;
    }

    if (g_nsa_root == NULL) {
        NSA_LOG_ERROR("Global NSA context is NULL");
        return -1;
    }

    cdb_handle = &g_nsa_root->cdb_handle;
    if (cdb_handle->enriched_session == NULL) {
        NSA_LOG_ERROR("Enriched session table not initialized");
        return -1;
    }

    NSA_LOG_DEBUG("Deleting enriched session data for session ID: %d", event->session_id);

    /* Set up session key for deletion */
    session.session_id = event->session_id;

    ret = cdb_record_delete(cdb_handle->enriched_session, &session, 0); /* No flags */

    if (ret != CDB_ERROR_OK) {
        NSA_LOG_ERROR("Failed to delete enriched session record: %s", cdb_errmsg(ret));
        return -1;
    }

    NSA_LOG_DEBUG("Successfully deleted enriched session data");
    return 0;
}

static void str_to_upper(char *str) {
    for (; *str != '\0'; ++str) {
        *str = toupper((unsigned char) *str);
    }
}

/**
 * @brief Enrich session data to CDB
 *
 * Enrich session record to the CDB database.
 *
 * @param[in] event  Pointer to NSA event containing session ID
 * @return 0 on success, negative error code on failure
 */
int nsa_cdb_enriched_session_enrich(nsa_event_t *event) {
    cdb_error ret = CDB_ERROR_OK;
    nsa_cdb_handle_t *cdb_handle = NULL;
    nsa_enriched_session rich = {0};
    nsa_session_context_t *nsa_ctx = NULL;

    if (event == NULL) {
        NSA_LOG_ERROR("Invalid event for session add operation");
        return -1;
    }

    if (g_nsa_root == NULL) {
        NSA_LOG_ERROR("Global NSA context is NULL");
        return -1;
    }

    cdb_handle = &g_nsa_root->cdb_handle;
    if (cdb_handle->enriched_session == NULL) {
        NSA_LOG_ERROR("Enriched session table not initialized");
        return -1;
    }

    NSA_LOG_DEBUG("Updating enriched session data for session ID: %d", event->session_id);

    nsa_ctx = (nsa_session_context_t *) event->payload;
    rich.session_id = event->session_id;

    ret = cdb_record_get(cdb_handle->enriched_session, &rich, NULL);
    if (CDB_ERROR_OK != ret) {
        NSA_LOG_ERROR("cdb_record_get(%d) failed: %s\n", rich.session_id, cdb_errmsg(ret));
        return -1;
    }

    memcpy(rich.alp, nsa_ctx->alp_name, 16);
    memcpy(rich.app, nsa_ctx->app_name, 16);
    str_to_upper(rich.alp);
    str_to_upper(rich.app);
    if (nsa_ctx->pml_context.verdict == 1) {
        memcpy(rich.verdict, "ACCEPTED", strlen("ACCEPTED") + 1);
    } else {
        memcpy(rich.verdict, "BLOCKED", strlen("BLOCKED") + 1);
    }

    ret = cdb_record_update(cdb_handle->enriched_session, &rich, NULL, 0);
    if (ret != CDB_ERROR_OK) {
        NSA_LOG_ERROR("Failed to update enriched session record: %s", cdb_errmsg(ret));
        return -1;
    }

    NSA_LOG_DEBUG("Successfully updated enriched session data");
    return 0;
}

void nsa_cdb_enriched_session_iterate(nsa_session_iterate_cb_t iter_cb, void *user_data)
{
    if (!iter_cb || !g_nsa_root) {
        return;
    }

    nsa_cdb_handle_t *cdb_handle = NULL;
    cdb_handle = &g_nsa_root->cdb_handle;

    void *cursorHandle = NULL;
    nsa_enriched_session session_entry;
    cdb_error cdb_ret;
    uint32_t index = 0;

    cdb_ret = cdb_table_get_cursor2(cdb_handle->enriched_session, &cursorHandle, NULL, &session_entry, NULL);
    if (cdb_ret != CDB_ERROR_OK) {
        NSA_LOG_ERROR("Failed to get cursor for enriched session table: %s", cdb_errmsg(cdb_ret));
        return;
    }

    cdb_ret = cdb_cursor_get_next(cursorHandle, &session_entry, NULL);
    while (cdb_ret == CDB_ERROR_OK) {
        if (iter_cb(&session_entry, index, user_data) != 0) {
            break; // Callback requested to abort
        }
        index++;
        cdb_ret = cdb_cursor_get_next(cursorHandle, &session_entry, NULL);
    }
    
    cdb_cursor_close(cursorHandle);
}