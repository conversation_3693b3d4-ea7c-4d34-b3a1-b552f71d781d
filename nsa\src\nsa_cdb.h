#ifndef _NSA_CDB_H_
#define _NSA_CDB_H_

#include "nsa.h"
#include "nsa_helper.h"

#ifdef __cplusplus
extern "C" {
#endif

#define NSA_CDB_DB_NAME "nsa_db"
#define NSA_ENRICHED_SESSION_CDB_TBL_NAME "rich_session"
#define NSA_ENRICHED_SESSION_SCHEMA_KEY "session_id"

typedef struct nsa_enriched_session_t {
    uint32_t session_id;
    uint64_t vpp_sid;
    char src_ip[46];
    char dst_ip[46];
    uint16_t sport;
    uint16_t dport;
    uint8_t proto;
    char sdev_mac[6];
    uint8_t src_zone;
    uint8_t dst_zone;
    uint16_t user;
    uint16_t group;
    uint16_t dev_type;
    char alp[16];
    char app[16];
    uint16_t category;
    uint16_t domain;
    uint16_t threat;
    uint8_t threat_level;
    char verdict[16];
} nsa_enriched_session;

#define NSA_ENRICHED_SESSION_SCHEMA()                                                         \
    CDB_SCHEMA_FIELD(session_id, UINT, DFT, 0),                                               \
        CDB_SCHEMA_FIELD(vpp_sid, UINT, DFT, 0),                                              \
        CDB_SCHEMA_FIELD(src_ip, STR, DFT, 0),                                                \
        CDB_SCHEMA_FIELD(dst_ip, STR, DFT, 0), CDB_SCHEMA_FIELD(sport, UINT, DFT, 0),         \
        CDB_SCHEMA_FIELD(dport, UINT, DFT, 0), CDB_SCHEMA_FIELD(proto, UINT, DFT, 0),         \
        CDB_SCHEMA_FIELD(sdev_mac, BYTE, MAC, 0), CDB_SCHEMA_FIELD(src_zone, UINT, DFT, 0),   \
        CDB_SCHEMA_FIELD(dst_zone, UINT, DFT, 0), CDB_SCHEMA_FIELD(user, UINT, DFT, 0),       \
        CDB_SCHEMA_FIELD(group, UINT, DFT, 0), CDB_SCHEMA_FIELD(dev_type, UINT, DFT, 0),      \
        CDB_SCHEMA_FIELD(alp, STR, DFT, 0), CDB_SCHEMA_FIELD(app, STR, DFT, 0),               \
        CDB_SCHEMA_FIELD(category, UINT, DFT, 0), CDB_SCHEMA_FIELD(domain, UINT, DFT, 0),     \
        CDB_SCHEMA_FIELD(threat, UINT, DFT, 0), CDB_SCHEMA_FIELD(threat_level, UINT, DFT, 0), \
        CDB_SCHEMA_FIELD(verdict, STR, DFT, 0), CDB_SCHEMA_END()


#define SIGNATURE_PATH_LEN 256
#define SIGNATURE_MD5_LEN 32
#define SIGNATURE_TYPE_LEN 32

// -- Table 1: signature_update --
// For generic signatures (e.g., IPS, AppID in other formats)
typedef struct {
    char type[SIGNATURE_TYPE_LEN];
    char path[SIGNATURE_PATH_LEN];
    char md5[SIGNATURE_MD5_LEN + 1];
} signature_bundle_t;

#define SIGNATURE_BUNDLE_UPDATE_SCHEMA()                                                                             \
    CDB_SCHEMA_FIELD(type, CHAR, DFT, 0), CDB_SCHEMA_FIELD(path, CHAR, DFT, 0), CDB_SCHEMA_FIELD(md5, CHAR, DFT, 0), \
        CDB_SCHEMA_END()

// -- Table 2: signature_ruleset_update --
// For our PML rule sets (classify.bin, control.bin)
// Assuming SIGNATURE_RULESET_*_LEN constants are defined somewhere accessible
#define SIGNATURE_RULESET_PATH_LEN 256
#define SIGNATURE_RULESET_TYPE_LEN 32
#define SIGNATURE_RULESET_MD5_LEN 32

typedef struct {
    char type[SIGNATURE_RULESET_TYPE_LEN];
    char path[SIGNATURE_RULESET_PATH_LEN];
    char md5[SIGNATURE_RULESET_MD5_LEN + 1];
} signature_ruleset_t;

#define SIGNATURE_RULESET_UPDATE_SCHEMA()                                                                            \
    CDB_SCHEMA_FIELD(type, CHAR, DFT, 0), CDB_SCHEMA_FIELD(path, CHAR, DFT, 0), CDB_SCHEMA_FIELD(md5, CHAR, DFT, 0), \
        CDB_SCHEMA_END()



// Define a callback function pointer type for the iterator
typedef int (*nsa_session_iterate_cb_t)(nsa_enriched_session *session, uint32_t index, void *user_data);

/**
 * @brief Safely iterates over all entries in the enriched_session table.
 * This function handles the entire lifecycle of the CDB cursor, preventing leaks.
 */
void nsa_cdb_enriched_session_iterate(nsa_session_iterate_cb_t iter_cb, void *user_data);

#ifdef __cplusplus
}
#endif

#endif