/**
 * @file nsa_cli.c
 * @brief NSA Command Line Interface Implementation
 * 
 * This file contains the implementation of NSA-specific CLI commands
 * that provide administrative and operational control over the NSA system.
 * 
 * Command categories:
 * - show: Display system status, statistics, and configuration
 * - xexec: Execute administrative operations
 * 
 * <AUTHOR>
 * @date 2025
 * @copyright Calix Inc.
 */

#include "nsa.h"
#include "nsa_cli.h"
#include "nsa_pml.h"

/* ========================================================================
 * CLI Command Handlers
 * ======================================================================== */

/**
 * @brief Execute a CLI command through DPIF interface
 * 
 * Forwards CLI commands to the DPIF layer and returns the response.
 * 
 * @param[in] command        Command string to execute
 * @param[in] output_stream  Output stream for response
 */
static void nsa_handle_cli_command(const char* command, FILE* output_stream) {
    char response_buffer[8192];
    int result = 0;

    if (command == NULL || output_stream == NULL) {
        NSA_LOG_ERROR("Invalid parameters for CLI command handler");
        if (output_stream != NULL) {
            fprintf(output_stream, "Error: Invalid command parameters\r\n");
        }
        return;
    }

    NSA_LOG_DEBUG("Executing CLI command: %s", command);

    result = dpif_execute_cli_command(command, response_buffer, sizeof(response_buffer));
    if (result == 0) {
        fprintf(output_stream, "%s", response_buffer);
        NSA_LOG_DEBUG("CLI command executed successfully");
    } else {
        fprintf(output_stream, "Error: Failed to execute command '%s' (code: %d)\r\n", 
               command, result);
        NSA_LOG_WARNING("CLI command execution failed: %s (code: %d)", command, result);
    }
}

/**
 * @brief Show NSA performance dashboard
 */
static cp_stat_t nsa_cli_show_snapshot(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    NSA_UNUSED(data);
    
    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for dashboard command");
        return CP_FAIL;
    }

    nsa_handle_cli_command("dashboard", cnx->fh);
    return CP_SUCCESS;
}

/**
 * @brief Show NSA global statistics
 */
static cp_stat_t nsa_cli_show_global_stats(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    NSA_UNUSED(data);
    
    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for global stats command");
        return CP_FAIL;
    }

    nsa_handle_cli_command("show_global_stats", cnx->fh);
    return CP_SUCCESS;
}

/**
 * @brief Show NSA core statistics details
 */
static cp_stat_t nsa_cli_show_core_stats(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    NSA_UNUSED(data);
    
    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for core stats command");
        return CP_FAIL;
    }

    nsa_handle_cli_command("show_core_stats all", cnx->fh);
    return CP_SUCCESS;
}

/**
 * @brief Show active NSA sessions
 */
static cp_stat_t nsa_cli_show_sessions(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    NSA_UNUSED(data);
    
    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for sessions command");
        return CP_FAIL;
    }

    nsa_handle_cli_command("list_sessions", cnx->fh);
    return CP_SUCCESS;
}

/**
 * @brief Show NSA configuration
 */
static cp_stat_t nsa_cli_show_configuration(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    NSA_UNUSED(data);
    
    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for configuration command");
        return CP_FAIL;
    }

    nsa_handle_cli_command("show_config", cnx->fh);
    return CP_SUCCESS;
}

/**
 * @brief Load security signature bundle
 */
static cp_stat_t nsa_cli_load_signature_bundle(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    NSA_UNUSED(cnx);
    NSA_UNUSED(data);
    
    /* TODO: Implement signature bundle loading */
    NSA_LOG_INFO("Signature bundle loading - implementation pending");
    
    if (cnx != NULL && cnx->fh != NULL) {
        fprintf(cnx->fh, "Signature bundle loading feature is under development\r\n");
    }
    
    return CP_SUCCESS;
}

/**
 * @brief Inject simulated packets for testing purposes
 */
static cp_stat_t nsa_cli_inject_packets(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    
    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for inject packets command");
        return CP_FAIL;
    }

    /* Build the inject command string for DPIF */
    char inject_command[512];
    int pos = 0;

    /* Add all arguments to the command */
    for (int i = 1; i < data->argc && pos < sizeof(inject_command) - 10; i++) {
        pos += snprintf(inject_command + pos, sizeof(inject_command) - pos, " %s", data->argv[i]);
    }

    NSA_LOG_DEBUG("Executing inject command: %s", inject_command);
    nsa_handle_cli_command(inject_command, cnx->fh);

    return CP_SUCCESS;
}

/**
 * @brief Start PCAP replay for testing purposes  
 */
static cp_stat_t nsa_cli_pcap_replay_start(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    
    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for pcap replay start command");
        return CP_FAIL;
    }

    /* Build the pcap-replay start command string for DPIF */
    char pcap_command[512];
    int pos = 0;

    /* Add all arguments to the command */
    for (int i = 1; i < data->argc && pos < sizeof(pcap_command) - 10; i++) {
        pos += snprintf(pcap_command + pos, sizeof(pcap_command) - pos, " %s", data->argv[i]);
    }

    NSA_LOG_DEBUG("Executing command: %s", pcap_command);
    nsa_handle_cli_command(pcap_command, cnx->fh);

    return CP_SUCCESS;
}

/**
 * @brief Stop PCAP replay
 */
static cp_stat_t nsa_cli_pcap_replay_stop(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    
    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for pcap replay stop command");
        return CP_FAIL;
    }

    /* Build the pcap-replay stop command string for DPIF */
    char pcap_command[256];
    int pos = 0;

    /* Add all arguments to the command */
    for (int i = 1; i < data->argc && pos < sizeof(pcap_command) - 10; i++) {
        pos += snprintf(pcap_command + pos, sizeof(pcap_command) - pos, " %s", data->argv[i]);
    }

    NSA_LOG_DEBUG("Executing pcap replay stop command: %s", pcap_command);
    nsa_handle_cli_command(pcap_command, cnx->fh);
    
    return CP_SUCCESS;
}

/**
 * @brief CLI command handler to trigger a hot-reload of PML rule files.
 */
static cp_stat_t nsa_cli_exec_pml_reload(cp_context_t *cp, dl_cnx_t *cnx, cp_cb_data_t *data) {
    NSA_UNUSED(cp);
    
    const char *classify_file = NULL;
    const char *rules_file = NULL;

    if (cnx == NULL || cnx->fh == NULL) {
        NSA_LOG_ERROR("Invalid connection for pml-reload command.");
        return CP_FAIL;
    }

    // Parse named arguments from the command line
    for (int i = 2; i < data->argc; i += 2) {
        if (i + 1 >= data->argc) {
            fprintf(cnx->fh, "Error: Missing value for argument '%s'\r\n", data->argv[i]);
            return CP_FAIL;
        }
        if (strcmp(data->argv[i], "classify") == 0) {
            classify_file = data->argv[i+1];
        } else if (strcmp(data->argv[i], "rules") == 0) {
            rules_file = data->argv[i+1];
        } else {
            fprintf(cnx->fh, "Error: Unknown argument '%s'\r\n", data->argv[i]);
            return CP_FAIL;
        }
    }

    if (classify_file == NULL && rules_file == NULL) {
        fprintf(cnx->fh, "Error: At least one file path ('classify' or 'rules') must be provided.\r\n");
        return CP_FAIL;
    }

    fprintf(cnx->fh, "Initiating PML hot-reload...\r\n");

    // Call the core hot-reload function
    int ret = nsa_pml_hot_reload(classify_file, rules_file);

    if (ret == 0) {
        fprintf(cnx->fh, "SUCCESS: PML hot-reload request published successfully.\r\n");
        fprintf(cnx->fh, "         RX threads will apply the update on their next cycle.\r\n");
    } else {
        fprintf(cnx->fh, "FAILURE: PML hot-reload failed with error code %d.\r\n", ret);
        fprintf(cnx->fh, "         Check nsad logs for details. Old rules remain active.\r\n");
    }

    return CP_SUCCESS;
}

/* ========================================================================
 * CLI Command Tree Definition
 * ======================================================================== */

/**
 * @brief NSA CLI "show" commands
 */
static cp_cmd_batch_t nsa_cli_show_commands[] = {
    {"snapshot", 
     "Show NSA performance snapshot", 
     CP_NO_ATTRS, 
     CP_NODE_LEAF, 
     CP_NO_CHILD, 
     nsa_cli_show_snapshot},
     
    {"sessions", 
     "Show active NSA sessions", 
     CP_NO_ATTRS, 
     CP_NODE_LEAF, 
     CP_NO_CHILD, 
     nsa_cli_show_sessions},
     
    {"core-stats", 
     "Show NSA core statistics", 
     CP_NO_ATTRS, 
     CP_NODE_LEAF, 
     CP_NO_CHILD, 
     nsa_cli_show_core_stats},
     
    {"global-stats", 
     "Show NSA global statistics", 
     CP_NO_ATTRS, 
     CP_NODE_LEAF, 
     CP_NO_CHILD, 
     nsa_cli_show_global_stats},
     
    {"configuration", 
     "Show NSA configuration", 
     CP_NO_ATTRS, 
     CP_NODE_LEAF, 
     CP_NO_CHILD, 
     nsa_cli_show_configuration},
     
    CP_CMD_BATCH_END
};

/**
 * @brief NSA CLI PCAP replay sub-commands
 */
static cp_cmd_batch_t nsa_cli_pcap_replay_commands[] = {
    {"start",
     "Start PCAP file replay on specified lcore",
     {{"lcore_id", "UINT32", CP_ATTR_REQUIRED},
      {"filepath", "STRING", CP_ATTR_REQUIRED},
      {"count", "UINT32", CP_ATTR_OPTIONAL},
      CP_ATTR_BATCH_END},
     CP_NODE_LEAF,
     CP_NO_CHILD,
     nsa_cli_pcap_replay_start},

    {"stop",
     "Stop PCAP replay on specified lcore",
     {{"lcore_id", "UINT32", CP_ATTR_REQUIRED},
      CP_ATTR_BATCH_END},
     CP_NODE_LEAF,
     CP_NO_CHILD,
     nsa_cli_pcap_replay_stop},
     
    CP_CMD_BATCH_END
};

/**
 * @brief NSA CLI "exec" commands for administrative operations
 */
static cp_cmd_batch_t nsa_cli_exec_commands[] = {
    {"signature-bundle",
     "Load security signature bundle from URL",
     {{"url", "STRING", CP_ATTR_REQUIRED}, 
      {"md5", "STRING", CP_ATTR_REQUIRED}, 
      CP_ATTR_BATCH_END},
     CP_NODE_LEAF,
     CP_NO_CHILD,
     nsa_cli_load_signature_bundle},

    {"pml-reload",
     "Hot-reload PML classify and/or control rule files",
     {{"classify", "STRING", CP_ATTR_OPTIONAL},
      {"rules", "STRING", CP_ATTR_OPTIONAL},
      CP_ATTR_BATCH_END},
     CP_NODE_LEAF,
     CP_NO_CHILD,
     nsa_cli_exec_pml_reload},

    {"inject",
     "Inject simulated packets for testing",
     {{"lcore_id", "UINT32", CP_ATTR_REQUIRED},
      {"count", "UINT32", CP_ATTR_REQUIRED}, 
      {"address_family", "STRING", CP_ATTR_REQUIRED},
      {"src_ip", "STRING", CP_ATTR_OPTIONAL},
      {"src_port", "UINT32", CP_ATTR_OPTIONAL},
      {"dst_ip", "STRING", CP_ATTR_OPTIONAL},
      {"dst_port", "UINT32", CP_ATTR_OPTIONAL},
      {"protocol", "STRING", CP_ATTR_OPTIONAL},
      {"payload_len", "UINT32", CP_ATTR_OPTIONAL},
      CP_ATTR_BATCH_END},
     CP_NODE_LEAF,
     CP_NO_CHILD,
     nsa_cli_inject_packets},
     
    {"pcap-replay",
     "Control PCAP file replay for testing",
     CP_NO_ATTRS,
     CP_NODE_CONTAINER,
     nsa_cli_pcap_replay_commands,
     CP_NO_CALLBACK},

    CP_CMD_BATCH_END
};

/**
 * @brief Root NSA CLI command tree
 */
static cp_cmd_batch_t nsa_cli_root_commands[] = {
    {"show", 
     "Display NSA system information", 
     CP_NO_ATTRS, 
     CP_NODE_CONTAINER, 
     nsa_cli_show_commands, 
     CP_NO_CALLBACK},
     
    {"exec", 
     "Execute administrative operations", 
     CP_NO_ATTRS, 
     CP_NODE_CONTAINER, 
     nsa_cli_exec_commands, 
     CP_NO_CALLBACK},
     
    CP_CMD_BATCH_END
};

/* ========================================================================
 * CLI Session Management
 * ======================================================================== */

/**
 * @brief Handle incoming CLI commands
 * 
 * Processes CLI commands received from client connections and
 * dispatches them to appropriate handlers.
 * 
 * @param[in] command  Command string to process
 * @param[in] cnx      Connection context
 */
void nsa_cli_command_handler(char *command, dl_cnx_t *cnx) {
    cp_cb_data_t cb_data;

    if (command == NULL || cnx == NULL) {
        NSA_LOG_ERROR("Invalid parameters for CLI command handler");
        return;
    }

    if (g_nsa_root == NULL || g_nsa_root->cli_parser == NULL) {
        NSA_LOG_ERROR("NSA CLI system not initialized");
        if (cnx->fh != NULL) {
            fprintf(cnx->fh, "Error: CLI system not initialized\r\n");
        }
        return;
    }

    NSA_LOG_DEBUG("Processing CLI command: %s", command);
    
    /* Process command through command parser */
    cp_handle_generic_dcli_cmd_cb(g_nsa_root->cli_parser, cnx, &cb_data, command);
}

/**
 * @brief Callback for CLI session close events
 * 
 * Handles cleanup when a CLI session is terminated.
 * 
 * @param[in] cnx  Connection context being closed
 */
static void nsa_cli_session_close_callback(dl_cnx_t *cnx) {
    if (cnx != NULL) {
        NSA_LOG_DEBUG("CLI session closed (connection: %p)", cnx);
    } else {
        NSA_LOG_WARNING("CLI session close callback with NULL connection");
    }
}

/**
 * @brief Add session context for new CLI connections
 * 
 * Sets up connection context and callbacks for new CLI sessions.
 * 
 * @param[in] cnx      Connection context
 * @param[in] context  User context data
 */
void nsa_cli_add_session_context(dl_cnx_t *cnx, void *context) {
    if (cnx == NULL) {
        NSA_LOG_ERROR("Cannot add session context to NULL connection");
        return;
    }

    NSA_LOG_DEBUG("Adding CLI session context (connection: %p)", cnx);
    
    cnx->user_data = context;
    cnx->disconnect_callback = nsa_cli_session_close_callback;
}

/* ========================================================================
 * CLI Initialization
 * ======================================================================== */

/**
 * @brief Initialize NSA CLI subsystem
 * 
 * Sets up the command parser and registers all CLI commands.
 * 
 * @param[in] root  Pointer to NSA root context
 * @return 0 on success, -1 on failure
 */
int nsa_cli_init(nsa_root_t *root) {
    if (root == NULL) {
        NSA_LOG_ERROR("Invalid root context for CLI initialization");
        return -1;
    }

    NSA_LOG_INFO("Initializing NSA CLI subsystem");

    /* Initialize command parser */
    root->cli_parser = cp_new(VALIDATE_TYPES_GLOBAL_PATH);
    if (root->cli_parser == NULL) {
        NSA_LOG_ERROR("Failed to create CLI command parser");
        return -1;
    }

    /* Register all CLI commands */
    cp_cmd_add_batch(root->cli_parser, NULL, nsa_cli_root_commands);

    NSA_LOG_INFO("NSA CLI subsystem initialized successfully");
    NSA_LOG_DEBUG("Registered CLI command tree with %zu command categories", 
                 sizeof(nsa_cli_root_commands) / sizeof(cp_cmd_batch_t) - 1);

    return 0;
}

/**
 * @brief Cleanup NSA CLI subsystem
 * 
 * Releases CLI parser resources and resets state.
 * 
 * @param[in] root  Pointer to NSA root context
 */
void nsa_cli_cleanup(nsa_root_t *root) {
    if (root == NULL) {
        NSA_LOG_WARNING("Attempting to cleanup CLI with NULL root context");
        return;
    }

    NSA_LOG_INFO("Cleaning up NSA CLI subsystem");

    if (root->cli_parser != NULL) {
        /* TODO: Add proper parser cleanup if CP library provides it */
        root->cli_parser = NULL;
        NSA_LOG_DEBUG("CLI parser cleaned up");
    }

    NSA_LOG_INFO("NSA CLI subsystem cleanup completed");
}