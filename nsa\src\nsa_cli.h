/**
 * @file nsa_cli.h
 * @brief NSA Command Line Interface Header
 * 
 * This header file defines the public interface for NSA CLI operations
 * including command processing, session management, and initialization.
 * 
 * <AUTHOR> Li
 * @date 2025
 * @copyright Calix Inc.
 */

#ifndef NSA_CLI_H
#define NSA_CLI_H

/* ========================================================================
 * System Includes
 * ======================================================================== */
#include <daemonlib.h>
#include <cmd_parser.h>

/* ========================================================================
 * Forward Declarations
 * ======================================================================== */
/* nsa_root_t is defined in nsa.h */

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * Public Function Declarations
 * ======================================================================== */

/**
 * @brief Initialize NSA CLI subsystem
 * 
 * @param[in] root  Pointer to NSA root context
 * @return 0 on success, -1 on failure
 */
int nsa_cli_init(nsa_root_t *root);

/**
 * @brief Cleanup NSA CLI subsystem
 * 
 * @param[in] root  Pointer to NSA root context
 */
void nsa_cli_cleanup(nsa_root_t *root);

/**
 * @brief Handle incoming CLI commands
 * 
 * @param[in] command  Command string to process
 * @param[in] cnx      Connection context
 */
void nsa_cli_command_handler(char *command, dl_cnx_t *cnx);

/**
 * @brief Add session context for new CLI connections
 * 
 * @param[in] cnx      Connection context
 * @param[in] context  User context data
 */
void nsa_cli_add_session_context(dl_cnx_t *cnx, void *context);

#ifdef __cplusplus
}
#endif

#endif /* NSA_CLI_H */
