#ifndef _NSA_CLI_COMMANDS_H_
#define _NSA_CLI_COMMANDS_H_

#include "nsa.h"
#include <cmd_parser.h>

#ifdef __cplusplus
extern "C" {
#endif

// CLI command result codes
typedef enum {
    NSA_CLI_SUCCESS = 0,
    NSA_CLI_ERROR_INVALID_PARAMS = -1,
    NSA_CLI_ERROR_NOT_FOUND = -2,
    NSA_CLI_ERROR_PERMISSION_DENIED = -3,
    NSA_CLI_ERROR_RESOURCE_BUSY = -4,
    NSA_CLI_ERROR_INTERNAL = -5
} nsa_cli_result_t;

// CLI context structure
typedef struct {
    dl_cnx_t *connection;
    char *output_buffer;
    size_t buffer_size;
    size_t buffer_used;
    uint32_t privileges;  // User privilege mask
    bool verbose_mode;
    bool json_output;
} nsa_cli_context_t;

// =========================
// System Management Commands
// =========================

// show system status
int nsa_cli_show_system_status(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show system version
int nsa_cli_show_system_version(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show system performance
int nsa_cli_show_system_performance(nsa_cli_context_t *ctx, int argc, char *argv[]);

// restart system
int nsa_cli_restart_system(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Session Management Commands
// =========================

// show sessions [active|all] [brief|detail] [filter <criteria>]
int nsa_cli_show_sessions(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show session <session-id>
int nsa_cli_show_session_detail(nsa_cli_context_t *ctx, int argc, char *argv[]);

// clear session <session-id|all>
int nsa_cli_clear_sessions(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show session statistics
int nsa_cli_show_session_stats(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Policy Management Commands
// =========================

// show policy rules [zone <zone-name>] [user <username>] [app <app-name>]
int nsa_cli_show_policy_rules(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show policy rule <rule-id>
int nsa_cli_show_policy_rule_detail(nsa_cli_context_t *ctx, int argc, char *argv[]);

// configure policy rule <rule-id> [enable|disable]
int nsa_cli_configure_policy_rule(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show policy statistics
int nsa_cli_show_policy_stats(nsa_cli_context_t *ctx, int argc, char *argv[]);

// test policy <src-ip> <dst-ip> <src-port> <dst-port> <protocol> [app <app-id>] [user <user-id>]
int nsa_cli_test_policy(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Zone Management Commands
// =========================

// show zones
int nsa_cli_show_zones(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show zone <zone-name>
int nsa_cli_show_zone_detail(nsa_cli_context_t *ctx, int argc, char *argv[]);

// configure zone <zone-name> security-level <1-100>
int nsa_cli_configure_zone(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Application Control Commands
// =========================

// show applications [category <category>] [risk <low|medium|high>]
int nsa_cli_show_applications(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show application <app-name|app-id>
int nsa_cli_show_application_detail(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show application statistics [top <count>]
int nsa_cli_show_app_stats(nsa_cli_context_t *ctx, int argc, char *argv[]);

// update application signatures
int nsa_cli_update_app_signatures(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Threat Intelligence Commands
// =========================

// show threats [severity <level>] [category <category>] [active]
int nsa_cli_show_threats(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show threat <threat-id>
int nsa_cli_show_threat_detail(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show threat-feeds
int nsa_cli_show_threat_feeds(nsa_cli_context_t *ctx, int argc, char *argv[]);

// update threat-feeds [feed-id <id>]
int nsa_cli_update_threat_feeds(nsa_cli_context_t *ctx, int argc, char *argv[]);

// lookup threat ip <ip-address>
int nsa_cli_lookup_threat_ip(nsa_cli_context_t *ctx, int argc, char *argv[]);

// lookup threat domain <domain-name>
int nsa_cli_lookup_threat_domain(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show threat statistics
int nsa_cli_show_threat_stats(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// User Management Commands
// =========================

// show users [group <group-name>] [zone <zone-name>]
int nsa_cli_show_users(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show user <username|user-id>
int nsa_cli_show_user_detail(nsa_cli_context_t *ctx, int argc, char *argv[]);

// configure user <username> quota <bytes-per-day>
int nsa_cli_configure_user(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show user activity [top <count>] [time-range <start> <end>]
int nsa_cli_show_user_activity(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Logging and Monitoring Commands
// =========================

// show logs [type <event-type>] [severity <level>] [time-range <start> <end>] [count <num>]
int nsa_cli_show_logs(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show log-config
int nsa_cli_show_log_config(nsa_cli_context_t *ctx, int argc, char *argv[]);

// configure logging [destination <dest>] [format <format>] [severity <level>]
int nsa_cli_configure_logging(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show alerts [active] [severity <level>]
int nsa_cli_show_alerts(nsa_cli_context_t *ctx, int argc, char *argv[]);

// clear alerts [alert-id <id>|all]
int nsa_cli_clear_alerts(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Performance and Statistics Commands
// =========================

// show performance [real-time] [history <hours>]
int nsa_cli_show_performance(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show statistics [sessions|threats|applications|policies] [time-range <start> <end>]
int nsa_cli_show_statistics(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show top [sessions|applications|threats|users|policies] [count <num>]
int nsa_cli_show_top(nsa_cli_context_t *ctx, int argc, char *argv[]);

// monitor [sessions|performance|threats] [interval <seconds>]
int nsa_cli_monitor(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Debugging and Troubleshooting Commands
// =========================

// debug session <session-id> [packet-capture] [verbose]
int nsa_cli_debug_session(nsa_cli_context_t *ctx, int argc, char *argv[]);

// debug dpi [app-detection] [threat-detection] [verbose]
int nsa_cli_debug_dpi(nsa_cli_context_t *ctx, int argc, char *argv[]);

// debug policy <src-ip> <dst-ip> <protocol> [step-by-step]
int nsa_cli_debug_policy(nsa_cli_context_t *ctx, int argc, char *argv[]);

// show debug logs [component <component>] [level <level>] [count <num>]
int nsa_cli_show_debug_log(nsa_cli_context_t *ctx, int argc, char *argv[]);

// packet-capture <interface> [filter <filter-expression>] [duration <seconds>] [count <packets>]
int nsa_cli_packet_capture(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Configuration Management Commands
// =========================

// show configuration [component <component>]
int nsa_cli_show_configuration(nsa_cli_context_t *ctx, int argc, char *argv[]);

// save configuration [file <filename>]
int nsa_cli_save_configuration(nsa_cli_context_t *ctx, int argc, char *argv[]);

// load configuration <filename>
int nsa_cli_load_configuration(nsa_cli_context_t *ctx, int argc, char *argv[]);

// backup configuration
int nsa_cli_backup_configuration(nsa_cli_context_t *ctx, int argc, char *argv[]);

// =========================
// Utility Functions
// =========================

// Initialize CLI command system
int nsa_cli_init(void);

// Cleanup CLI command system
void nsa_cli_cleanup(void);

// Register all CLI commands
int nsa_cli_register_commands(cp_context_t *cp);

// Helper functions for output formatting
int nsa_cli_output_table(nsa_cli_context_t *ctx, const char *headers[], const char *data[][16], int rows, int cols);
int nsa_cli_output_json(nsa_cli_context_t *ctx, const char *json_string);
int nsa_cli_output_text(nsa_cli_context_t *ctx, const char *text);
int nsa_cli_output_error(nsa_cli_context_t *ctx, const char *error_msg);

// Helper functions for parameter validation
bool nsa_cli_validate_ip(const char *ip_str, uint32_t *ip_addr);
bool nsa_cli_validate_port(const char *port_str, uint16_t *port);
bool nsa_cli_validate_time_range(const char *start_str, const char *end_str, time_t *start_time, time_t *end_time);

#ifdef __cplusplus
}
#endif

#endif  // _NSA_CLI_COMMANDS_H_