#ifndef _NSA_DPI_H_
#define _NSA_DPI_H_

#include <stdint.h>
#include <dpif.h>

#ifdef __cplusplus
extern "C" {
#endif

// Application identification results
typedef struct {
    uint16_t app_id;           // Application ID (HTTP, SSH, etc.)
    uint16_t app_category;     // Category (Social, Business, etc.)
    uint16_t app_subcategory;  // Sub-category
    uint16_t app_technology;   // Technology (Browser-based, Client-Server, etc.)
    uint16_t app_risk;         // Risk level (1-5)
    char app_name[32];         // Application name
    char app_vendor[32];       // Vendor name
} nsa_app_result_t;

// Protocol parsing results
typedef struct {
    uint8_t l7_proto;          // Layer 7 protocol
    uint16_t l7_payload_offset; // Offset to L7 payload
    uint16_t l7_payload_len;   // L7 payload length
    char hostname[256];        // Extracted hostname (HTTP, TLS SNI)
    char url[512];             // Full URL for HTTP
    char user_agent[256];      // User agent string
    uint8_t ssl_version;       // SSL/TLS version
    char ssl_cipher[64];       // SSL cipher suite
} nsa_protocol_result_t;

// Threat detection results
typedef struct {
    uint16_t threat_id;        // Threat signature ID
    uint8_t threat_severity;   // Severity (1-10)
    uint8_t threat_category;   // Category (Malware, Exploit, etc.)
    char threat_name[128];     // Threat name
    char threat_description[256]; // Description
    uint32_t threat_signature_id; // Signature that matched
} nsa_threat_result_t;

// DPI analysis context per session
typedef struct {
    uint32_t session_id;
    uint32_t packets_analyzed;
    uint32_t bytes_analyzed;
    uint8_t analysis_stage;    // 0=initial, 1=in_progress, 2=complete
    
    // Results
    nsa_app_result_t app_result;
    nsa_protocol_result_t proto_result;
    nsa_threat_result_t threat_result;
    
    // Analysis state
    uint8_t need_more_packets; // Flag indicating more packets needed
    uint16_t min_packets_needed; // Minimum packets for confident analysis
    uint32_t analysis_timeout; // Timeout for analysis completion
} nsa_dpi_context_t;

// DPI engine interface
int nsa_dpi_init(void);
void nsa_dpi_cleanup(void);
int nsa_dpi_analyze_packet(nsa_dpi_context_t *ctx, struct dpi_packet *packet);
int nsa_dpi_get_verdict(nsa_dpi_context_t *ctx);
void nsa_dpi_reset_context(nsa_dpi_context_t *ctx);

// Application signature database management
int nsa_dpi_load_app_signatures(const char *signature_file);
int nsa_dpi_update_app_signatures(void);

// Threat signature database management  
int nsa_dpi_load_threat_signatures(const char *signature_file);
int nsa_dpi_update_threat_signatures(void);

#ifdef __cplusplus
}
#endif

#endif // _NSA_DPI_H_ 