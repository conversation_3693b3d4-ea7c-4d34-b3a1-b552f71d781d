/**
 * @file nsa_enhanced_monitor.c
 * @brief Enhanced NSA Monitoring Dashboard with Performance Analysis
 * 
 * This file provides an advanced monitoring dashboard that integrates
 * with the performance analyzer to provide real-time insights,
 * bottleneck detection, and optimization recommendations.
 */

#include <ncurses.h>
#include <pthread.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/time.h>

#include "nsa.h"
#include "nsa_performance_analyzer.h"
#include "nsa_logging.h"

#define NSA_MONITOR_SOCKET_PATH "/tmp/nsa_monitor.sock"
#define REFRESH_INTERVAL_MS 1000
#define MAX_DISPLAY_BOTTLENECKS 10
#define MAX_DISPLAY_RECOMMENDATIONS 5

/* Enhanced monitoring context */
typedef struct {
    bool running;
    bool paused;
    int current_tab;
    
    /* Performance data */
    nsa_enhanced_metrics_t current_metrics;
    nsa_bottleneck_t bottlenecks[MAX_DISPLAY_BOTTLENECKS];
    uint32_t bottleneck_count;
    nsa_optimization_recommendation_t recommendations[MAX_DISPLAY_RECOMMENDATIONS];
    uint32_t recommendation_count;
    
    /* Display state */
    int selected_bottleneck;
    int selected_recommendation;
    bool show_help;
    
    /* Connection */
    int socket_fd;
    struct timeval last_update;
    
} nsa_enhanced_monitor_t;

/* Tab definitions */
typedef enum {
    TAB_OVERVIEW = 0,
    TAB_PERFORMANCE,
    TAB_BOTTLENECKS,
    TAB_RECOMMENDATIONS,
    TAB_TRENDS,
    TAB_COUNT
} monitor_tab_t;

static const char *tab_names[TAB_COUNT] = {
    "Overview",
    "Performance",
    "Bottlenecks", 
    "Recommendations",
    "Trends"
};

/* Global monitor context */
static nsa_enhanced_monitor_t g_monitor = {0};

/**
 * @brief Initialize ncurses interface
 */
static void init_display(void) {
    initscr();
    cbreak();
    noecho();
    nodelay(stdscr, TRUE);
    keypad(stdscr, TRUE);
    curs_set(0);
    
    /* Initialize colors if available */
    if (has_colors()) {
        start_color();
        init_pair(1, COLOR_GREEN, COLOR_BLACK);   /* Normal */
        init_pair(2, COLOR_YELLOW, COLOR_BLACK);  /* Warning */
        init_pair(3, COLOR_RED, COLOR_BLACK);     /* Critical */
        init_pair(4, COLOR_CYAN, COLOR_BLACK);    /* Info */
        init_pair(5, COLOR_MAGENTA, COLOR_BLACK); /* Selected */
    }
}

/**
 * @brief Draw tab bar
 */
static void draw_tab_bar(void) {
    int x = 2;
    
    mvprintw(0, 0, "NSA Enhanced Performance Monitor");
    mvprintw(1, 0, "================================================================================");
    
    for (int i = 0; i < TAB_COUNT; i++) {
        if (i == g_monitor.current_tab) {
            attron(COLOR_PAIR(5) | A_BOLD);
            mvprintw(2, x, "[%s]", tab_names[i]);
            attroff(COLOR_PAIR(5) | A_BOLD);
        } else {
            mvprintw(2, x, " %s ", tab_names[i]);
        }
        x += strlen(tab_names[i]) + 3;
    }
    
    mvprintw(3, 0, "================================================================================");
}

/**
 * @brief Draw overview tab
 */
static void draw_overview_tab(void) {
    int y = 5;
    
    mvprintw(y++, 2, "System Overview");
    mvprintw(y++, 2, "---------------");
    y++;
    
    /* Traffic metrics */
    mvprintw(y++, 2, "Traffic:");
    mvprintw(y++, 4, "Current PPS: %lu", g_monitor.current_metrics.traffic.current_pps);
    mvprintw(y++, 4, "Peak PPS:    %lu", g_monitor.current_metrics.traffic.peak_pps);
    mvprintw(y++, 4, "Average PPS: %lu", g_monitor.current_metrics.traffic.avg_pps);
    y++;
    
    /* Session metrics */
    mvprintw(y++, 2, "Sessions:");
    mvprintw(y++, 4, "Active Sessions:     %u", g_monitor.current_metrics.sessions.active_sessions);
    mvprintw(y++, 4, "Session Setup Rate:  %u/s", g_monitor.current_metrics.sessions.session_setup_rate);
    mvprintw(y++, 4, "Session Table Load:  %u%%", g_monitor.current_metrics.sessions.session_table_load);
    y++;
    
    /* Resource utilization */
    mvprintw(y++, 2, "Resources:");
    mvprintw(y++, 4, "Memory Utilization: %u%%", g_monitor.current_metrics.resources.memory_utilization);
    mvprintw(y++, 4, "Cache Hit Rate:     %u%%", g_monitor.current_metrics.resources.cache_hit_rate);
    y++;
    
    /* DPI metrics */
    mvprintw(y++, 2, "DPI Performance:");
    mvprintw(y++, 4, "Processing Time:    %u ns", g_monitor.current_metrics.dpi.dpi_processing_time_ns);
    mvprintw(y++, 4, "Apps Detected/sec:  %u", g_monitor.current_metrics.dpi.apps_detected_per_sec);
    mvprintw(y++, 4, "Threats/sec:        %u", g_monitor.current_metrics.dpi.threats_detected_per_sec);
    y++;
    
    /* Memory pools */
    mvprintw(y++, 2, "Memory Pools:");
    mvprintw(y++, 4, "Mbuf Pool:    %u%%", g_monitor.current_metrics.memory_pools.mbuf_pool_utilization);
    mvprintw(y++, 4, "Session Pool: %u%%", g_monitor.current_metrics.memory_pools.session_pool_utilization);
    mvprintw(y++, 4, "Work Pool:    %u%%", g_monitor.current_metrics.memory_pools.work_pool_utilization);
}

/**
 * @brief Draw performance tab
 */
static void draw_performance_tab(void) {
    int y = 5;
    
    mvprintw(y++, 2, "Performance Metrics");
    mvprintw(y++, 2, "-------------------");
    y++;
    
    /* Latency metrics */
    mvprintw(y++, 2, "Latency (nanoseconds):");
    mvprintw(y++, 4, "Average:  %u ns", g_monitor.current_metrics.latency.avg_packet_latency_ns);
    mvprintw(y++, 4, "Minimum:  %u ns", g_monitor.current_metrics.latency.min_packet_latency_ns);
    mvprintw(y++, 4, "Maximum:  %u ns", g_monitor.current_metrics.latency.max_packet_latency_ns);
    mvprintw(y++, 4, "P95:      %u ns", g_monitor.current_metrics.latency.p95_latency_ns);
    mvprintw(y++, 4, "P99:      %u ns", g_monitor.current_metrics.latency.p99_latency_ns);
    y++;
    
    /* Queue metrics */
    mvprintw(y++, 2, "Queue Depths:");
    for (int i = 0; i < 8 && y < LINES - 5; i++) {
        mvprintw(y++, 4, "RX Queue %d:     %u", i, g_monitor.current_metrics.queues.rx_queue_depth[i]);
    }
    y++;
    
    /* CPU utilization (first 8 cores) */
    mvprintw(y++, 2, "CPU Utilization:");
    for (int i = 0; i < 8 && y < LINES - 5; i++) {
        int cpu_util = g_monitor.current_metrics.resources.cpu_utilization[i];
        int color = cpu_util > 90 ? 3 : (cpu_util > 75 ? 2 : 1);
        
        attron(COLOR_PAIR(color));
        mvprintw(y++, 4, "CPU %d: %3d%%", i, cpu_util);
        attroff(COLOR_PAIR(color));
    }
}

/**
 * @brief Draw bottlenecks tab
 */
static void draw_bottlenecks_tab(void) {
    int y = 5;
    
    mvprintw(y++, 2, "Performance Bottlenecks");
    mvprintw(y++, 2, "-----------------------");
    y++;
    
    if (g_monitor.bottleneck_count == 0) {
        attron(COLOR_PAIR(1));
        mvprintw(y++, 2, "No performance bottlenecks detected!");
        attroff(COLOR_PAIR(1));
        return;
    }
    
    mvprintw(y++, 2, "Detected %u bottlenecks:", g_monitor.bottleneck_count);
    y++;
    
    for (uint32_t i = 0; i < g_monitor.bottleneck_count && y < LINES - 8; i++) {
        const nsa_bottleneck_t *bottleneck = &g_monitor.bottlenecks[i];
        
        /* Color based on severity */
        int color = 1;
        switch (bottleneck->severity) {
            case NSA_SEVERITY_CRITICAL: color = 3; break;
            case NSA_SEVERITY_HIGH:     color = 3; break;
            case NSA_SEVERITY_MEDIUM:   color = 2; break;
            case NSA_SEVERITY_LOW:      color = 1; break;
        }
        
        if (i == g_monitor.selected_bottleneck) {
            attron(COLOR_PAIR(5) | A_BOLD);
        } else {
            attron(COLOR_PAIR(color));
        }
        
        mvprintw(y++, 4, "%d. %s", i + 1, bottleneck->description);
        mvprintw(y++, 6, "Location: %s", bottleneck->location);
        mvprintw(y++, 6, "Impact: %.1f", bottleneck->impact_score);
        
        if (i == g_monitor.selected_bottleneck) {
            attroff(COLOR_PAIR(5) | A_BOLD);
        } else {
            attroff(COLOR_PAIR(color));
        }
        y++;
    }
    
    mvprintw(LINES - 3, 2, "Use UP/DOWN arrows to select, ENTER for details");
}

/**
 * @brief Draw recommendations tab
 */
static void draw_recommendations_tab(void) {
    int y = 5;
    
    mvprintw(y++, 2, "Optimization Recommendations");
    mvprintw(y++, 2, "-----------------------------");
    y++;
    
    if (g_monitor.recommendation_count == 0) {
        attron(COLOR_PAIR(1));
        mvprintw(y++, 2, "No optimization recommendations available.");
        attroff(COLOR_PAIR(1));
        return;
    }
    
    mvprintw(y++, 2, "Generated %u recommendations:", g_monitor.recommendation_count);
    y++;
    
    for (uint32_t i = 0; i < g_monitor.recommendation_count && y < LINES - 10; i++) {
        const nsa_optimization_recommendation_t *rec = &g_monitor.recommendations[i];
        
        if (i == g_monitor.selected_recommendation) {
            attron(COLOR_PAIR(5) | A_BOLD);
        } else {
            attron(COLOR_PAIR(4));
        }
        
        mvprintw(y++, 4, "%d. %s", i + 1, rec->title);
        mvprintw(y++, 6, "Expected Improvement: %.1f%%", rec->expected_improvement);
        mvprintw(y++, 6, "Effort: %u/10, Risk: %u/10", rec->implementation_effort, rec->risk_level);
        mvprintw(y++, 6, "Auto-applicable: %s", rec->auto_applicable ? "Yes" : "No");
        
        if (i == g_monitor.selected_recommendation) {
            attroff(COLOR_PAIR(5) | A_BOLD);
        } else {
            attroff(COLOR_PAIR(4));
        }
        y++;
    }
    
    mvprintw(LINES - 3, 2, "Use UP/DOWN arrows to select, ENTER for implementation details");
}

/**
 * @brief Draw status bar
 */
static void draw_status_bar(void) {
    int y = LINES - 2;
    
    mvprintw(y, 0, "================================================================================");
    
    if (g_monitor.paused) {
        attron(COLOR_PAIR(2));
        mvprintw(y + 1, 2, "PAUSED");
        attroff(COLOR_PAIR(2));
    } else {
        attron(COLOR_PAIR(1));
        mvprintw(y + 1, 2, "RUNNING");
        attroff(COLOR_PAIR(1));
    }
    
    mvprintw(y + 1, 15, "| TAB: Switch tabs | P: Pause | Q: Quit | H: Help");
    
    /* Show timestamp */
    time_t now = time(NULL);
    char timestr[32];
    strftime(timestr, sizeof(timestr), "%H:%M:%S", localtime(&now));
    mvprintw(y + 1, COLS - 15, "Time: %s", timestr);
}

/**
 * @brief Update performance data
 */
static void update_performance_data(void) {
    if (g_monitor.paused) {
        return;
    }
    
    /* Collect enhanced metrics */
    if (nsa_collect_enhanced_metrics(&g_monitor.current_metrics) != 0) {
        NSA_LOG_WARNING("Failed to collect enhanced metrics");
        return;
    }
    
    /* Detect bottlenecks */
    nsa_detect_bottlenecks(&g_monitor.current_metrics,
                          g_monitor.bottlenecks,
                          MAX_DISPLAY_BOTTLENECKS,
                          &g_monitor.bottleneck_count);
    
    /* Generate recommendations */
    nsa_generate_recommendations(g_monitor.bottlenecks,
                                g_monitor.bottleneck_count,
                                g_monitor.recommendations,
                                MAX_DISPLAY_RECOMMENDATIONS,
                                &g_monitor.recommendation_count);
}

/**
 * @brief Main monitoring loop
 */
int nsa_enhanced_monitor_main(void) {
    /* Initialize performance analyzer */
    if (nsa_performance_analyzer_init() != 0) {
        fprintf(stderr, "Failed to initialize performance analyzer\n");
        return -1;
    }
    
    /* Initialize display */
    init_display();
    
    /* Initialize monitor state */
    g_monitor.running = true;
    g_monitor.paused = false;
    g_monitor.current_tab = TAB_OVERVIEW;
    g_monitor.selected_bottleneck = 0;
    g_monitor.selected_recommendation = 0;
    g_monitor.show_help = false;
    gettimeofday(&g_monitor.last_update, NULL);
    
    /* Main loop */
    while (g_monitor.running) {
        /* Handle input */
        int ch = getch();
        switch (ch) {
            case 'q':
            case 'Q':
                g_monitor.running = false;
                break;
                
            case 'p':
            case 'P':
                g_monitor.paused = !g_monitor.paused;
                break;
                
            case '\t':
                g_monitor.current_tab = (g_monitor.current_tab + 1) % TAB_COUNT;
                break;
                
            case KEY_UP:
                if (g_monitor.current_tab == TAB_BOTTLENECKS && g_monitor.selected_bottleneck > 0) {
                    g_monitor.selected_bottleneck--;
                } else if (g_monitor.current_tab == TAB_RECOMMENDATIONS && g_monitor.selected_recommendation > 0) {
                    g_monitor.selected_recommendation--;
                }
                break;
                
            case KEY_DOWN:
                if (g_monitor.current_tab == TAB_BOTTLENECKS && 
                    g_monitor.selected_bottleneck < g_monitor.bottleneck_count - 1) {
                    g_monitor.selected_bottleneck++;
                } else if (g_monitor.current_tab == TAB_RECOMMENDATIONS && 
                          g_monitor.selected_recommendation < g_monitor.recommendation_count - 1) {
                    g_monitor.selected_recommendation++;
                }
                break;
        }
        
        /* Update data periodically */
        struct timeval now;
        gettimeofday(&now, NULL);
        long elapsed_ms = (now.tv_sec - g_monitor.last_update.tv_sec) * 1000 +
                         (now.tv_usec - g_monitor.last_update.tv_usec) / 1000;
        
        if (elapsed_ms >= REFRESH_INTERVAL_MS) {
            update_performance_data();
            g_monitor.last_update = now;
        }
        
        /* Redraw display */
        clear();
        draw_tab_bar();
        
        switch (g_monitor.current_tab) {
            case TAB_OVERVIEW:
                draw_overview_tab();
                break;
            case TAB_PERFORMANCE:
                draw_performance_tab();
                break;
            case TAB_BOTTLENECKS:
                draw_bottlenecks_tab();
                break;
            case TAB_RECOMMENDATIONS:
                draw_recommendations_tab();
                break;
            case TAB_TRENDS:
                mvprintw(5, 2, "Trend analysis coming soon...");
                break;
        }
        
        draw_status_bar();
        refresh();
        
        usleep(50000);  /* 50ms refresh rate */
    }
    
    /* Cleanup */
    endwin();
    nsa_performance_analyzer_cleanup();
    
    return 0;
}
