/**
 * @file nsa_helper.h
 * @brief NSA Helper Thread Interface
 * 
 * This header file defines the interface for NSA helper thread operations
 * including event types, structures, and function declarations for
 * asynchronous event processing.
 * 
 * <AUTHOR> Li
 * @date 2025
 * @copyright Calix Inc.
 */

#ifndef NSA_HELPER_H
#define NSA_HELPER_H

/* ========================================================================
 * System Includes
 * ======================================================================== */
#include <stdint.h>
#include <stdbool.h>
#include <pthread.h>

/* ========================================================================
 * DPDK Includes
 * ======================================================================== */
#include <rte_ring.h>

/* ========================================================================
 * Constants and Definitions
 * ======================================================================== */

/** @brief Maximum size of event payload data */
#define NSA_EVENT_PAYLOAD_SIZE 512

/* ========================================================================
 * Type Definitions
 * ======================================================================== */

/**
 * @brief NSA event types for helper thread processing
 */
typedef enum {
    NSA_EVENT_SESSION_ENRICH,    /**< Session enrichment event */
    NSA_EVENT_SESSION_DESTROY,     /**< Session destruction event */
    NSA_EVENT_SESSION_CREATE,      /**< Session create event */
    NSA_EVENT_MAX                  /**< Maximum event type (boundary) */
} nsa_event_type_t;

/**
 * @brief NSA event structure for helper thread communication
 */
typedef struct {
    nsa_event_type_t type;                    /**< Event type */
    uint32_t session_id;                      /**< Associated session ID */
    uint8_t payload[NSA_EVENT_PAYLOAD_SIZE];  /**< Event payload data */
} nsa_event_t;

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * Public Function Declarations
 * ======================================================================== */

/**
 * @brief Initialize NSA helper thread subsystem
 * 
 * Creates DPDK memory pool and ring buffer, starts the helper thread,
 * and registers default event handlers.
 * 
 * @return 0 on success, -1 on failure
 */
int nsa_helper_init(void);

/**
 * @brief Stop NSA helper thread subsystem
 * 
 * Gracefully shuts down the helper thread and cleans up resources.
 */
void nsa_helper_stop(void);

/**
 * @brief Enqueue an event for processing by the helper thread
 * 
 * Creates an event object and enqueues it to the helper ring buffer
 * for asynchronous processing.
 * 
 * @param[in] type          Event type
 * @param[in] session_id    Session identifier
 * @param[in] payload       Event payload data (can be NULL)
 * @param[in] payload_size  Size of payload data
 * @return 0 on success, negative error code on failure
 */
int nsa_helper_event_enqueue(nsa_event_type_t type, uint32_t session_id, 
                            const void *payload, size_t payload_size);

/**
 * @brief Register an event handler for a specific event type
 * 
 * Associates a handler function with an event type for dispatch.
 * This function is primarily for internal use and extension points.
 * 
 * @param[in] type     Event type to register handler for
 * @param[in] handler  Handler function pointer
 */
void nsa_event_register_handler(nsa_event_type_t type, void (*handler)(nsa_event_t *event));

/**
 * @brief Add enriched session data to CDB
 * @param[in] event  Pointer to NSA event containing session data
 * @return 0 on success, negative error code on failure
 */
int nsa_cdb_enriched_session_add(nsa_event_t *event);

/**
 * @brief Delete enriched session data from CDB
 * @param[in] event  Pointer to NSA event containing session ID
 * @return 0 on success, negative error code on failure
 */
int nsa_cdb_enriched_session_delete(nsa_event_t *event);

/**
 * @brief enriched session data to CDB
 * @param[in] event  Pointer to NSA event containing session ID
 * @return 0 on success, negative error code on failure
 */
int nsa_cdb_enriched_session_enrich(nsa_event_t *event);

#ifdef __cplusplus
}
#endif

#endif /* NSA_HELPER_H */
