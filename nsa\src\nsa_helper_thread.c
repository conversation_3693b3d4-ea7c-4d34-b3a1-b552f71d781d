/**
 * @file nsa_helper_thread.c
 * @brief NSA Helper Thread Implementation
 * 
 * This file contains the implementation of the NSA helper thread system
 * which processes asynchronous events using DPDK ring buffers and memory pools.
 * The helper thread handles session enrichment and other background tasks.
 * 
 * Key features:
 * - Multi-producer, single-consumer event processing
 * - DPDK-based high-performance ring buffer
 * - Event type-based handler dispatch
 * - Bulk event processing for efficiency
 * 
 * <AUTHOR> Li
 * @date 2025
 * @copyright Calix Inc.
 */

/* ========================================================================
 * System Includes
 * ======================================================================== */
#include <stdio.h>
#include <unistd.h>
#include <errno.h>

/* ========================================================================
 * DPDK Includes
 * ======================================================================== */
#include <rte_mempool.h>
#include <rte_ring.h>

/* ========================================================================
 * NSA Module Includes
 * ======================================================================== */
#include "nsa.h"
#include "nsa_helper.h"

/* ========================================================================
 * Constants and Configuration
 * ======================================================================== */

/** @brief Size of the helper event ring buffer(2^17) */
#define NSA_HELPER_RING_SIZE        131072

/** @brief Size of the event memory pool */
#define NSA_HELPER_EVENT_POOL_SIZE  131072

/** @brief Number of events to process in one burst */
#define NSA_HELPER_BURST_SIZE       64

/** @brief Sleep time (microseconds) when ring is empty */
#define NSA_HELPER_SLEEP_TIME_US    100

/* ========================================================================
 * Global Variables
 * ======================================================================== */

/** @brief DPDK ring buffer for event passing */
static struct rte_ring *g_nsa_helper_ring = NULL;

/** @brief DPDK memory pool for event objects */
static struct rte_mempool *g_nsa_event_pool = NULL;

/** @brief Helper thread identifier */
static pthread_t g_nsa_helper_thread_id;

/** @brief Flag to signal helper thread shutdown */
static volatile bool g_nsa_helper_shutdown = false;

/* ========================================================================
 * Type Definitions
 * ======================================================================== */

/** @brief Event handler function pointer type */
typedef void (*nsa_event_handler_t)(nsa_event_t *event);

/** @brief Array of registered event handlers */
static nsa_event_handler_t g_nsa_event_handlers[NSA_EVENT_MAX] = {0};

/* ========================================================================
 * Event Handler Management Functions
 * ======================================================================== */

/**
 * @brief Register an event handler for a specific event type
 * 
 * Associates a handler function with an event type for dispatch.
 * 
 * @param[in] type     Event type to register handler for
 * @param[in] handler  Handler function pointer
 */
void nsa_event_register_handler(nsa_event_type_t type, nsa_event_handler_t handler) {
    if (type >= NSA_EVENT_MAX) {
        NSA_LOG_ERROR("Attempted to register handler for invalid event type: %d", type);
        return;
    }

    if (handler == NULL) {
        NSA_LOG_ERROR("Attempted to register NULL handler for event type: %d", type);
        return;
    }

    if (g_nsa_event_handlers[type] != NULL) {
        NSA_LOG_WARNING("Overwriting existing handler for event type: %d", type);
    }

    g_nsa_event_handlers[type] = handler;
    NSA_LOG_INFO("Registered handler for event type: %d", type);
}

/**
 * @brief Dispatch an event to its registered handler
 * 
 * Calls the appropriate handler function for the event type.
 * 
 * @param[in] event  Pointer to event to dispatch
 */
static inline void nsa_event_dispatch(nsa_event_t *event) {
    if (event == NULL) {
        NSA_LOG_ERROR("Attempted to dispatch NULL event");
        return;
    }

    if (event->type >= NSA_EVENT_MAX) {
        NSA_LOG_WARNING("Event with invalid type received: %d", event->type);
        return;
    }

    if (g_nsa_event_handlers[event->type] != NULL) {
        NSA_LOG_DEBUG("Dispatching event type: %d, session: %d", event->type, event->session_id);
        g_nsa_event_handlers[event->type](event);
    } else {
        NSA_LOG_WARNING("No handler registered for event type: %d, event dropped", event->type);
    }
}

/**
 * @brief Register all default NSA event handlers
 * 
 * Sets up the standard event handlers for NSA operations.
 */
static void nsa_register_default_event_handlers(void) {
    NSA_LOG_INFO("Registering default NSA event handlers");
    
    /* Session enrichment handler */
    nsa_event_register_handler(NSA_EVENT_SESSION_CREATE, 
                              (nsa_event_handler_t)nsa_cdb_enriched_session_add);
    
    /* Session destruction handler */  
    nsa_event_register_handler(NSA_EVENT_SESSION_DESTROY, 
                              (nsa_event_handler_t)nsa_cdb_enriched_session_delete);

    /* Session enrichment handler */  
    nsa_event_register_handler(NSA_EVENT_SESSION_ENRICH, 
                              (nsa_event_handler_t)nsa_cdb_enriched_session_enrich);

    NSA_LOG_INFO("Default event handlers registered successfully");
}

/* ========================================================================
 * Helper Thread Implementation
 * ======================================================================== */

/**
 * @brief Main helper thread function
 * 
 * This is the main loop for the NSA helper thread. It continuously
 * processes events from the ring buffer in bursts for efficiency.
 * 
 * @param[in] arg  Thread argument (unused)
 * @return NULL when thread exits
 */
static void *nsa_helper_thread_main(void *arg) {
    void *events[NSA_HELPER_BURST_SIZE];
    unsigned int events_processed = 0;
    unsigned int total_events_processed = 0;

    NSA_UNUSED(arg);

    NSA_LOG_INFO("NSA helper thread started (PID: %d, TID: %ld)", 
                getpid(), pthread_self());

    while (!g_nsa_helper_shutdown) {
        /* Dequeue a burst of events from the ring buffer */
        events_processed = rte_ring_sc_dequeue_burst(g_nsa_helper_ring, 
                                                    events, 
                                                    NSA_HELPER_BURST_SIZE, 
                                                    NULL);

        if (events_processed > 0) {
            NSA_LOG_DEBUG("Processing %u events in burst", events_processed);
            
            /* Process each event in the burst */
            for (uint32_t i = 0; i < events_processed; i++) {
                nsa_event_dispatch((nsa_event_t*)events[i]);
            }

            /* Return event objects to memory pool */
            rte_mempool_put_bulk(g_nsa_event_pool, events, events_processed);
            
            total_events_processed += events_processed;
            
            if (total_events_processed % 1000 == 0) {
                NSA_LOG_DEBUG("Total events processed: %u", total_events_processed);
            }
        } else {
            /* No events available, yield CPU briefly */
            usleep(NSA_HELPER_SLEEP_TIME_US);
        }
    }

    NSA_LOG_INFO("NSA helper thread exiting (total events processed: %u)", 
                total_events_processed);
    return NULL;
}

/* ========================================================================
 * Public API Functions
 * ======================================================================== */

/**
 * @brief Enqueue an event for processing by the helper thread
 * 
 * Creates an event object and enqueues it to the helper ring buffer
 * for asynchronous processing.
 * 
 * @param[in] type          Event type
 * @param[in] session_id    Session identifier
 * @param[in] payload       Event payload data (can be NULL)
 * @param[in] payload_size  Size of payload data
 * @return 0 on success, negative error code on failure
 */
int nsa_helper_event_enqueue(nsa_event_type_t type, uint32_t session_id, 
                            const void *payload, size_t payload_size) {
    nsa_event_t *event = NULL;
    int ret = 0;

    /* Validate event type */
    if (type >= NSA_EVENT_MAX) {
        NSA_LOG_ERROR("Invalid event type: %d", type);
        return -EINVAL;
    }

    /* Validate payload size */
    if (payload_size > NSA_EVENT_PAYLOAD_SIZE) {
        NSA_LOG_ERROR("Event payload size %zu exceeds maximum %d for type %d",
                     payload_size, NSA_EVENT_PAYLOAD_SIZE, type);
        return -EINVAL;
    }

    /* Check if helper system is initialized */
    if (g_nsa_event_pool == NULL || g_nsa_helper_ring == NULL) {
        NSA_LOG_ERROR("Helper system not initialized");
        return -ENOTTY;
    }

    /* Get event object from memory pool */
    ret = rte_mempool_get(g_nsa_event_pool, (void**)&event);
    if (unlikely(ret != 0)) {
        NSA_LOG_WARNING("Failed to get event object from mempool (type: %d), event dropped", type);
        return -ENOMEM;
    }

    /* Initialize event object */
    event->type = type;
    event->session_id = session_id;
    
    /* Copy payload if provided */
    if (payload != NULL && payload_size > 0) {
        memcpy(event->payload, payload, payload_size);
    }

    /* Enqueue event to ring buffer */
    ret = rte_ring_mp_enqueue(g_nsa_helper_ring, event);
    if (unlikely(ret != 0)) {
        NSA_LOG_WARNING("Event ring full, dropping event type %d for session %d", 
                       type, session_id);
        rte_mempool_put(g_nsa_event_pool, event);
        return -ENOBUFS;
    }

    NSA_LOG_DEBUG("Successfully enqueued event type %d for session %d", type, session_id);
    return 0;
}

/**
 * @brief Initialize NSA helper thread subsystem
 * 
 * Creates DPDK memory pool and ring buffer, starts the helper thread,
 * and registers default event handlers.
 * 
 * @return 0 on success, -1 on failure
 */
int nsa_helper_init(void) {
    int ret = 0;

    NSA_LOG_INFO("Initializing NSA helper thread subsystem");

    /* Create memory pool for event objects */
    g_nsa_event_pool = rte_mempool_create("NSA_HELPER_EVENT_POOL",
                                         NSA_HELPER_EVENT_POOL_SIZE,
                                         sizeof(nsa_event_t),
                                         0,        /* Cache size */
                                         0,        /* Private data size */
                                         NULL,     /* MP init function */
                                         NULL,     /* MP init args */
                                         NULL,     /* Object init function */
                                         NULL,     /* Object init args */
                                         rte_socket_id(),
                                         MEMPOOL_F_SP_PUT);
    if (g_nsa_event_pool == NULL) {
        NSA_LOG_ERROR("Failed to create helper event memory pool");
        return -1;
    }
    NSA_LOG_INFO("Created helper event memory pool (size: %d)", NSA_HELPER_EVENT_POOL_SIZE);

    /* Create Multi-Producer, Single-Consumer ring buffer */
    g_nsa_helper_ring = rte_ring_create("NSA_HELPER_RING", 
                                       NSA_HELPER_RING_SIZE, 
                                       rte_socket_id(), 
                                       0);
    if (g_nsa_helper_ring == NULL) {
        NSA_LOG_ERROR("Failed to create helper ring buffer");
        rte_mempool_free(g_nsa_event_pool);
        g_nsa_event_pool = NULL;
        return -1;
    }
    NSA_LOG_INFO("Created helper ring buffer (size: %d)", NSA_HELPER_RING_SIZE);

    /* Initialize shutdown flag */
    g_nsa_helper_shutdown = false;

    /* Create helper thread */
    ret = pthread_create(&g_nsa_helper_thread_id, NULL, nsa_helper_thread_main, NULL);
    if (ret != 0) {
        NSA_LOG_ERROR("Failed to create helper thread: %s", strerror(ret));
        g_nsa_helper_shutdown = true;
        rte_ring_free(g_nsa_helper_ring);
        rte_mempool_free(g_nsa_event_pool);
        g_nsa_helper_ring = NULL;
        g_nsa_event_pool = NULL;
        return -1;
    }

    /* Register default event handlers */
    nsa_register_default_event_handlers();

    NSA_LOG_INFO("NSA helper thread subsystem initialized successfully");
    return 0;
}

/**
 * @brief Stop NSA helper thread subsystem
 * 
 * Gracefully shuts down the helper thread and cleans up resources.
 */
void nsa_helper_stop(void) {
    if (g_nsa_helper_shutdown) {
        NSA_LOG_INFO("Helper thread already stopped");
        return;
    }

    NSA_LOG_INFO("Stopping NSA helper thread subsystem");

    /* Signal helper thread to stop */
    g_nsa_helper_shutdown = true;

    /* Wait for helper thread to exit */
    if (pthread_join(g_nsa_helper_thread_id, NULL) != 0) {
        NSA_LOG_WARNING("Failed to join helper thread, continuing with cleanup");
    }

    /* Cleanup DPDK resources */
    if (g_nsa_helper_ring != NULL) {
        rte_ring_free(g_nsa_helper_ring);
        g_nsa_helper_ring = NULL;
        NSA_LOG_DEBUG("Helper ring buffer freed");
    }

    if (g_nsa_event_pool != NULL) {
        rte_mempool_free(g_nsa_event_pool);
        g_nsa_event_pool = NULL;
        NSA_LOG_DEBUG("Helper event memory pool freed");
    }

    /* Clear event handlers */
    memset(g_nsa_event_handlers, 0, sizeof(g_nsa_event_handlers));

    NSA_LOG_INFO("NSA helper thread subsystem stopped successfully");
}