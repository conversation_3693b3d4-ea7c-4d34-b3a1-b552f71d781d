/**
 * @file nsa_log.c
 * @brief Implements a simple, temporary in-memory event logging system.
 */

#include <stdio.h>
#include <string.h>
#include <time.h>
#include <stdarg.h>
#include <pthread.h>
#include "nsa.h"

// --- Configuration for the simple logger ---
#define MAX_LOG_ENTRIES 10
#define MAX_LOG_MSG_LEN 256

// --- Global log buffer and control variables ---
static pthread_mutex_t g_log_buffer_mutex = PTHREAD_MUTEX_INITIALIZER;
static char g_log_buffer[MAX_LOG_ENTRIES][MAX_LOG_MSG_LEN];
static int g_log_buffer_idx = 0;
static int g_log_total_count = 0;

/**
 * @brief [PUBLIC API] Submits a new log event to the simple in-memory buffer.
 * This function is thread-safe.
 */
void nsa_log_event_simple(const char* format, ...)
{
    char temp_buffer[MAX_LOG_MSG_LEN];
    char final_log_line[MAX_LOG_MSG_LEN];
    time_t now;
    struct tm time_info;

    // 1. Format the message content safely
    va_list args;
    va_start(args, format);
    vsnprintf(temp_buffer, sizeof(temp_buffer), format, args);
    va_end(args);

    // 2. Add a timestamp
    now = time(NULL);
    localtime_r(&now, &time_info);
    strftime(final_log_line, sizeof(final_log_line), "[%Y-%m-%d %H:%M:%S] ", &time_info);
    // 3. Append the message safely
    strncat(final_log_line, temp_buffer, sizeof(final_log_line) - strlen(final_log_line) - 1);

    // 4. Lock and write to the circular buffer
    pthread_mutex_lock(&g_log_buffer_mutex);

    // Use snprintf for a final safe copy into the buffer
    snprintf(g_log_buffer[g_log_buffer_idx], MAX_LOG_MSG_LEN, "%s", final_log_line);
    
    g_log_buffer_idx = (g_log_buffer_idx + 1) % MAX_LOG_ENTRIES;
    if (g_log_total_count < MAX_LOG_ENTRIES) {
        g_log_total_count++;
    }

    pthread_mutex_unlock(&g_log_buffer_mutex);
}

/**
 * @brief [PUBLIC API] Retrieves the latest logs for the monitor.
 */
void nsa_log_get_latest(nsa_monitor_event_log_t* logs_dest, int max_logs)
{
    pthread_mutex_lock(&g_log_buffer_mutex);

    memset(logs_dest, 0, sizeof(nsa_monitor_event_log_t) * max_logs);

    int num_to_copy = (g_log_total_count < max_logs) ? g_log_total_count : max_logs;

    for (int i = 0; i < num_to_copy; ++i) {
        int src_idx = (g_log_buffer_idx - 1 - i + MAX_LOG_ENTRIES) % MAX_LOG_ENTRIES;
        
        char* message_start = strchr(g_log_buffer[src_idx], ']');
        if (message_start && message_start[1] == ' ') {
            message_start += 2; 
            // Use snprintf for safe copying
            snprintf(logs_dest[i].message, sizeof(logs_dest[i].message), "%s", message_start);
            
            char level_buf[32] = {0};
            int level_len = message_start - g_log_buffer[src_idx] - 2;
            if (level_len > 0 && level_len < (int)sizeof(level_buf) -1) {
                // Use memcpy as we know the exact length, it's faster.
                memcpy(level_buf, g_log_buffer[src_idx], level_len); 
                // level_buf is already zeroed, so it's null-terminated.

                // *** FIX: Use snprintf instead of strncpy for guaranteed safety ***
                snprintf(logs_dest[i].level, sizeof(logs_dest[i].level), "%s", level_buf);
            }
        } else {
             snprintf(logs_dest[i].message, sizeof(logs_dest[i].message), "%s", g_log_buffer[src_idx]);
        }

        logs_dest[i].color_pair = 1;
    }

    pthread_mutex_unlock(&g_log_buffer_mutex);
}