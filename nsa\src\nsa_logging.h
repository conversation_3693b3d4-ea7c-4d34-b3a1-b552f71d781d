#ifndef _NSA_LOGGING_H_
#define _NSA_LOGGING_H_

#include <stdint.h>
#include <stdbool.h>
#include <time.h>
#include <syslog.h>

#ifdef __cplusplus
extern "C" {
#endif

// Security event types
typedef enum {
    NSA_EVENT_TYPE_SESSION_START = 1,
    NSA_EVENT_TYPE_SESSION_END = 2,
    NSA_EVENT_TYPE_THREAT_DETECTED = 3,
    NSA_EVENT_TYPE_POLICY_VIOLATION = 4,
    NSA_EVENT_TYPE_APP_IDENTIFIED = 5,
    NSA_EVENT_TYPE_USER_LOGIN = 6,
    NSA_EVENT_TYPE_ADMIN_ACTION = 7,
    NSA_EVENT_TYPE_SYSTEM_ALERT = 8,
    NSA_EVENT_TYPE_PERFORMANCE_ALERT = 9,
    NSA_EVENT_TYPE_CONFIG_CHANGE = 10,
    NSA_EVENT_TYPE_MALWARE_DETECTED = 11,
    NSA_EVENT_TYPE_DATA_EXFILTRATION = 12,
    NSA_EVENT_TYPE_INTRUSION_ATTEMPT = 13,
    NSA_EVENT_TYPE_DLP_VIOLATION = 14,
    NSA_EVENT_TYPE_URL_BLOCKED = 15
} nsa_event_type_t;

// Log output destinations
typedef enum {
    NSA_LOG_DEST_SYSLOG = 0x01,
    NSA_LOG_DEST_FILE = 0x02,
    NSA_LOG_DEST_REMOTE_SYSLOG = 0x04,
    NSA_LOG_DEST_SNMP_TRAP = 0x08,
    NSA_LOG_DEST_EMAIL = 0x10,
    NSA_LOG_DEST_DATABASE = 0x20,
    NSA_LOG_DEST_KAFKA = 0x40,
    NSA_LOG_DEST_ELASTICSEARCH = 0x80
} nsa_log_destination_t;

// Log formats
typedef enum {
    NSA_LOG_FORMAT_SYSLOG = 1,
    NSA_LOG_FORMAT_CEF = 2,      // Common Event Format
    NSA_LOG_FORMAT_LEEF = 3,     // Log Event Extended Format
    NSA_LOG_FORMAT_JSON = 4,
    NSA_LOG_FORMAT_CSV = 5,
    NSA_LOG_FORMAT_CUSTOM = 6
} nsa_log_format_t;

// Security event structure
typedef struct {
    uint64_t event_id;
    nsa_event_type_t event_type;
    time_t timestamp;
    uint32_t severity;           // 1-10 (1=lowest, 10=highest)
    
    // Session information
    uint32_t session_id;
    uint32_t src_ip;
    uint32_t dst_ip;
    uint16_t src_port;
    uint16_t dst_port;
    uint8_t protocol;
    uint16_t src_zone_id;
    uint16_t dst_zone_id;
    
    // User information
    uint32_t user_id;
    char username[64];
    char user_group[64];
    
    // Application information
    uint16_t app_id;
    char app_name[64];
    uint16_t app_category;
    uint8_t app_risk;
    
    // URL information
    char hostname[256];
    char url[512];
    uint16_t url_category;
    
    // Threat information
    uint16_t threat_id;
    char threat_name[128];
    uint8_t threat_severity;
    uint16_t threat_category;
    char threat_signature[64];
    
    // Policy information
    uint32_t policy_rule_id;
    char policy_rule_name[128];
    uint32_t policy_action;
    
    // Traffic statistics
    uint64_t bytes_sent;
    uint64_t bytes_received;
    uint64_t packets_sent;
    uint64_t packets_received;
    uint32_t session_duration;
    
    // File transfer information
    char filename[256];
    char file_hash[65];
    uint64_t file_size;
    char file_type[32];
    
    // Additional context
    char event_description[512];
    char additional_info[1024];
    
    // Geolocation
    char src_country[3];
    char dst_country[3];
    char src_city[64];
    char dst_city[64];
    
    // Device information
    char device_name[64];
    char device_type[32];
} nsa_security_event_t;

// Performance metrics structure
typedef struct {
    time_t timestamp;
    
    // Throughput metrics
    uint64_t packets_per_second;
    uint64_t bits_per_second;
    uint64_t sessions_per_second;
    
    // Latency metrics
    uint32_t avg_session_setup_time_us;
    uint32_t avg_packet_processing_time_us;
    uint32_t avg_policy_evaluation_time_us;
    
    // Resource utilization
    uint8_t cpu_utilization_percent;
    uint8_t memory_utilization_percent;
    uint8_t disk_utilization_percent;
    
    // Session statistics
    uint32_t active_sessions;
    uint32_t session_table_utilization_percent;
    uint32_t session_timeouts_per_minute;
    
    // Threat detection statistics
    uint32_t threats_detected_per_minute;
    uint32_t false_positives_per_hour;
    uint32_t blocked_sessions_per_minute;
    
    // DPI statistics
    uint32_t apps_identified_per_minute;
    uint8_t dpi_accuracy_percent;
    uint32_t dpi_cache_hit_rate_percent;
    
    // Engine performance
    uint64_t rx_thread_cycles_per_packet;
    uint64_t worker_thread_cycles_per_task;
    uint32_t completion_queue_depth;
} nsa_performance_metrics_t;

// Log configuration
typedef struct {
    bool enabled;
    uint32_t destinations;       // Bitmask of nsa_log_destination_t
    nsa_log_format_t format;
    uint32_t min_severity;       // Minimum severity to log
    uint32_t max_events_per_second; // Rate limiting
    
    // File logging configuration
    char log_file_path[256];
    uint64_t max_file_size_mb;
    uint32_t max_files_to_keep;
    bool compress_old_files;
    
    // Remote syslog configuration
    char remote_syslog_server[256];
    uint16_t remote_syslog_port;
    uint8_t syslog_facility;
    
    // Database configuration
    char db_connection_string[512];
    char db_table_name[64];
    uint32_t db_batch_size;
    
    // Email alerting configuration
    char smtp_server[256];
    uint16_t smtp_port;
    char from_email[128];
    char to_email[512];        // Comma-separated list
    char email_subject[256];
    uint32_t min_severity_for_email;
    
    // Performance logging
    bool log_performance_metrics;
    uint32_t performance_log_interval_seconds;
} nsa_log_config_t;

// Logging API
int nsa_logging_init(const nsa_log_config_t *config);
void nsa_logging_cleanup(void);

// Configuration management
int nsa_logging_update_config(const nsa_log_config_t *new_config);
int nsa_logging_get_config(nsa_log_config_t *config);

// Event logging
int nsa_log_security_event(const nsa_security_event_t *event);
int nsa_log_performance_metrics(const nsa_performance_metrics_t *metrics);

// Convenience functions for common events
int nsa_log_session_start(uint32_t session_id, uint32_t src_ip, uint32_t dst_ip, 
                         uint16_t src_port, uint16_t dst_port, uint8_t protocol);
int nsa_log_session_end(uint32_t session_id, uint64_t bytes_sent, uint64_t bytes_received,
                       uint32_t duration_seconds);
int nsa_log_threat_detected(uint32_t session_id, uint16_t threat_id, const char *threat_name,
                          uint8_t severity, const char *signature);
int nsa_log_policy_violation(uint32_t session_id, uint32_t rule_id, const char *rule_name,
                           uint32_t action, const char *reason);
int nsa_log_app_identified(uint32_t session_id, uint16_t app_id, const char *app_name,
                         uint16_t category, uint8_t risk);
int nsa_log_admin_action(const char *admin_user, const char *action, const char *target,
                       const char *result);
int nsa_log_config_change(const char *admin_user, const char *component, 
                        const char *old_value, const char *new_value);

// Filtering and querying
typedef struct {
    time_t start_time;
    time_t end_time;
    nsa_event_type_t event_type;
    uint32_t min_severity;
    uint32_t src_ip;
    uint32_t dst_ip;
    uint32_t user_id;
    uint16_t app_id;
    uint16_t threat_id;
    uint32_t policy_rule_id;
    uint32_t max_results;
} nsa_log_filter_t;

int nsa_logging_query_events(const nsa_log_filter_t *filter, nsa_security_event_t *events,
                            uint32_t max_events, uint32_t *found_count);

// Statistics and management
int nsa_logging_get_stats(uint64_t *total_events, uint64_t *events_per_hour,
                        uint32_t *queue_depth, uint32_t *dropped_events);
int nsa_logging_flush_buffers(void);
int nsa_logging_rotate_files(void);

// Real-time alerting
typedef void (*nsa_alert_callback_t)(const nsa_security_event_t *event, void *user_data);
int nsa_logging_register_alert_callback(nsa_alert_callback_t callback, void *user_data);
int nsa_logging_unregister_alert_callback(nsa_alert_callback_t callback);

#ifdef __cplusplus
}
#endif

#endif // _NSA_LOGGING_H_ 