/**
 * @file nsa_main.c
 * @brief Network Security Application - Main Program
 * 
 * This file contains the main entry point and initialization logic
 * for the NSA (Network Security Application). It handles daemon
 * initialization, signal processing, IPC setup, and main event loop.
 * 
 * Key responsibilities:
 * - Daemon framework initialization
 * - Signal handling for graceful shutdown
 * - CLI command processing setup
 * - Database initialization
 * - Helper thread management
 * - Integration with DPIF data plane
 * 
 * <AUTHOR> <PERSON>
 * @date 2025
 * @copyright Calix Inc.
 */

#define _GNU_SOURCE

/* ========================================================================
 * System Includes
 * ======================================================================== */
#include <stdlib.h>
#include <sys/shm.h>
#include <sys/mman.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/inotify.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <string.h>
#include <stdbool.h>
#include <stdio.h>
#include <unistd.h>
#include <errno.h>
#include <semaphore.h>
#include <regex.h>
#include <libgen.h>
#include <pthread.h>
#include <signal.h>

/* ========================================================================
 * External Library Includes
 * ======================================================================== */
#include <daemonlib.h>
#include <cmd_parser.h>
#include <sl_files.h>
#include <dl_util.h>
#include <ipc/ipc_lib.h>
#include <ipc/ipc_msg_def.h>
#include <../version.h>

/* ========================================================================
 * NSA Module Includes
 * ======================================================================== */
#include "nsa.h"
#include "nsa_cli.h"

/* ========================================================================
 * Global Variables
 * ======================================================================== */

/** @brief Static NSA root context instance */
static nsa_root_t g_nsa_context;

/** @brief Global pointer to NSA root context */
nsa_root_t *g_nsa_root = &g_nsa_context;

/* ========================================================================
 * Local Constants and Macros
 * ======================================================================== */

/* IPC buffer size configuration */
#ifndef NSA_IPC_BUFFER_SIZE
#define NSA_IPC_BUFFER_SIZE     0x500000    /* 5MB IPC buffer */
#endif


/* ========================================================================
 * Local Function Prototypes
 * ======================================================================== */

static void nsa_daemon_event_callback(void *ctx);
static void nsa_signal_handler(int signo);
static void nsa_ipc_handler(void *in_payload, int fd, uint32_t msg_type, 
                           size_t len, mbox_address_t sma);
static void nsa_daemon_init(void);
static void nsa_graceful_shutdown(nsa_root_t *root, uint8_t reboot);
static void nsa_psm_callback(dl_psm_state state, dl_psm_state old_state, void *ctx);

/* ========================================================================
 * Usage and Help Functions
 * ======================================================================== */

/**
 * @brief Display NSA usage information
 * 
 * Prints command line usage information and available options.
 */
static void nsa_usage(void) {
    printf("NSA (Network Security Application) v%s\n", NSA_VERSION_STRING);
    printf("Built on %s at %s\n\n", NSA_BUILD_DATE, NSA_BUILD_TIME);
    printf("Usage: nsa [options]\n");
    printf("Options:\n");
    printf("  -f, --foreground     Run in foreground mode\n");
    printf("  -h, --help          Show this help message\n");
    printf("  -v, --version       Show version information\n");
    printf("  -c, --config FILE   Specify configuration file\n");
    printf("  -l, --log-file FILE Specify log file path\n");
    printf("  -d, --debug         Enable debug mode\n");
    printf("\n");
}

/* ========================================================================
 * Event and Signal Handling Functions
 * ======================================================================== */

/**
 * @brief NSA daemon event callback function
 * 
 * Processes daemon framework events including event posts, subscriptions,
 * and unsubscriptions. This is the main event processing loop for the
 * daemon framework.
 * 
 * @param[in] ctx  Event context (unused)
 */
static void nsa_daemon_event_callback(void *ctx) {
    dl_event_t  *event = NULL;
    int         len = 0;
    int         events_processed = 0;
    const int   max_events_per_cycle = 256;  /* Prevent event loop starvation */

    NSA_UNUSED(ctx);

    /* Process events in batches to prevent starvation */
    while ((events_processed < max_events_per_cycle) &&
           ((len = dl_event_get_next(&event)) > 0)) {
        
        events_processed++;
        
        if (event == NULL) {
            NSA_LOG_WARNING("Received NULL event pointer");
            continue;
        }

        switch (event->type) {
        case EVENT_TYPE_POST:
            NSA_LOG_INFO("Received event: %s", event->event ? event->event : "unknown");
            
            /* TODO: Implement specific event handlers
             * - Session events
             * - Configuration changes
             * - System events
             */
            break;

        case EVENT_TYPE_SUBSCRIBE:
            NSA_LOG_DEBUG("Subscribe event for: %s", 
                         event->event ? event->event : "unknown");
            break;

        case EVENT_TYPE_UNSUBSCRIBE:
            NSA_LOG_DEBUG("Unsubscribe event for: %s", 
                         event->event ? event->event : "unknown");
            break;

        default:
            NSA_LOG_WARNING("Unknown event type: %d", event->type);
            break;
        }
        
        /* Free the event memory */
        free(event);
        event = NULL;
    }

    if (events_processed >= max_events_per_cycle) {
        NSA_LOG_DEBUG("Processed maximum events (%d) in one cycle", max_events_per_cycle);
    }
}

/**
 * @brief NSA signal handler
 * 
 * Handles various system signals for graceful shutdown, restart,
 * and configuration reload. Ensures proper cleanup of resources
 * before termination.
 * 
 * @param[in] signo  Signal number received
 */
static void nsa_signal_handler(int signo) {
    
    switch (signo) {
    case SIGINT:
    case SIGTERM:
        NSA_LOG_INFO("Received termination signal (%s), initiating graceful shutdown", 
                    (signo == SIGINT) ? "SIGINT" : "SIGTERM");

        /* Check for repeated signals */
        if (g_nsa_root->control_flags & NSA_FLAG_DO_RESTART) {
            NSA_LOG_WARNING("Second termination signal received, forcing immediate cleanup and exit");
            nsa_cleanup();
            exit(EXIT_FAILURE);
        }

        /* Set flag for graceful restart */
        g_nsa_root->control_flags |= NSA_FLAG_DO_RESTART;
        g_nsa_root->is_running = false;
        
        /* Signal the daemon framework to shutdown */
        if (g_nsa_root->daemon_ctx) {
            DL_PSM_EVENT_SHUTDOWN(g_nsa_root->daemon_ctx);
        }
        break;

    case SIGQUIT:
        NSA_LOG_INFO("Received SIGQUIT, initiating immediate shutdown");

        /* Check for repeated signals */
        if (g_nsa_root->control_flags & NSA_FLAG_DO_SHUTDOWN) {
            NSA_LOG_WARNING("Second SIGQUIT received, forcing immediate cleanup and abort");
            nsa_cleanup();
            abort();
        }

        /* Set flag for immediate shutdown */
        g_nsa_root->control_flags |= NSA_FLAG_DO_SHUTDOWN;
        g_nsa_root->is_running = false;
        
        /* Signal immediate shutdown */
        if (g_nsa_root->daemon_ctx) {
            DL_PSM_EVENT_SHUTDOWN(g_nsa_root->daemon_ctx);
        }
        break;

    case SIGHUP:
        NSA_LOG_INFO("Received SIGHUP, reloading configuration");
        
        /* TODO: Implement configuration reload
         * - Reload configuration files
         * - Update runtime parameters
         * - Refresh policy rules
         * - Update threat signatures
         */
        break;

    case SIGUSR1:
        NSA_LOG_INFO("Received SIGUSR1, dumping runtime statistics");
        
        /* TODO: Implement statistics dump
         * - Print current session statistics
         * - Print performance metrics
         * - Print memory usage
         */
        break;

    case SIGUSR2:
        NSA_LOG_INFO("Received SIGUSR2, toggling debug mode");
        
        /* TODO: Implement debug mode toggle
         * - Toggle debug logging
         * - Enable/disable verbose output
         */
        break;

    default:
        NSA_LOG_WARNING("Received unhandled signal: %d", signo);
        break;
    }
}

/* ========================================================================
 * CLI (Command Line Interface) Functions
 * ======================================================================== */

/**
 * @brief NSA CLI command batch definitions
 * CLI initialization is now handled by nsa_cli.c
 */

/* CLI handling is now managed by nsa_cli.c */

/* ========================================================================
 * IPC (Inter-Process Communication) Functions
 * ======================================================================== */

/**
 * @brief Handle incoming IPC messages
 * 
 * Processes IPC messages from other system components such as
 * configuration managers, monitoring systems, and external interfaces.
 * 
 * @param[in] in_payload  Message payload data
 * @param[in] fd          File descriptor
 * @param[in] msg_type    Message type identifier
 * @param[in] len         Payload length
 * @param[in] sma         Source mailbox address
 */
static void nsa_ipc_handler(void *in_payload, int fd, uint32_t msg_type,
                           size_t len, mbox_address_t sma) {
    NSA_UNUSED(fd);
    NSA_UNUSED(sma);
    
    if (in_payload == NULL || len == 0) {
        NSA_LOG_WARNING("Received IPC message with invalid payload");
        return;
    }

    NSA_LOG_DEBUG("Received IPC message: type=%u, len=%zu", msg_type, len);

    switch (msg_type) {
    case 1:  /* Configuration Update */
        NSA_LOG_INFO("Received configuration update message");
        /* TODO: Handle configuration updates
         * - Parse configuration payload
         * - Validate new configuration
         * - Apply changes to runtime
         */
        break;

    case 2:  /* Status Request */
        NSA_LOG_DEBUG("Received status request message");
        /* TODO: Handle status requests
         * - Collect current system status
         * - Format response message
         * - Send status response
         */
        break;

    case 3:  /* Control Command */
        NSA_LOG_INFO("Received control command message");
        /* TODO: Handle control commands
         * - Parse command payload
         * - Execute requested action
         * - Send command response
         */
        break;

    default:
        NSA_LOG_WARNING("Received unknown IPC message type: %u", msg_type);
        break;
    }
}

/* ========================================================================
 * Daemon Initialization Functions
 * ======================================================================== */

/**
 * @brief Initialize NSA daemon framework
 * 
 * Performs core daemon initialization including process name setup,
 * signal handlers, daemonization, and logging initialization.
 */
static void nsa_daemon_init(void) {
    int status = 0;

    /* Set daemon process name */
    if (g_nsa_root->process_name[0] == '\0') {
        strncpy(g_nsa_root->process_name, "nsad", NSA_MAX_PROCESS_NAME_LEN - 1);
        g_nsa_root->process_name[NSA_MAX_PROCESS_NAME_LEN - 1] = '\0';
    }

    NSA_LOG_INFO("Initializing NSA daemon: %s", g_nsa_root->process_name);

    /* Initialize daemon library global context */
    if (g_nsa_root->daemon_ctx == NULL) {
        NSA_LOG_ERROR("Daemon context is NULL, cannot proceed");
        exit(EXIT_FAILURE);
    }

    /* Initialize daemon library global context */
    dl_init_daemon_context(g_nsa_root->daemon_ctx, g_nsa_root->process_name);

    /* Set up signal handlers */
    dl_default_signals(nsa_signal_handler);
    
    /* Register additional signal handlers */
    signal(SIGUSR1, nsa_signal_handler);  /* Statistics dump */
    signal(SIGUSR2, nsa_signal_handler);  /* Debug toggle */

    /* Daemonize process if not running in foreground */
    if (!(g_nsa_root->control_flags & NSA_FLAG_FOREGROUND)) {
        dl_daemonize_us();
        NSA_LOG_INFO("Daemonizing process...");
    }

     /* Initialize logging system */
    if (strlen(g_nsa_root->log_file_path) > 0) {
        NSA_LOG_INFO("Opening log file: %s", g_nsa_root->log_file_path);
        dl_open_logfile(g_nsa_root->log_file_path);
    }

    /* Initialize logging system */
    dl_log_init(g_nsa_root->daemon_ctx, g_nsa_root->process_name);

    /* Set default logging mask if not configured */
    if (!g_nsa_root->daemon_ctx->logger_mask) {
        g_nsa_root->daemon_ctx->logger_mask = 0x00FFFFF0;
    }

    NSA_LOG_INFO("Logging initialized successfully (level: %d)", 
                g_nsa_root->daemon_ctx->logger_level);
    NSA_LOG_INFO("NSA Version %s (Built: %s %s)", 
                NSA_VERSION_STRING, NSA_BUILD_DATE, NSA_BUILD_TIME);

    /* Initialize Event library */
    status = dl_event_init(g_nsa_root->daemon_ctx,
                          g_nsa_root->process_name,
                          nsa_daemon_event_callback,
                          NULL);
    if (status != 0) {
        NSA_LOG_ERROR("Failed to initialize event library: %d", status);
    }

    /* Initialize CLI command parser */
    status = nsa_cli_init(g_nsa_root);
    if (status != 0) {
        NSA_LOG_ERROR("Failed to initialize CLI parser: %d", status);
    }

    /* Load schema files */
    g_nsa_root->schema_set = sl_load_schema_files();
    if (g_nsa_root->schema_set == NULL) {
        NSA_LOG_ERROR("Failed to load schema files");
    }
    NSA_LOG_INFO("Schema files loaded successfully");

    /* Initialize IPC subsystem */
    NSA_LOG_INFO("Initializing IPC subsystem");
    status = ipc_init(g_nsa_root->process_name, 
                     NSA_IPC_BUFFER_SIZE, 
                     true, 0, false, 
                     nsa_ipc_handler);
    if (status != IPC_OK) {
        NSA_LOG_ERROR("IPC initialization failed: %d", status);
        dl_fatal("IPC subsystem initialization failed");
    }

    /* Create CLI server socket */
    g_nsa_root->server_ctx = dl_dcli_init_server(nsa_cli_command_handler,
                                                 nsa_cli_add_session_context,
                                                 g_nsa_root,
                                                 g_nsa_root->process_name,
                                                 10,  /* max connections */
                                                 1);  /* port offset */

    if (g_nsa_root->server_ctx == NULL) {
        NSA_LOG_ERROR("Failed to create CLI server");
        dl_fatal("CLI server creation failed");
    }
    NSA_LOG_INFO("CLI server initialized successfully");

    /* Initialize circular log buffer for event tracing */
    /* Initialize circular log buffer for event tracing */
    dl_clog_init(g_nsa_root->daemon_ctx, 60, 2000, false);

    /* Set running state and start time */
    g_nsa_root->is_running = true;
    g_nsa_root->start_time = time(NULL);
    
    NSA_LOG_INFO("NSA daemon initialization completed successfully");
}

/**
 * @brief Perform graceful NSA shutdown (PSM callback version)
 * 
 * This is a lightweight shutdown function specifically designed for PSM callbacks.
 * It sets shutdown flags and signals the main loop to exit, allowing the main
 * cleanup function (nsa_cleanup) to handle the actual resource cleanup.
 * 
 * @param[in] root    Pointer to NSA root context
 * @param[in] reboot  Flag indicating if this is a restart (1) or full shutdown (0)
 */
static void nsa_graceful_shutdown(nsa_root_t *root, uint8_t reboot) {
    NSA_UNUSED(reboot);  /* Parameter kept for interface compatibility */
    
    if (root == NULL) {
        NSA_LOG_ERROR("Invalid root context in graceful shutdown");
        return;
    }

    NSA_LOG_INFO("PSM graceful shutdown initiated");

    /* Set shutdown flags to signal main event loop to exit */
    root->control_flags |= NSA_FLAG_DO_SHUTDOWN;
    root->is_running = false;

    /* Post shutdown event for monitoring/logging */
    if (root->control_flags & NSA_FLAG_DO_RESTART) {
        NSA_LOG_INFO("Shutdown reason: restart");
        dl_event_post("psm_shutdown", ETAG_STRING(DL_ATTR_DETAILS, "PSM restart"));
    } else {
        NSA_LOG_INFO("Shutdown reason: full stop");
        dl_event_post("psm_shutdown", ETAG_STRING(DL_ATTR_DETAILS, "PSM shutdown"));
    }

    /* NOTE: Actual cleanup will be performed by nsa_cleanup() in main loop */
    NSA_LOG_INFO("PSM graceful shutdown signaled - cleanup will be handled by main loop");
}

/**
 * @brief NSA Process State Manager (PSM) callback
 * 
 * Called by the daemon library when the process state changes.
 * Handles initialization, running, and shutdown states.
 * 
 * @param[in] state      New PSM state
 * @param[in] old_state  Previous PSM state
 * @param[in] ctx        Context pointer (unused)
 */
static void nsa_psm_callback(dl_psm_state state, dl_psm_state old_state, void *ctx)
{
    nsa_root_t *root = (nsa_root_t *)ctx;
    
    NSA_UNUSED(old_state);

    if (root == NULL) {
        NSA_LOG_ERROR("NULL context in PSM callback");
        return;
    }

    NSA_LOG_DEBUG("PSM state transition: %d -> %d", old_state, state);

    switch (state) {
    case DL_PSM_STATE_INIT:
        NSA_LOG_INFO("PSM: Entering INIT state");
        
        /* Initialize NSA subsystems */
        /* TODO: Add subsystem initialization calls here
         * - Initialize session management
         * - Initialize policy engine
         * - Initialize threat intelligence
         * - Start worker threads
         */
        break;

    case DL_PSM_STATE_WAIT_SB:
        NSA_LOG_INFO("PSM: Entering WAIT_SB state");
        
        /* Disconnect all CLI clients during standby transition */
        if (root->server_ctx != NULL) {
            dl_dcli_disconnect_clients_inrange(root->server_ctx);
        }
        
        /* TODO: Additional standby preparations
         * - Suspend packet processing
         * - Save current state
         * - Prepare for potential failover
         */
        break;

    case DL_PSM_STATE_UP:
        NSA_LOG_INFO("PSM: Entering UP state - NSA is now operational");
        
        /* TODO: Start full operational mode
         * - Resume packet processing
         * - Enable policy enforcement
         * - Start monitoring and statistics
         */
        break;

    case DL_PSM_STATE_SHUTDOWN:
        NSA_LOG_INFO("PSM: Entering SHUTDOWN state");
        
        /* Initiate graceful shutdown (flags will be set by the shutdown function) */
        nsa_graceful_shutdown(root, 0);
        break;

    default:
        NSA_LOG_WARNING("PSM: Unknown state transition to %d (current daemon state: %d)",
                       state, DL_PSM_GET_STATE(root->daemon_ctx));
        break;
    }
}

/**
 * @brief Initializes NSA's global resources that depend on DPDK EAL.
 * This is called after dpif_prepare() and before dpif_start().
 */
int nsa_create_global_resources(const dpif_app_config_t *app_config) {
    if (g_nsa_root->nsa_session_ctx_pool != NULL) {
        NSA_LOG_WARNING("NSA session context pool already exists.");
        return 0;
    }

    char pool_name[] = "NSA_SESSION_CONTEXT_POOL";
    uint32_t pool_size = app_config->max_sessions;

    g_nsa_root->nsa_session_ctx_pool = rte_mempool_create(
        pool_name,
        pool_size,
        sizeof(nsa_session_context_t),
        512, // cache_size
        0, NULL, NULL, NULL, NULL,
        rte_socket_id(), // Create on the main core's NUMA node
        0);

    if (g_nsa_root->nsa_session_ctx_pool == NULL) {
        NSA_LOG_ERROR("Failed to create global NSA session context pool");
        return -ENOMEM;
    }

    NSA_LOG_INFO("Global NSA session context pool '%s' created with %u entries.", pool_name, pool_size);
    return 0;
}

/* ========================================================================
 * NSA Core API Implementation
 * ======================================================================== */

/**
 * @brief Initialize the NSA application
 * 
 * Initializes all core components of the NSA application including
 * daemon framework, database, session management, and helper threads.
 * 
 * @param[in] argc  Command line argument count  
 * @param[in] argv  Command line argument vector
 * @return 0 on success, negative error code on failure
 */
int nsa_init(int argc, char *argv[]) {
    int ca;
    int status = 0;

    /* Initialize global NSA context */
    memset(g_nsa_root, 0, sizeof(nsa_root_t));

    /* Create daemon framework context */
    g_nsa_root->daemon_ctx = dl_new_daemon_context();
    if (g_nsa_root->daemon_ctx == NULL) {
        fprintf(stderr, "Failed to create daemon context\n");
        return -1;
    }

    /* Set default logging level */
    g_nsa_root->daemon_ctx->logger_level = ILOG_INFO;

    /* Parse command line options */
    while ((ca = getopt(argc, argv, "hvfso:l:m:n:e:k:r:")) != -1) {
        switch (ca) {
        case 'v':
            printf("%s Version: %s\n", "nsad", NSA_VERSION_STRING);
            return 1; /* Non-error exit for version display */
            
        case 'f':
            g_nsa_root->control_flags |= NSA_FLAG_FOREGROUND;
            break;
            
        case 's':
            g_nsa_root->daemon_ctx->use_syslog = 1;
            break;

        case 'o':
            strncpy(g_nsa_root->log_file_path, optarg, sizeof(g_nsa_root->log_file_path) - 1);
            break;

        case 'l':
            g_nsa_root->daemon_ctx->logger_level = atoi(optarg);
            break;

        case 'm':
            if ((strlen(optarg) > 2) && (optarg[1] == 'x')) {
                sscanf(optarg, "%x", &g_nsa_root->daemon_ctx->logger_mask);
            } else {
                g_nsa_root->daemon_ctx->logger_mask = atoi(optarg);
            }
            break;

        case 'n':
            strncpy(g_nsa_root->process_name, optarg, NSA_MAX_PROCESS_NAME_LEN - 1);
            break;

        case 'h':
        default:
            nsa_usage();
            return 1; /* Non-error exit for help display */
        }
    }

    /* Initialize daemon framework */
    nsa_daemon_init();

    NSA_LOG_INFO("Initializing Process State Machine");

    /* Initialize process state machine */
    dl_psm_init(g_nsa_root->daemon_ctx, nsa_psm_callback, g_nsa_root);

    /* Initialize PML engine */
    NSA_LOG_INFO("Initializing PML (Packet Matching Language) engine");
    if (nsa_pml_init() < 0) {
        NSA_LOG_ERROR("PML engine initialization failed");
        return -2;
    }

    /* Register DPIF callbacks */
    nsa_session_register_callbacks();

    #if defined(PLATFORM_AEGIS)
    /* Configure DPIF library */
    dpif_app_config_t app_config = {
        .app_name_prefix = "nsa",
        .num_rx_threads = 8,
        .num_worker_threads = 2,
        .max_sessions = 1000000,
        .session_timeout_seconds = 300,
        .session_update_interval_seconds = 1800
    };
    dpif_config_init(1, 0);
    #elif defined(PLATFORM_SMBSIM)
    /* Configure DPIF library */
    dpif_app_config_t app_config = {
        .app_name_prefix = "nsa",
        .num_rx_threads = 2,
        .num_worker_threads = 1,
        .max_sessions = 1000000,
        .session_timeout_seconds = 300,
        .session_update_interval_seconds = 1800
    };
    dpif_config_init(0, 1);
    #endif

    NSA_LOG_INFO("Preparing DPIF library resources (Phase 1)...");
    if (dpif_prepare(&app_config) < 0) {
        NSA_LOG_ERROR("DPIF library preparation failed");
        return -3;
    }

    NSA_LOG_INFO("Creating NSA global resources...");
    if (nsa_create_global_resources(&app_config) < 0) {
        NSA_LOG_ERROR("Failed to create NSA global resources");
        dpif_cleanup();
        return -4;
    }

    NSA_LOG_INFO("Starting DPIF data plane (Phase 2)...");
    if (dpif_start() < 0) {
        NSA_LOG_ERROR("DPIF library start failed");
        nsa_cleanup();
        return -5;
    }

    /* Initialize NSA event system */
    if (nsa_helper_init()< 0) {
        NSA_LOG_ERROR("Failed to initialize helper thread");
        nsa_cleanup();
        return -6;
    }

    /* Initialize CDB (Database) subsystem */
    status = nsa_cdb_init(g_nsa_root);
    if (status != 0) {
        NSA_LOG_ERROR("Failed to initialize NSA CDB subsystem: %d", status);
        dpif_cleanup();
        return -7;
    }

    /* Start the monitor thread */
    if (nsa_monitor_thread_start(g_nsa_root) != 0) {
        NSA_LOG_ERROR("Failed to start monitor thread");
        dpif_cleanup();
        return -8;
    }

    NSA_LOG_INFO("NSA initialization completed successfully");
    return 0;
}

/**
 * @brief Cleanup and shutdown the NSA application
 * 
 * Gracefully shuts down all NSA components and releases resources.
 * This is the main cleanup entry point that orchestrates the shutdown sequence.
 */
void nsa_cleanup(void) {
    static bool cleanup_in_progress = false;
    
    if (g_nsa_root == NULL) {
        return;
    }

    /* Prevent double cleanup */
    if (cleanup_in_progress) {
        NSA_LOG_WARNING("NSA cleanup already in progress, skipping duplicate call");
        return;
    }
    cleanup_in_progress = true;

    NSA_LOG_INFO("Starting NSA cleanup sequence");

    /* Set running state to false */
    g_nsa_root->is_running = false;

    /* Log shutdown event */
    if (g_nsa_root->control_flags & NSA_FLAG_DO_RESTART) {
        NSA_LOG_INFO("NSA graceful restart shutdown initiated");
        dl_event_post("shutdown", ETAG_STRING(DL_ATTR_DETAILS, "Graceful restart"));
    } else {
        NSA_LOG_INFO("NSA permanent shutdown initiated");
        dl_event_post("shutdown", ETAG_STRING(DL_ATTR_DETAILS, "Full stop"));
    }

    /* 1. Stop helper threads first */
    NSA_LOG_INFO("Stopping helper threads");
    nsa_helper_stop();

    /* Stop the monitor thread */
    nsa_monitor_thread_stop(g_nsa_root);

    /* 2. Stop DPIF data plane interface */
    NSA_LOG_INFO("Stopping DPIF interface");
    dpif_stop();
    dpif_cleanup();

    /* 3. Cleanup PML engine */
    NSA_LOG_INFO("Cleaning up PML engine");
    nsa_pml_cleanup();

    /* 4. Close database connections */
    NSA_LOG_INFO("Closing database connections");
    //nsa_cdb_cleanup(&g_nsa_root->cdb_handle);

    /* 5. Close CLI server and cleanup CLI system */
    if (g_nsa_root->server_ctx != NULL) {
        NSA_LOG_INFO("Closing CLI server");
        dl_dcli_close_server(g_nsa_root->server_ctx);
        g_nsa_root->server_ctx = NULL;
    }
    
    /* 6. Cleanup CLI subsystem */
    NSA_LOG_INFO("Cleaning up CLI subsystem");
    nsa_cli_cleanup(g_nsa_root);

    /* 7. Cleanup IPC subsystem */
    NSA_LOG_INFO("Cleaning up IPC subsystem");
    /* Note: IPC library may not have explicit cleanup function - check if needed */
    /* ipc_cleanup(); */  /* TODO: Check if IPC library provides cleanup */

    /* 8. Save circular log buffer if configured */
    if (g_nsa_root->daemon_ctx && 
        g_nsa_root->daemon_ctx->dl_membuff.use_memlog &&
        g_nsa_root->daemon_ctx->ccb) {
        NSA_LOG_INFO("Saving circular log buffer");
        clog_save_dcli(g_nsa_root->daemon_ctx->ccb);
    }

    /* 9. Cleanup schema files */
    if (g_nsa_root->schema_set != NULL) {
        NSA_LOG_INFO("Cleaning up schema files");
        /* TODO: Add proper schema cleanup function if available */
        /* sl_cleanup_schema_files(g_nsa_root->schema_set); */
        g_nsa_root->schema_set = NULL;
    }

    /* 10. Stop daemon library event processing */
    NSA_LOG_INFO("Stopping daemon library event processing");
    dl_graceful_shutdown();

    /* 11. Final daemon context cleanup */
    if (g_nsa_root->daemon_ctx) {
        NSA_LOG_INFO("Cleaning up daemon context");
        dl_free_daemon_context(g_nsa_root->daemon_ctx);
        g_nsa_root->daemon_ctx = NULL;
    }

    /* 12. Clear all global context data */
    NSA_LOG_INFO("Clearing global context");
    memset(g_nsa_root, 0, sizeof(nsa_root_t));

    NSA_LOG_INFO("NSA cleanup completed");

    /* Reset cleanup flag for potential re-initialization */
    cleanup_in_progress = false;
}

/* ========================================================================
 * Main Program Entry Point
 * ======================================================================== */

/**
 * @brief NSA main program entry point
 * 
 * Initializes the NSA application, processes command line arguments,
 * sets up all subsystems, and enters the main event loop.
 * 
 * @param[in] argc  Command line argument count
 * @param[in] argv  Command line argument vector
 * @return 0 on success, non-zero on failure
 */
int main(int argc, char **argv) {
    int status = 0;

    /* Initialize NSA application */
    status = nsa_init(argc, argv);
    if (status < 0) {
        fprintf(stderr, "NSA initialization failed: %d\n", status);
        exit(EXIT_FAILURE);
    } else if (status > 0) {
        /* Non-error exit (version/help displayed) */
        exit(EXIT_SUCCESS);
    }

    /* Main event loop */
    NSA_LOG_INFO("Entering main event loop");
    while (1) {
        DL_PSM_RUN_SM(g_nsa_root->daemon_ctx);

        if (g_nsa_root->control_flags & NSA_FLAG_DO_SHUTDOWN) {
            NSA_LOG_INFO("Shutdown signal received - exiting main loop");
            break;
        }

        dl_poll_fds(-1);
        dl_reap_zombie(-1);
    }

    /* Cleanup and exit */
    nsa_cleanup();
    
    NSA_LOG_INFO("NSA application terminated");
    return 0;
}