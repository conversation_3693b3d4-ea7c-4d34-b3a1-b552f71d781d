/**
 * @file nsa_monitor.c
 * @brief NSA Monitor Thread Implementation (Server-side)
 *
 * This file contains the implementation of the NSA monitor thread.
 * It periodically collects statistics from DPIF (via its public API) and
 * business data from NSA's internal modules (like CDB), aggregates them
 * into the nsa_monitor_data_t structure, and serves this data to
 * connecting clients over a Unix socket.
 */

#include <errno.h>
#include <poll.h>
#include <pthread.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <time.h>
#include <unistd.h>

#include "nsa.h"
#include "nsa_cdb.h"
#include "nsa_performance_analyzer.h"
#include "nsa_profiler.h"

// Used for rate calculation
typedef struct {
  dpif_stats_snapshot_t last_snapshot;
} nsa_monitor_rate_context_t;

static nsa_monitor_rate_context_t g_rate_ctx = {0};

// Forward declarations for static functions
static void *nsa_monitor_thread_main(void *arg);
void nsa_monitor_update_data(nsa_monitor_data_t *data);

typedef struct {
  nsa_monitor_data_t *monitor_data;
  int count;
} monitor_iterator_ctx_t;

static int monitor_fill_sessions_callback(nsa_enriched_session *session,
                                          uint32_t index, void *user_data) {
  monitor_iterator_ctx_t *ctx = (monitor_iterator_ctx_t *)user_data;

  if (ctx->count >= MAX_SESSIONS) {
    return -1; // Abort if we have filled the buffer
  }

  nsa_monitor_session_info_t *info = &ctx->monitor_data->sessions[ctx->count];
  memset(info, 0, sizeof(*info));

  switch (session->proto) {
  case 6:
    strncpy(info->protocol, "TCP", sizeof(info->protocol));
    break;
  case 17:
    strncpy(info->protocol, "UDP", sizeof(info->protocol));
    break;
  default:
    snprintf(info->protocol, sizeof(info->protocol), "%d", session->proto);
    break;
  }
  snprintf(info->src, sizeof(info->src), "%s:%d", session->src_ip,
           session->sport);
  snprintf(info->dst, sizeof(info->dst), "%s:%d", session->dst_ip,
           session->dport);
  strncpy(info->app, session->app[0] ? session->app : "...",
          sizeof(info->app) - 1);
  strncpy(info->alp, session->alp[0] ? session->alp : "...",
          sizeof(info->alp) - 1);
  snprintf(info->user, sizeof(info->user), "user_%d", session->user);
  snprintf(info->device, sizeof(info->device), "dev_%d", session->dev_type);
  strncpy(info->threat, "nil", sizeof(info->threat) - 1);
  strncpy(info->verdict, session->verdict[0] ? session->verdict : "PENDING",
          sizeof(info->verdict) - 1);

  if (strcmp(session->verdict, "ACCEPTED") == 0) {
    info->verdict_color_pair = 1;
    strncpy(info->icon, "[+]", sizeof(info->icon));
  } else if (strcmp(session->verdict, "DROPPED") == 0) {
    info->verdict_color_pair = 2;
    strncpy(info->icon, "[!]", sizeof(info->icon));
  } else if (strcmp(session->verdict, "BLOCKED") == 0) {
    info->verdict_color_pair = 3;
    strncpy(info->icon, "[-]", sizeof(info->icon));
  } else {
    info->verdict_color_pair = 1;
  }

  ctx->count++;
  return 0; // Continue iteration
}

/**
 * @brief Starts the monitor thread.
 */
int nsa_monitor_thread_start(nsa_root_t *root) {
  if (pthread_mutex_init(&root->monitor_data_mutex, NULL) != 0) {
    NSA_LOG_ERROR("Failed to initialize monitor data mutex: %s",
                  strerror(errno));
    return -1;
  }

  // Initialize the rate calculation context with a baseline snapshot
  dpif_get_stats_snapshot(&g_rate_ctx.last_snapshot);

  if (pthread_create(&root->monitor_thread_id, NULL, nsa_monitor_thread_main,
                     root) != 0) {
    NSA_LOG_ERROR("Failed to create monitor thread: %s", strerror(errno));
    pthread_mutex_destroy(&root->monitor_data_mutex);
    return -1;
  }

  NSA_LOG_INFO("Monitor thread started successfully.");
  return 0;
}

/**
 * @brief Stops the monitor thread and cleans up resources.
 */
void nsa_monitor_thread_stop(nsa_root_t *root) {
  if (!root || root->monitor_thread_id == 0)
    return;

  pthread_cancel(root->monitor_thread_id);
  pthread_join(root->monitor_thread_id, NULL);

  if (root->monitor_socket_fd > 0) {
    close(root->monitor_socket_fd);
    root->monitor_socket_fd = 0;
  }
  unlink(NSA_MONITOR_SOCKET_PATH);
  pthread_mutex_destroy(&root->monitor_data_mutex);

  NSA_LOG_INFO("Monitor thread stopped.");
}

/**
 * @brief Main loop for the monitor thread.
 * This version uses a short-lived connection model as per the original design.
 * It periodically updates its data cache and serves it to any connecting
 * client.
 */
static void *nsa_monitor_thread_main(void *arg) {
  nsa_root_t *root = (nsa_root_t *)arg;
  struct sockaddr_un addr;
  int client_fd;

  root->monitor_socket_fd = socket(AF_UNIX, SOCK_STREAM, 0);
  if (root->monitor_socket_fd == -1) {
    NSA_LOG_ERROR("Failed to create monitor socket: %s", strerror(errno));
    return NULL;
  }

  memset(&addr, 0, sizeof(struct sockaddr_un));
  addr.sun_family = AF_UNIX;
  strncpy(addr.sun_path, NSA_MONITOR_SOCKET_PATH, sizeof(addr.sun_path) - 1);

  unlink(NSA_MONITOR_SOCKET_PATH);

  if (bind(root->monitor_socket_fd, (struct sockaddr *)&addr,
           sizeof(struct sockaddr_un)) < 0) {
    NSA_LOG_ERROR("Failed to bind monitor socket '%s': %s",
                  NSA_MONITOR_SOCKET_PATH, strerror(errno));
    close(root->monitor_socket_fd);
    return NULL;
  }

  if (listen(root->monitor_socket_fd, 5) < 0) {
    NSA_LOG_ERROR("Failed to listen on monitor socket: %s", strerror(errno));
    close(root->monitor_socket_fd);
    return NULL;
  }

  NSA_LOG_INFO("Monitor socket listening on %s", NSA_MONITOR_SOCKET_PATH);

  while (1) {
    // --- This loop now combines periodic updates and serving clients ---

    // 1. Update data cache
    pthread_mutex_lock(&root->monitor_data_mutex);
    nsa_monitor_update_data(&root->monitor_data);
    pthread_mutex_unlock(&root->monitor_data_mutex);

    // 2. Poll for a client connection with a timeout
    struct pollfd pfd;
    pfd.fd = root->monitor_socket_fd;
    pfd.events = POLLIN;
    int poll_ret = poll(&pfd, 1, 1000); // Wait up to 1 second for a client

    if (poll_ret > 0 && (pfd.revents & POLLIN)) {
      client_fd = accept(root->monitor_socket_fd, NULL, NULL);
      if (client_fd != -1) {
        NSA_LOG_DEBUG("Monitor client connected, serving data.");

        char req_buf[16];
        recv(client_fd, req_buf, sizeof(req_buf),
             0); // Wait for the "GET" request

        pthread_mutex_lock(&root->monitor_data_mutex);
        send(client_fd, &root->monitor_data, sizeof(nsa_monitor_data_t),
             MSG_NOSIGNAL);
        pthread_mutex_unlock(&root->monitor_data_mutex);

        close(client_fd);
      }
    }
  }
  return NULL;
}

/**
 * @brief Collects and aggregates all monitoring data, replacing hardcoded
 * values.
 */
void nsa_monitor_update_data(nsa_monitor_data_t *data) {
  dpif_stats_snapshot_t current_snapshot;

  // --- 1. Get performance snapshot from DPIF ---
  if (dpif_get_stats_snapshot(&current_snapshot) != 0) {
    NSA_LOG_WARNING(
        "Failed to get stats snapshot from DPIF. Monitor data may be stale.");
    return;
  }

  // --- Enhanced: Run performance analysis ---
  if (nsa_analyze_performance() != 0) {
    NSA_LOG_DEBUG("Performance analysis failed or not ready");
  }

  uint64_t hz = rte_get_timer_hz();
  double interval_sec =
      (hz > 0 && current_snapshot.tsc > g_rate_ctx.last_snapshot.tsc)
          ? (double)(current_snapshot.tsc - g_rate_ctx.last_snapshot.tsc) / hz
          : 1.0;

  // --- 2. Calculate and populate global metrics from the snapshot ---
  uint64_t current_total_pkts = 0;
  uint64_t current_active_sessions = 0;
  uint64_t last_total_pkts = 0;

  for (int i = 0; i < DPIF_MAX_MONITORED_CORES; ++i) {
    current_total_pkts += current_snapshot.core_stats[i].rx_pkts;
    current_active_sessions += current_snapshot.core_stats[i].sessions;
    last_total_pkts += g_rate_ctx.last_snapshot.core_stats[i].rx_pkts;
  }

  data->total_pps = (current_total_pkts > last_total_pkts)
                        ? (current_total_pkts - last_total_pkts) / interval_sec
                        : 0;
  data->total_bps =
      data->total_pps * 512 * 8; // Estimate using average packet size
  data->total_cps =
      0; // Placeholder, requires dedicated "new session" counter from DPIF
  data->active_sessions = current_active_sessions;
  data->total_sessions_capacity = 1000000; // This should come from config

  // --- 3. Populate per-core stats from the snapshot ---
  int core_display_idx = 0;
  for (int i = 0;
       i < DPIF_MAX_MONITORED_CORES && core_display_idx < MAX_MONITOR_CORES;
       ++i) {
    const dpif_core_stats_snapshot_t *core_snap =
        &current_snapshot.core_stats[i];
    if (core_snap->type[0] == '\0')
      continue; // Skip inactive cores

    nsa_monitor_core_stats_t *mon_core = &data->core_stats[core_display_idx];
    const dpif_core_stats_snapshot_t *last_core_snap =
        &g_rate_ctx.last_snapshot.core_stats[i];
    mon_core->lcore_id = core_snap->lcore_id;
    strncpy(mon_core->type, core_snap->type, sizeof(mon_core->type) - 1);
    mon_core->cpu_usage = 100; // CPU usage remains a placeholder

    int core_pps =
        (core_snap->rx_pkts > last_core_snap->rx_pkts)
            ? (core_snap->rx_pkts - last_core_snap->rx_pkts) / interval_sec
            : 0;

    mon_core->rate = core_pps; // Assuming 'rate' in original struct is PPS
    mon_core->ring_usage = core_snap->rx_pkts; // tmp use ring_usage
    mon_core->offload_rate =
        (core_snap->tasks_offloaded > last_core_snap->tasks_offloaded)
            ? (core_snap->tasks_offloaded - last_core_snap->tasks_offloaded) /
                  interval_sec
            : 0;

    // mon_core->ring_usage = core_snap->ring_count;
    mon_core->ring_capacity = core_snap->ring_capacity;
    mon_core->sessions = core_snap->sessions;

    core_display_idx++;
  }
  // Clear any remaining slots in the monitor data structure
  for (; core_display_idx < MAX_MONITOR_CORES; ++core_display_idx) {
    memset(&data->core_stats[core_display_idx], 0,
           sizeof(nsa_monitor_core_stats_t));
  }

  // --- 4. Populate latest sessions from CDB using the SAFE ITERATOR ---

  // First, clear the session area in the data structure
  memset(data->sessions, 0, sizeof(data->sessions));

  // Prepare context for the callback
  monitor_iterator_ctx_t iterator_ctx = {.monitor_data = data, .count = 0};

  // Call the safe iterator with our callback function
  nsa_cdb_enriched_session_iterate(monitor_fill_sessions_callback,
                                   &iterator_ctx);

  // --- 5. Populate Event Logs (using placeholder data as before) ---
  nsa_log_get_latest(data->logs, MAX_MONITOR_LOGS);

  // --- Finally, update the rate context for the next iteration ---
  g_rate_ctx.last_snapshot = current_snapshot;
}