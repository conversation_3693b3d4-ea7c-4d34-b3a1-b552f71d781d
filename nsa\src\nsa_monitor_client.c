#include <ncurses.h>
#include <pthread.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/time.h>

#define NSA_MONITOR_SOCKET_PATH "/tmp/nsa_monitor.sock"
#define MAX_MONITOR_CORES 8
#define MAX_MONITOR_SESSIONS 10
#define MAX_MONITOR_LOGS 5
#define NUM_SESSIONS 200

typedef struct {
    int lcore_id;
    char type[8];
    int cpu_usage;
    int rate;
    int offload_rate;
    int ring_usage;
    int ring_capacity;
    int sessions;
} nsa_monitor_core_stats_t;

typedef struct {
    char protocol[8];
    char src[128];
    char dst[128];
    char app[20];
    char alp[20];
    char user[20];
    char device[20];
    char verdict[20];
    int verdict_color_pair;
    char details[32];
    char icon[5];
    char threat[20];
} nsa_monitor_session_info_t;

typedef struct {
    int color_pair;
    char level[12];
    char message[256];
} nsa_monitor_event_log_t;

typedef struct {
    double total_bps;
    double total_pps;
    double total_cps;
    long long active_sessions;
    long long total_sessions_capacity;
    int session_timeouts;
    nsa_monitor_core_stats_t core_stats[MAX_MONITOR_CORES];
    nsa_monitor_session_info_t sessions[NUM_SESSIONS];
    nsa_monitor_event_log_t logs[MAX_MONITOR_LOGS];
} nsa_monitor_data_t;

typedef enum { VIEW_DASHBOARD, VIEW_SESSION_LIST } view_mode_t;

static volatile int ui_should_exit = 0;
static view_mode_t current_view = VIEW_DASHBOARD;
static int session_scroll_top = 0;
static int session_selected_line = 0;
static int paused = 0;

void draw_progress_bar(int y, int x, int width, float percentage) {
    int solid_width = (int) (width * percentage);
    if (solid_width < 0) solid_width = 0;
    if (solid_width > width) solid_width = width;

    mvaddch(y, x, '[');
    attron(A_REVERSE);
    for (int i = 0; i < solid_width; i++) {
        mvaddch(y, x + 1 + i, ' ');
    }
    attroff(A_REVERSE);

    for (int i = 0; i < width - solid_width; i++) {
        mvaddch(y, x + 1 + solid_width + i, '-');
    }
    mvaddch(y, x + 1 + width, ']');
}

void print_session_line(int y, const nsa_monitor_session_info_t *s, int is_highlighted) {
    if (is_highlighted) {
        attron(A_REVERSE);
    }

    int x = 3;
    char buffer[COLS];

    snprintf(buffer, sizeof(buffer), "%-8s | %-31s | %-31s | %-15s | %-15s | %-15s | %-17s | ",
             s->protocol, s->src, s->dst, s->app, s->alp, s->device, s->user);

    mvprintw(y, x, "%s", buffer);

    x = 3 + 8 + 3 + 31 + 3 + 31 + 3 + 15 + 3 + 15 + 3 + 15 + 3 + 17 + 3;

    attron(COLOR_PAIR(s->verdict_color_pair) | A_BOLD);
    mvprintw(y, x, "%-4s %-10s", s->icon, s->verdict);
    attroff(COLOR_PAIR(s->verdict_color_pair) | A_BOLD);

    if (is_highlighted) {
        attroff(A_REVERSE);
    }
}

void draw_layout_and_titles() {
    box(stdscr, 0, 0);
    attron(A_REVERSE);
    mvprintw(0, 70, " NSA Monitoring Copilot ");
    attroff(A_REVERSE);
    mvprintw(LINES - 1, 3, "Press 'ls' for session list, 'q' to quit.");

    char title_buffer[256];
    time_t now = time(0);
    struct tm *tm_info = localtime(&now);
    strftime(title_buffer, sizeof(title_buffer), " > Global Performance Metrics (Last updated: %Y-%m-%d %H:%M:%S) ", tm_info);
    mvaddch(2, 0, ACS_LTEE);
    mvprintw(2, 2, "%s", title_buffer);
    mvhline(2, 2 + strlen(title_buffer), ACS_HLINE, COLS - 3 - strlen(title_buffer));
    mvaddch(2, COLS - 1, ACS_RTEE);

    snprintf(title_buffer, sizeof(title_buffer), " > Thread Performance Metrics ");
    mvaddch(8, 0, ACS_LTEE);
    mvprintw(8, 2, "%s", title_buffer);
    mvhline(8, 2 + strlen(title_buffer), ACS_HLINE, COLS - 3 - strlen(title_buffer));
    mvaddch(8, COLS - 1, ACS_RTEE);

    snprintf(title_buffer, sizeof(title_buffer), " > NSA Enriched Session Pool (Showing latest 10) ");
    mvaddch(22, 0, ACS_LTEE);
    mvprintw(22, 2, "%s", title_buffer);
    mvhline(22, 2 + strlen(title_buffer), ACS_HLINE, COLS - 3 - strlen(title_buffer));
    mvaddch(22, COLS - 1, ACS_RTEE);

    snprintf(title_buffer, sizeof(title_buffer), " > Event & Alert Log ");
    mvaddch(38, 0, ACS_LTEE);
    mvprintw(38, 2, "%s", title_buffer);
    mvhline(38, 2 + strlen(title_buffer), ACS_HLINE, COLS - 3 - strlen(title_buffer));
    mvaddch(38, COLS - 1, ACS_RTEE);
}

void render_global_metrics(const nsa_monitor_data_t *data) {
    mvprintw(4, 6, "throughput (BPS)");
    mvprintw(4, 33, "packet rate (PPS)");
    mvprintw(4, 61, "new connections (CPS-TBD)");
    mvprintw(4, 88, "active sessions");
    draw_progress_bar(5, 6, 22, (float) data->total_bps / 20e9);
    mvprintw(6, 6, "%.2f Gbps", data->total_bps / 1e9);
    draw_progress_bar(5, 33, 22, (float) data->total_pps / 3e6);
    mvprintw(6, 33, "%.2f Mpps", data->total_pps / 1e6);
    draw_progress_bar(5, 61, 22, (float) data->total_cps / 20e3);
    mvprintw(6, 61, "%.1f k/s", data->total_cps / 1e3);
    draw_progress_bar(5, 88, 20, (float) data->active_sessions / data->total_sessions_capacity);
    mvprintw(6, 88, "%lld", data->active_sessions);
}

void render_core_stats(const nsa_monitor_core_stats_t *cores, int num_cores) {
    #if 0
    for (int i = 0; i < num_cores; ++i) {
        int y = 10 + i;
        mvprintw(y, 3, "[%s Core %d]:", cores[i].type, cores[i].lcore_id);
        mvprintw(y, 19, "CPU: %3d%%", cores[i].cpu_usage);
        mvaddch(y, 28, ACS_VLINE);
        mvprintw(y, 30, "PPS: %-7.1fk", cores[i].rate / 1e3);
    }
    #endif

    int core_y = 9;

    attron(A_BOLD);
    mvprintw(core_y + 1, 4, "CORE | TYPE   | CPU  | PPS       | PKT       | Sessions ");
    attroff(A_BOLD);
    mvprintw(core_y + 2, 2, "-------------------------------------------------------------------------");
    int core_display_count = 0;
    for (int i=0; i < num_cores; ++i) {
        if (core_display_count >= 10) break; 

        const nsa_monitor_core_stats_t *core = &cores[i];
        if (core->type[0] == '\0') continue;

        int y = core_y + 3 + core_display_count;
        //float ring_perc = (core->ring_capacity > 0) ? (float)core->ring_usage / core->ring_capacity : 0;

        mvprintw(y, 4, "%-4d | %-6s | %3d%% | %-8.2d  | %-8.2d  | %-8.1d |", 
            core->lcore_id, core->type, core->cpu_usage, 
            core->rate,
            core->ring_usage,
            core->sessions);
        core_display_count++;
    }

}

void render_session_feed(const nsa_monitor_session_info_t *sessions, int num_sessions) {
    attron(A_BOLD);
    mvprintw(24, 3, "Protocol | Source IP:Port                  | Destination IP:Port             | Application     | Protocol        | Device          | User              | Verdict           ");
    attroff(A_BOLD);
    mvprintw(25, 1, "-----------+---------------------------------+---------------------------------+-----------------+-----------------+-----------------+-------------------+-------------------");

    for (int i = 0; i < num_sessions; ++i) {
        int y = 26 + i;
        print_session_line(y, &sessions[i], FALSE);
    }
}

void render_log_feed(const nsa_monitor_event_log_t *logs, int num_logs) {
    for (int i = 0; i < num_logs; ++i) {
        int y = 40 + i;
        const nsa_monitor_event_log_t *log = &logs[i];
        attron(COLOR_PAIR(log->color_pair) | A_BOLD);
        mvprintw(y, 3, "%-10s", log->level);
        attroff(COLOR_PAIR(log->color_pair) | A_BOLD);
        mvprintw(y, 14, "%s", log->message);
    }
}

void render_all_sessions_view(const nsa_monitor_session_info_t *sessions, int total_sessions)
{
    // Title
    attron(A_REVERSE);
    mvprintw(0, 1, " All Sessions List ");
    attroff(A_REVERSE);
    mvprintw(0, COLS - 50, "Use UP/DOWN/PGUP/PGDN to scroll, 'd' to return.");

    // Header
    attron(A_BOLD);
    mvprintw(2, 3, "Protocol | Source IP:Port                  | Destination IP:Port             | Application     | Protocol            | Device          | User            | Verdict           ");
    attroff(A_BOLD);
    mvprintw(3, 1, "-----------+---------------------------------+---------------------------------+-----------------+-----------------+-----------------+-------------------+-------------------");

    // Content
    int viewable_lines = LINES - 5; // Total lines available for sessions
    for (int i = 0; i < viewable_lines; i++)
    {
        int session_index = session_scroll_top + i;
        if (session_index >= total_sessions)
            break;

        print_session_line(4 + i, &sessions[session_index], session_index == session_selected_line);
    }

    // Footer
    attron(A_REVERSE);
    mvprintw(LINES - 1, 1, " Line %d / %d ", session_selected_line + 1, total_sessions);
    attroff(A_REVERSE);
}

int main() {
    int sock_fd = -1;
    struct sockaddr_un addr;
    nsa_monitor_data_t monitor_data;

    initscr();
    cbreak();
    noecho();
    curs_set(0);
    nodelay(stdscr, TRUE);
    keypad(stdscr, TRUE);
    srand(time(NULL));

    if (has_colors()) {
        start_color();
        init_pair(1, COLOR_GREEN, COLOR_BLACK);
        init_pair(2, COLOR_YELLOW, COLOR_BLACK);
        init_pair(3, COLOR_RED, COLOR_BLACK);
    }

    struct timeval last_pull = {0, 0};
    int pull_interval_ms = 3000;

    while (!ui_should_exit) {
        int ch = getch();

        if (ch == 'p' || ch == 'P') {
            paused = !paused;
        }
        if (sock_fd == -1) {
            erase();
            mvprintw(LINES / 2, (COLS - 29) / 2, "Connecting to nsad daemon...");
            refresh();

            sock_fd = socket(AF_UNIX, SOCK_STREAM, 0);
            if (sock_fd != -1) {
                fcntl(sock_fd, F_SETFL, O_NONBLOCK);
                memset(&addr, 0, sizeof(struct sockaddr_un));
                addr.sun_family = AF_UNIX;
                strncpy(addr.sun_path, NSA_MONITOR_SOCKET_PATH, sizeof(addr.sun_path) - 1);

                if (connect(sock_fd, (struct sockaddr *)&addr, sizeof(struct sockaddr_un)) == -1) {
                    close(sock_fd);
                    sock_fd = -1;
                    sleep(1);
                }
            }
        }

        struct timeval now;
        gettimeofday(&now, NULL);
        long elapsed = (now.tv_sec - last_pull.tv_sec) * 1000 + (now.tv_usec - last_pull.tv_usec) / 1000;
        if (elapsed >= pull_interval_ms && sock_fd != -1 && !paused) {
            const char *req = "GET";
            send(sock_fd, req, strlen(req), 0);
            last_pull = now;
        }

        if (sock_fd != -1) {
            if (!paused) {
                ssize_t bytes_received = recv(sock_fd, &monitor_data, sizeof(nsa_monitor_data_t), 0);
                if (bytes_received == 0) {
                    close(sock_fd);
                    sock_fd = -1;
                    continue;
                } else if (bytes_received < 0) {
                    if (errno != EAGAIN && errno != EWOULDBLOCK) {
                        close(sock_fd);
                        sock_fd = -1;
                        continue;
                    }
                    
                }
            }
            if (ch == 'q' || ch == 'Q')
            {
                ui_should_exit = 1;
                continue;
            }

            // --- View-specific Input Handling ---
            switch (current_view)
            {
            case VIEW_DASHBOARD:
                if (ch == 's') {
                    current_view = VIEW_SESSION_LIST;
                    nodelay(stdscr, FALSE); // Switch to blocking input
                }
                break;

            case VIEW_SESSION_LIST:
                int viewable_lines = LINES - 5;
                switch (ch)
                {
                case KEY_UP:
                    if (session_selected_line > 0)
                        session_selected_line--;
                    break;
                case KEY_DOWN:
                    if (session_selected_line < NUM_SESSIONS - 1)
                        session_selected_line++;
                    break;
                case KEY_PPAGE: // Page Up
                    session_selected_line -= viewable_lines;
                    if (session_selected_line < 0)
                        session_selected_line = 0;
                    break;
                case KEY_NPAGE: // Page Down
                    session_selected_line += viewable_lines;
                    if (session_selected_line >= NUM_SESSIONS)
                        session_selected_line = NUM_SESSIONS - 1;
                    break;
                case 'd':
                    current_view = VIEW_DASHBOARD;
                    nodelay(stdscr, TRUE); // Switch back to non-blocking
                    break;
                }

                // Ensure selected line is always in view
                if (session_selected_line < session_scroll_top)
                {
                    session_scroll_top = session_selected_line;
                }
                if (session_selected_line >= session_scroll_top + viewable_lines)
                {
                    session_scroll_top = session_selected_line - viewable_lines + 1;
                }
                break;
            }

            erase();
            switch (current_view) {
                case VIEW_DASHBOARD:
                    draw_layout_and_titles();
                    render_global_metrics(&monitor_data);
                    render_core_stats(monitor_data.core_stats, 10);
                    render_session_feed(monitor_data.sessions, MAX_MONITOR_SESSIONS);
                    render_log_feed(monitor_data.logs, MAX_MONITOR_LOGS);
                    break;
                case VIEW_SESSION_LIST:
                    render_all_sessions_view(monitor_data.sessions, NUM_SESSIONS);
                    break;
            }
            if (paused) {
                attron(A_REVERSE);
                mvprintw(0, COLS - 12, "[PAUSED]");
                attroff(A_REVERSE);
            }
            refresh();
        }
    }

    if (sock_fd != -1) close(sock_fd);
    endwin();
    //printf("NSA UI Cockpit stopped.\n");
    return 0;
}
