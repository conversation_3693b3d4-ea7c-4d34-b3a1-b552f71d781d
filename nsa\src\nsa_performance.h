#ifndef _NSA_PERFORMANCE_H_
#define _NSA_PERFORMANCE_H_

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

// Performance optimization levels
typedef enum {
    NSA_PERF_LEVEL_BALANCED = 0,    // Default balanced mode
    NSA_PERF_LEVEL_THROUGHPUT = 1,  // Optimize for throughput
    NSA_PERF_LEVEL_LATENCY = 2,     // Optimize for low latency
    NSA_PERF_LEVEL_SECURITY = 3,    // Optimize for security (deep inspection)
    NSA_PERF_LEVEL_POWER_SAVE = 4   // Optimize for power consumption
} nsa_performance_level_t;

// CPU affinity configuration
typedef struct {
    bool enabled;
    uint32_t rx_cpu_mask;           // CPU mask for RX threads
    uint32_t worker_cpu_mask;       // CPU mask for worker threads
    uint32_t helper_cpu_mask;       // CPU mask for helper threads
    uint32_t management_cpu_mask;   // CPU mask for management threads
    bool isolate_cpus;              // Isolate CPUs from OS scheduler
} nsa_cpu_affinity_config_t;

// Memory optimization configuration
typedef struct {
    uint32_t mbuf_pool_size;        // Number of mbufs in pool
    uint32_t session_pool_size;     // Number of sessions in pool
    uint32_t work_pool_size;        // Number of work items in pool
    uint32_t ring_size;             // Size of communication rings
    bool huge_pages_enabled;        // Use huge pages for memory pools
    uint32_t numa_node;             // NUMA node for memory allocation
} nsa_memory_config_t;

// DPI optimization configuration
typedef struct {
    bool enable_early_detection;    // Stop DPI analysis after app detection
    uint32_t max_packets_per_flow;  // Maximum packets to analyze per flow
    uint32_t max_bytes_per_flow;    // Maximum bytes to analyze per flow
    bool enable_signature_caching;  // Cache signature matching results
    uint32_t signature_cache_size;  // Size of signature cache
    bool enable_flow_optimization;  // Optimize flows based on patterns
} nsa_dpi_optimization_t;

// Performance monitoring thresholds
typedef struct {
    uint64_t max_packets_per_second;    // Alert threshold for PPS
    uint64_t max_bits_per_second;       // Alert threshold for BPS
    uint32_t max_cpu_utilization;       // Alert threshold for CPU usage (%)
    uint32_t max_memory_utilization;    // Alert threshold for memory usage (%)
    uint32_t max_session_table_usage;   // Alert threshold for session table (%)
    uint32_t max_latency_microseconds;  // Alert threshold for packet latency
    uint32_t max_queue_depth;           // Alert threshold for queue depth
} nsa_performance_thresholds_t;

// Real-time performance metrics
typedef struct {
    time_t timestamp;
    
    // Traffic metrics
    uint64_t current_pps;           // Current packets per second
    uint64_t current_bps;           // Current bits per second
    uint64_t peak_pps;              // Peak packets per second
    uint64_t peak_bps;              // Peak bits per second
    
    // Latency metrics (in microseconds)
    uint32_t avg_packet_latency;    // Average packet processing latency
    uint32_t min_packet_latency;    // Minimum packet processing latency
    uint32_t max_packet_latency;    // Maximum packet processing latency
    uint32_t p95_packet_latency;    // 95th percentile latency
    uint32_t p99_packet_latency;    // 99th percentile latency
    
    // Resource utilization
    uint32_t cpu_utilization[64];   // Per-CPU utilization (%)
    uint32_t memory_utilization;    // Overall memory utilization (%)
    uint32_t cache_hit_rate;        // DPI cache hit rate (%)
    
    // Session metrics
    uint32_t active_sessions;       // Current active sessions
    uint32_t session_setup_rate;    // Sessions established per second
    uint32_t session_teardown_rate; // Sessions torn down per second
    uint32_t session_timeout_rate;  // Sessions timed out per second
    
    // Queue and buffer metrics
    uint32_t rx_queue_depth[32];    // RX queue depth per thread
    uint32_t worker_queue_depth[32]; // Worker queue depth per thread
    uint32_t completion_queue_depth[32]; // Completion queue depth per thread
    uint32_t dropped_packets;       // Packets dropped due to overload
    
    // DPI performance
    uint32_t dpi_processing_time;   // Average DPI processing time (us)
    uint32_t apps_detected_per_sec; // Applications detected per second
    uint32_t threats_detected_per_sec; // Threats detected per second
    uint32_t policy_evaluations_per_sec; // Policy evaluations per second
    
    // Error rates
    uint32_t packet_errors_per_sec; // Packet processing errors per second
    uint32_t dpi_errors_per_sec;    // DPI analysis errors per second
    uint32_t policy_errors_per_sec; // Policy evaluation errors per second
} nsa_realtime_metrics_t;

// Performance optimization suggestions
typedef struct {
    bool suggest_cpu_rebalancing;   // Suggest CPU affinity changes
    bool suggest_memory_increase;   // Suggest memory pool size increase
    bool suggest_queue_size_increase; // Suggest queue size increase
    bool suggest_dpi_optimization;  // Suggest DPI parameter tuning
    bool suggest_signature_update;  // Suggest signature database optimization
    char detailed_suggestions[1024]; // Detailed optimization suggestions
} nsa_optimization_suggestions_t;

// Performance tuning API
int nsa_performance_init(void);
void nsa_performance_cleanup(void);

// Configuration management
int nsa_performance_set_level(nsa_performance_level_t level);
nsa_performance_level_t nsa_performance_get_level(void);

int nsa_performance_set_cpu_affinity(const nsa_cpu_affinity_config_t *config);
int nsa_performance_get_cpu_affinity(nsa_cpu_affinity_config_t *config);

int nsa_performance_set_memory_config(const nsa_memory_config_t *config);
int nsa_performance_get_memory_config(nsa_memory_config_t *config);

int nsa_performance_set_dpi_optimization(const nsa_dpi_optimization_t *config);
int nsa_performance_get_dpi_optimization(nsa_dpi_optimization_t *config);

// Monitoring and metrics
int nsa_performance_get_realtime_metrics(nsa_realtime_metrics_t *metrics);
int nsa_performance_get_historical_metrics(time_t start_time, time_t end_time,
                                          nsa_realtime_metrics_t *metrics_array,
                                          uint32_t max_entries, uint32_t *actual_entries);

// Threshold management
int nsa_performance_set_thresholds(const nsa_performance_thresholds_t *thresholds);
int nsa_performance_get_thresholds(nsa_performance_thresholds_t *thresholds);

// Performance analysis and optimization
int nsa_performance_analyze_system(nsa_optimization_suggestions_t *suggestions);
int nsa_performance_auto_tune(bool enable_auto_tuning);

// Benchmarking and testing
int nsa_performance_run_benchmark(uint32_t duration_seconds, nsa_realtime_metrics_t *results);
int nsa_performance_stress_test(uint32_t target_pps, uint32_t duration_seconds);

// Performance alerting
typedef void (*nsa_performance_alert_callback_t)(const char *alert_type, 
                                                const char *message, 
                                                const nsa_realtime_metrics_t *metrics,
                                                void *user_data);
int nsa_performance_register_alert_callback(nsa_performance_alert_callback_t callback, 
                                           void *user_data);
int nsa_performance_unregister_alert_callback(nsa_performance_alert_callback_t callback);

// Performance profiling
int nsa_performance_enable_profiling(bool enable);
int nsa_performance_get_profile_data(char *profile_data, size_t max_size);
int nsa_performance_reset_profile_data(void);

// Hot path optimization
int nsa_performance_optimize_hot_paths(void);
int nsa_performance_identify_bottlenecks(char *bottleneck_report, size_t max_size);

// Dynamic load balancing
int nsa_performance_enable_dynamic_load_balancing(bool enable);
int nsa_performance_rebalance_threads(void);

#ifdef __cplusplus
}
#endif

#endif // _NSA_PERFORMANCE_H_ 