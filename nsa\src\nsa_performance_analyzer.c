/**
 * @file nsa_performance_analyzer.c
 * @brief Enhanced NSA Performance Analysis and Monitoring Implementation
 */

#include "nsa_performance_analyzer.h"
#include "nsa.h"
#include "nsa_logging.h"
#include <math.h>
#include <rte_cycles.h>
#include <rte_malloc.h>
#include <rte_mempool.h>
#include <stdlib.h>
#include <string.h>

/* Global performance analyzer context */
static nsa_performance_analyzer_t *g_perf_analyzer = NULL;

/* Performance thresholds for bottleneck detection */
static const struct {
  double cpu_utilization_threshold;
  double memory_utilization_threshold;
  double queue_depth_threshold;
  double latency_threshold_ns;
  double cache_hit_rate_threshold;
} perf_thresholds = {
    .cpu_utilization_threshold = 85.0,    /* 85% CPU utilization */
    .memory_utilization_threshold = 90.0, /* 90% memory utilization */
    .queue_depth_threshold = 0.8,         /* 80% queue depth */
    .latency_threshold_ns = 100000,       /* 100us latency */
    .cache_hit_rate_threshold = 95.0      /* 95% cache hit rate */
};

/**
 * @brief Initialize the performance analysis engine
 */
int nsa_performance_analyzer_init(void) {
  if (g_perf_analyzer != NULL) {
    NSA_LOG_WARNING("Performance analyzer already initialized");
    return 0;
  }

  g_perf_analyzer =
      rte_zmalloc("nsa_perf_analyzer", sizeof(nsa_performance_analyzer_t),
                  RTE_CACHE_LINE_SIZE);
  if (!g_perf_analyzer) {
    NSA_LOG_ERROR("Failed to allocate memory for performance analyzer");
    return -1;
  }

  /* Allocate metrics history */
  g_perf_analyzer->metrics_history =
      rte_zmalloc("nsa_metrics_history",
                  sizeof(nsa_enhanced_metrics_t) * NSA_PERF_MAX_METRICS_HISTORY,
                  RTE_CACHE_LINE_SIZE);
  if (!g_perf_analyzer->metrics_history) {
    NSA_LOG_ERROR("Failed to allocate memory for metrics history");
    rte_free(g_perf_analyzer);
    g_perf_analyzer = NULL;
    return -1;
  }

  /* Initialize analyzer configuration */
  g_perf_analyzer->enabled = true;
  g_perf_analyzer->auto_tuning_enabled = false;
  g_perf_analyzer->analysis_interval_ms = NSA_PERF_ANALYSIS_INTERVAL_MS;
  g_perf_analyzer->history_size = NSA_PERF_MAX_METRICS_HISTORY;
  g_perf_analyzer->history_head = 0;
  g_perf_analyzer->history_count = 0;
  g_perf_analyzer->bottleneck_count = 0;
  g_perf_analyzer->recommendation_count = 0;
  g_perf_analyzer->last_analysis_tsc = 0;
  g_perf_analyzer->total_analyses = 0;
  g_perf_analyzer->auto_tuning_actions = 0;

  NSA_LOG_INFO("Performance analyzer initialized successfully");
  return 0;
}

/**
 * @brief Cleanup the performance analysis engine
 */
void nsa_performance_analyzer_cleanup(void) {
  if (g_perf_analyzer) {
    if (g_perf_analyzer->metrics_history) {
      rte_free(g_perf_analyzer->metrics_history);
    }
    rte_free(g_perf_analyzer);
    g_perf_analyzer = NULL;
  }
  NSA_LOG_INFO("Performance analyzer cleaned up");
}

/**
 * @brief Collect enhanced performance metrics
 */
int nsa_collect_enhanced_metrics(nsa_enhanced_metrics_t *metrics) {
  if (!metrics || !g_perf_analyzer) {
    return -1;
  }

  memset(metrics, 0, sizeof(nsa_enhanced_metrics_t));

  /* Set timestamp and TSC */
  metrics->timestamp = time(NULL);
  metrics->collection_tsc = rte_rdtsc();

  /* Collect DPIF statistics */
  dpif_stats_snapshot_t dpif_stats;
  if (dpif_get_stats_snapshot(&dpif_stats) == 0) {
    /* Calculate traffic metrics */
    uint64_t total_pkts = 0;
    for (int i = 0; i < DPIF_MAX_MONITORED_CORES; i++) {
      total_pkts += dpif_stats.core_stats[i].rx_pkts;
    }

    /* Calculate PPS from previous measurement */
    if (g_perf_analyzer->history_count > 0) {
      uint32_t prev_idx =
          (g_perf_analyzer->history_head - 1 + g_perf_analyzer->history_size) %
          g_perf_analyzer->history_size;
      nsa_enhanced_metrics_t *prev_metrics =
          &g_perf_analyzer->metrics_history[prev_idx];

      uint64_t tsc_diff =
          metrics->collection_tsc - prev_metrics->collection_tsc;
      uint64_t pkt_diff = total_pkts - prev_metrics->traffic.current_pps;

      if (tsc_diff > 0) {
        double time_diff_sec = (double)tsc_diff / rte_get_timer_hz();
        metrics->traffic.current_pps = (uint64_t)(pkt_diff / time_diff_sec);
      }
    }

    /* Update traffic statistics */
    if (g_perf_analyzer->history_count > 0) {
      uint64_t sum_pps = 0;
      uint64_t min_pps = UINT64_MAX;
      uint64_t max_pps = 0;

      for (uint32_t i = 0; i < g_perf_analyzer->history_count; i++) {
        uint64_t pps = g_perf_analyzer->metrics_history[i].traffic.current_pps;
        sum_pps += pps;
        if (pps < min_pps)
          min_pps = pps;
        if (pps > max_pps)
          max_pps = pps;
      }

      metrics->traffic.avg_pps = sum_pps / g_perf_analyzer->history_count;
      metrics->traffic.min_pps = min_pps;
      metrics->traffic.peak_pps = max_pps;
    }

    /* Collect session metrics */
    uint32_t total_sessions = 0;
    for (int i = 0; i < DPIF_MAX_MONITORED_CORES; i++) {
      total_sessions += dpif_stats.core_stats[i].sessions;
    }
    metrics->sessions.active_sessions = total_sessions;

    /* Collect queue metrics */
    for (int i = 0; i < DPIF_MAX_MONITORED_CORES && i < 32; i++) {
      metrics->queues.rx_queue_depth[i] =
          dpif_stats.core_stats[i].completion_ring_count;
      metrics->queues.worker_queue_depth[i] =
          dpif_stats.core_stats[i].task_ring_count;
    }
  }

  /* Collect memory pool utilization */
  extern dpif_global_context_t *g_dpif_ctx;
  if (g_dpif_ctx) {
    if (g_dpif_ctx->mbuf_pool) {
      uint32_t available = rte_mempool_avail_count(g_dpif_ctx->mbuf_pool);
      uint32_t total = rte_mempool_count(g_dpif_ctx->mbuf_pool);
      metrics->memory_pools.mbuf_pool_utilization =
          total > 0 ? ((total - available) * 100) / total : 0;
    }

    if (g_dpif_ctx->session_pool) {
      uint32_t available = rte_mempool_avail_count(g_dpif_ctx->session_pool);
      uint32_t total = rte_mempool_count(g_dpif_ctx->session_pool);
      metrics->memory_pools.session_pool_utilization =
          total > 0 ? ((total - available) * 100) / total : 0;
    }

    if (g_dpif_ctx->work_pool) {
      uint32_t available = rte_mempool_avail_count(g_dpif_ctx->work_pool);
      uint32_t total = rte_mempool_count(g_dpif_ctx->work_pool);
      metrics->memory_pools.work_pool_utilization =
          total > 0 ? ((total - available) * 100) / total : 0;
    }
  }

  /* Simulate some additional metrics for demonstration */
  metrics->latency.avg_packet_latency_ns =
      50000 + (rand() % 20000); /* 50-70us */
  metrics->latency.p95_latency_ns = metrics->latency.avg_packet_latency_ns * 2;
  metrics->latency.p99_latency_ns = metrics->latency.avg_packet_latency_ns * 3;

  metrics->resources.cache_hit_rate = 95 + (rand() % 5);      /* 95-99% */
  metrics->resources.memory_utilization = 60 + (rand() % 20); /* 60-80% */

  metrics->dpi.dpi_processing_time_ns = 10000 + (rand() % 5000); /* 10-15us */
  metrics->dpi.apps_detected_per_sec = 100 + (rand() % 50);
  metrics->dpi.threats_detected_per_sec = rand() % 10;

  return 0;
}

/**
 * @brief Store metrics in history
 */
static void store_metrics_in_history(const nsa_enhanced_metrics_t *metrics) {
  if (!g_perf_analyzer || !metrics) {
    return;
  }

  /* Store metrics in circular buffer */
  memcpy(&g_perf_analyzer->metrics_history[g_perf_analyzer->history_head],
         metrics, sizeof(nsa_enhanced_metrics_t));

  g_perf_analyzer->history_head =
      (g_perf_analyzer->history_head + 1) % g_perf_analyzer->history_size;

  if (g_perf_analyzer->history_count < g_perf_analyzer->history_size) {
    g_perf_analyzer->history_count++;
  }
}

/**
 * @brief Analyze performance and detect bottlenecks
 */
int nsa_analyze_performance(void) {
  if (!g_perf_analyzer || !g_perf_analyzer->enabled) {
    return -1;
  }

  /* Check if it's time for analysis */
  uint64_t current_tsc = rte_rdtsc();
  uint64_t tsc_diff = current_tsc - g_perf_analyzer->last_analysis_tsc;
  double time_diff_ms = (double)tsc_diff / rte_get_timer_hz() * 1000.0;

  if (time_diff_ms < g_perf_analyzer->analysis_interval_ms) {
    return 0; /* Not time for analysis yet */
  }

  /* Collect current metrics */
  nsa_enhanced_metrics_t current_metrics;
  if (nsa_collect_enhanced_metrics(&current_metrics) != 0) {
    NSA_LOG_ERROR("Failed to collect performance metrics");
    return -1;
  }

  /* Store metrics in history */
  store_metrics_in_history(&current_metrics);

  /* Detect bottlenecks */
  uint32_t detected_count = 0;
  nsa_detect_bottlenecks(&current_metrics, g_perf_analyzer->bottlenecks,
                         NSA_PERF_MAX_BOTTLENECKS, &detected_count);

  g_perf_analyzer->bottleneck_count = detected_count;

  /* Generate recommendations */
  uint32_t recommendation_count = 0;
  nsa_generate_recommendations(
      g_perf_analyzer->bottlenecks, g_perf_analyzer->bottleneck_count,
      g_perf_analyzer->recommendations, NSA_PERF_MAX_RECOMMENDATIONS,
      &recommendation_count);

  g_perf_analyzer->recommendation_count = recommendation_count;

  /* Update analysis state */
  g_perf_analyzer->last_analysis_tsc = current_tsc;
  g_perf_analyzer->total_analyses++;

  /* Log analysis results */
  if (detected_count > 0) {
    NSA_LOG_INFO("Performance analysis detected %u bottlenecks, generated %u "
                 "recommendations",
                 detected_count, recommendation_count);
  }

  return 0;
}

/**
 * @brief Detect performance bottlenecks
 */
int nsa_detect_bottlenecks(const nsa_enhanced_metrics_t *metrics,
                           nsa_bottleneck_t *bottlenecks,
                           uint32_t max_bottlenecks, uint32_t *detected_count) {
  if (!metrics || !bottlenecks || !detected_count) {
    return -1;
  }

  *detected_count = 0;
  uint32_t count = 0;

  /* Check CPU utilization bottleneck */
  for (int i = 0; i < 64 && count < max_bottlenecks; i++) {
    if (metrics->resources.cpu_utilization[i] >
        perf_thresholds.cpu_utilization_threshold) {
      nsa_bottleneck_t *bottleneck = &bottlenecks[count++];
      bottleneck->type = NSA_BOTTLENECK_CPU;
      bottleneck->severity = metrics->resources.cpu_utilization[i] > 95
                                 ? NSA_SEVERITY_CRITICAL
                                 : NSA_SEVERITY_HIGH;
      bottleneck->impact_score = metrics->resources.cpu_utilization[i];
      bottleneck->first_detected = metrics->timestamp;
      bottleneck->last_seen = metrics->timestamp;
      bottleneck->occurrence_count = 1;

      snprintf(bottleneck->description, sizeof(bottleneck->description),
               "High CPU utilization detected: %u%%",
               metrics->resources.cpu_utilization[i]);
      snprintf(bottleneck->location, sizeof(bottleneck->location),
               "CPU Core %d", i);
      snprintf(bottleneck->recommendations, sizeof(bottleneck->recommendations),
               "Consider CPU affinity optimization, reduce processing load, or "
               "scale horizontally");
    }
  }

  /* Check memory utilization bottleneck */
  if (metrics->resources.memory_utilization >
          perf_thresholds.memory_utilization_threshold &&
      count < max_bottlenecks) {
    nsa_bottleneck_t *bottleneck = &bottlenecks[count++];
    bottleneck->type = NSA_BOTTLENECK_MEMORY;
    bottleneck->severity = metrics->resources.memory_utilization > 95
                               ? NSA_SEVERITY_CRITICAL
                               : NSA_SEVERITY_HIGH;
    bottleneck->impact_score = metrics->resources.memory_utilization;
    bottleneck->first_detected = metrics->timestamp;
    bottleneck->last_seen = metrics->timestamp;
    bottleneck->occurrence_count = 1;

    snprintf(bottleneck->description, sizeof(bottleneck->description),
             "High memory utilization detected: %u%%",
             metrics->resources.memory_utilization);
    snprintf(bottleneck->location, sizeof(bottleneck->location),
             "System Memory");
    snprintf(bottleneck->recommendations, sizeof(bottleneck->recommendations),
             "Increase memory pool sizes, optimize memory allocation patterns, "
             "enable huge pages");
  }

  /* Check queue depth bottlenecks */
  for (int i = 0; i < 32 && count < max_bottlenecks; i++) {
    double queue_utilization = (double)metrics->queues.rx_queue_depth[i] /
                               8192.0; /* Assuming 8192 queue size */
    if (queue_utilization > perf_thresholds.queue_depth_threshold) {
      nsa_bottleneck_t *bottleneck = &bottlenecks[count++];
      bottleneck->type = NSA_BOTTLENECK_QUEUE;
      bottleneck->severity =
          queue_utilization > 0.95 ? NSA_SEVERITY_CRITICAL : NSA_SEVERITY_HIGH;
      bottleneck->impact_score = queue_utilization * 100;
      bottleneck->first_detected = metrics->timestamp;
      bottleneck->last_seen = metrics->timestamp;
      bottleneck->occurrence_count = 1;

      snprintf(bottleneck->description, sizeof(bottleneck->description),
               "High queue utilization detected: %.1f%%",
               queue_utilization * 100);
      snprintf(bottleneck->location, sizeof(bottleneck->location),
               "RX Queue %d", i);
      snprintf(bottleneck->recommendations, sizeof(bottleneck->recommendations),
               "Increase queue sizes, optimize burst processing, balance load "
               "across queues");
    }
  }

  /* Check latency bottleneck */
  if (metrics->latency.avg_packet_latency_ns >
          perf_thresholds.latency_threshold_ns &&
      count < max_bottlenecks) {
    nsa_bottleneck_t *bottleneck = &bottlenecks[count++];
    bottleneck->type = NSA_BOTTLENECK_DPI;
    bottleneck->severity = metrics->latency.avg_packet_latency_ns > 200000
                               ? NSA_SEVERITY_CRITICAL
                               : NSA_SEVERITY_MEDIUM;
    bottleneck->impact_score = (double)metrics->latency.avg_packet_latency_ns /
                               1000.0; /* Convert to microseconds */
    bottleneck->first_detected = metrics->timestamp;
    bottleneck->last_seen = metrics->timestamp;
    bottleneck->occurrence_count = 1;

    snprintf(bottleneck->description, sizeof(bottleneck->description),
             "High packet processing latency: %u ns",
             metrics->latency.avg_packet_latency_ns);
    snprintf(bottleneck->location, sizeof(bottleneck->location), "DPI Engine");
    snprintf(bottleneck->recommendations, sizeof(bottleneck->recommendations),
             "Enable early detection, optimize PML patterns, implement "
             "caching, use prefetching");
  }

  /* Check cache hit rate */
  if (metrics->resources.cache_hit_rate <
          perf_thresholds.cache_hit_rate_threshold &&
      count < max_bottlenecks) {
    nsa_bottleneck_t *bottleneck = &bottlenecks[count++];
    bottleneck->type = NSA_BOTTLENECK_CACHE;
    bottleneck->severity = metrics->resources.cache_hit_rate < 90
                               ? NSA_SEVERITY_HIGH
                               : NSA_SEVERITY_MEDIUM;
    bottleneck->impact_score = 100 - metrics->resources.cache_hit_rate;
    bottleneck->first_detected = metrics->timestamp;
    bottleneck->last_seen = metrics->timestamp;
    bottleneck->occurrence_count = 1;

    snprintf(bottleneck->description, sizeof(bottleneck->description),
             "Low cache hit rate: %u%%", metrics->resources.cache_hit_rate);
    snprintf(bottleneck->location, sizeof(bottleneck->location),
             "Cache System");
    snprintf(bottleneck->recommendations, sizeof(bottleneck->recommendations),
             "Optimize data structures for cache locality, increase cache "
             "sizes, implement prefetching");
  }

  *detected_count = count;
  return 0;
}

/**
 * @brief Generate optimization recommendations
 */
int nsa_generate_recommendations(
    const nsa_bottleneck_t *bottlenecks, uint32_t bottleneck_count,
    nsa_optimization_recommendation_t *recommendations,
    uint32_t max_recommendations, uint32_t *generated_count) {
  if (!bottlenecks || !recommendations || !generated_count) {
    return -1;
  }

  *generated_count = 0;
  uint32_t count = 0;

  for (uint32_t i = 0; i < bottleneck_count && count < max_recommendations;
       i++) {
    const nsa_bottleneck_t *bottleneck = &bottlenecks[i];
    nsa_optimization_recommendation_t *rec = &recommendations[count];

    switch (bottleneck->type) {
    case NSA_BOTTLENECK_CPU:
      rec->type = NSA_RECOMMENDATION_CPU_AFFINITY;
      snprintf(rec->title, sizeof(rec->title), "CPU Affinity Optimization");
      snprintf(rec->description, sizeof(rec->description),
               "High CPU utilization detected on core %s. Optimize CPU "
               "affinity and thread distribution.",
               bottleneck->location);
      snprintf(rec->implementation, sizeof(rec->implementation),
               "1. Isolate CPU cores from OS scheduler\n"
               "2. Bind RX threads to dedicated cores\n"
               "3. Distribute worker threads across NUMA nodes\n"
               "4. Enable CPU frequency scaling");
      rec->expected_improvement = 15.0;
      rec->implementation_effort = 3;
      rec->risk_level = 2;
      rec->auto_applicable = true;
      snprintf(rec->parameters, sizeof(rec->parameters),
               "cpu_affinity_mask=0x%x", 0xFF);
      count++;
      break;

    case NSA_BOTTLENECK_MEMORY:
      rec->type = NSA_RECOMMENDATION_MEMORY_TUNING;
      snprintf(rec->title, sizeof(rec->title), "Memory Pool Optimization");
      snprintf(rec->description, sizeof(rec->description),
               "High memory utilization detected. Optimize memory pool "
               "configurations and allocation patterns.");
      snprintf(rec->implementation, sizeof(rec->implementation),
               "1. Increase mbuf pool size to 65536\n"
               "2. Increase session cache size to 2048\n"
               "3. Enable huge pages (2MB or 1GB)\n"
               "4. Implement NUMA-aware allocation");
      rec->expected_improvement = 20.0;
      rec->implementation_effort = 4;
      rec->risk_level = 3;
      rec->auto_applicable = true;
      snprintf(rec->parameters, sizeof(rec->parameters),
               "mbuf_pool_size=65536,session_cache_size=2048");
      count++;
      break;

    case NSA_BOTTLENECK_QUEUE:
      rec->type = NSA_RECOMMENDATION_QUEUE_SIZING;
      snprintf(rec->title, sizeof(rec->title), "Queue Size Optimization");
      snprintf(rec->description, sizeof(rec->description),
               "High queue utilization detected on %s. Increase queue sizes "
               "and optimize burst processing.",
               bottleneck->location);
      snprintf(rec->implementation, sizeof(rec->implementation),
               "1. Increase ring buffer size to 65536\n"
               "2. Optimize burst processing sizes\n"
               "3. Implement adaptive polling\n"
               "4. Balance load across multiple queues");
      rec->expected_improvement = 25.0;
      rec->implementation_effort = 2;
      rec->risk_level = 1;
      rec->auto_applicable = true;
      snprintf(rec->parameters, sizeof(rec->parameters),
               "ring_size=65536,burst_size=256");
      count++;
      break;

    case NSA_BOTTLENECK_DPI:
      rec->type = NSA_RECOMMENDATION_DPI_OPTIMIZATION;
      snprintf(rec->title, sizeof(rec->title), "DPI Engine Optimization");
      snprintf(rec->description, sizeof(rec->description),
               "High DPI processing latency detected. Enable early detection "
               "and optimize PML patterns.");
      snprintf(rec->implementation, sizeof(rec->implementation),
               "1. Enable early detection after 5 packets\n"
               "2. Implement PML result caching\n"
               "3. Optimize pattern matching algorithms\n"
               "4. Use application whitelisting for safe apps");
      rec->expected_improvement = 35.0;
      rec->implementation_effort = 6;
      rec->risk_level = 4;
      rec->auto_applicable = false;
      snprintf(rec->parameters, sizeof(rec->parameters),
               "early_detection=true,max_packets=50");
      count++;
      break;

    case NSA_BOTTLENECK_CACHE:
      rec->type = NSA_RECOMMENDATION_CACHE_TUNING;
      snprintf(rec->title, sizeof(rec->title), "Cache Optimization");
      snprintf(rec->description, sizeof(rec->description),
               "Low cache hit rate detected. Optimize data structures for "
               "better cache locality.");
      snprintf(rec->implementation, sizeof(rec->implementation),
               "1. Align data structures to cache lines\n"
               "2. Implement data prefetching\n"
               "3. Optimize memory access patterns\n"
               "4. Increase cache sizes where possible");
      rec->expected_improvement = 18.0;
      rec->implementation_effort = 5;
      rec->risk_level = 3;
      rec->auto_applicable = true;
      snprintf(rec->parameters, sizeof(rec->parameters),
               "prefetch_distance=2,cache_line_align=true");
      count++;
      break;

    default:
      /* Skip unknown bottleneck types */
      break;
    }
  }

  *generated_count = count;
  return 0;
}
