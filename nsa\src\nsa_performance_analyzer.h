/**
 * @file nsa_performance_analyzer.h
 * @brief Enhanced NSA Performance Analysis and Monitoring System
 * 
 * This file contains advanced performance monitoring, analysis, and optimization
 * tools for enterprise-grade network security applications.
 * 
 * Key features:
 * - Real-time performance metrics collection
 * - Automated bottleneck detection and analysis
 * - Performance trend analysis and prediction
 * - Intelligent auto-tuning recommendations
 * - Advanced profiling and hot path analysis
 * - Comprehensive benchmarking suite
 * 
 * <AUTHOR> Optimization Team
 * @date 2025
 */

#ifndef NSA_PERFORMANCE_ANALYZER_H
#define NSA_PERFORMANCE_ANALYZER_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>
#include "nsa.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * PERFORMANCE ANALYSIS CONSTANTS
 * ======================================================================== */

#define NSA_PERF_MAX_METRICS_HISTORY    1000    /**< Maximum metrics history entries */
#define NSA_PERF_MAX_BOTTLENECKS        32      /**< Maximum bottlenecks to track */
#define NSA_PERF_MAX_RECOMMENDATIONS    64      /**< Maximum optimization recommendations */
#define NSA_PERF_ANALYSIS_INTERVAL_MS   1000    /**< Analysis interval in milliseconds */

/* ========================================================================
 * ENHANCED PERFORMANCE METRICS
 * ======================================================================== */

/** @brief Detailed performance metrics with statistical analysis */
typedef struct {
    time_t timestamp;
    uint64_t collection_tsc;
    
    /* Traffic metrics with statistics */
    struct {
        uint64_t current_pps;
        uint64_t peak_pps;
        uint64_t avg_pps;
        uint64_t min_pps;
        double pps_variance;
        double pps_trend;           /**< Trend coefficient */
    } traffic;
    
    /* Latency metrics with percentiles */
    struct {
        uint32_t avg_packet_latency_ns;
        uint32_t min_packet_latency_ns;
        uint32_t max_packet_latency_ns;
        uint32_t p50_latency_ns;
        uint32_t p95_latency_ns;
        uint32_t p99_latency_ns;
        uint32_t p999_latency_ns;
        double latency_jitter;
    } latency;
    
    /* Resource utilization */
    struct {
        uint32_t cpu_utilization[64];
        uint32_t memory_utilization;
        uint32_t cache_hit_rate;
        uint32_t numa_efficiency;   /**< NUMA locality efficiency */
        uint32_t thread_efficiency; /**< Thread utilization efficiency */
    } resources;
    
    /* Session management metrics */
    struct {
        uint32_t active_sessions;
        uint32_t session_setup_rate;
        uint32_t session_teardown_rate;
        uint32_t session_timeout_rate;
        uint32_t session_table_load;
        double session_lifetime_avg;
    } sessions;
    
    /* DPI performance metrics */
    struct {
        uint32_t dpi_processing_time_ns;
        uint32_t apps_detected_per_sec;
        uint32_t threats_detected_per_sec;
        uint32_t policy_evaluations_per_sec;
        uint32_t pml_scan_efficiency;
        uint32_t early_detection_rate;
    } dpi;
    
    /* Queue and buffer metrics */
    struct {
        uint32_t rx_queue_depth[32];
        uint32_t worker_queue_depth[32];
        uint32_t completion_queue_depth[32];
        uint32_t ring_utilization[64];
        uint32_t dropped_packets;
        uint32_t queue_overflow_events;
    } queues;
    
    /* Memory pool metrics */
    struct {
        uint32_t mbuf_pool_utilization;
        uint32_t session_pool_utilization;
        uint32_t work_pool_utilization;
        uint32_t memory_fragmentation;
        uint64_t allocation_failures;
    } memory_pools;
    
} nsa_enhanced_metrics_t;

/* ========================================================================
 * BOTTLENECK DETECTION AND ANALYSIS
 * ======================================================================== */

/** @brief Bottleneck types */
typedef enum {
    NSA_BOTTLENECK_CPU = 0,
    NSA_BOTTLENECK_MEMORY,
    NSA_BOTTLENECK_NETWORK,
    NSA_BOTTLENECK_DPI,
    NSA_BOTTLENECK_SESSION_MGMT,
    NSA_BOTTLENECK_QUEUE,
    NSA_BOTTLENECK_NUMA,
    NSA_BOTTLENECK_CACHE,
    NSA_BOTTLENECK_COUNT
} nsa_bottleneck_type_t;

/** @brief Bottleneck severity levels */
typedef enum {
    NSA_SEVERITY_LOW = 0,
    NSA_SEVERITY_MEDIUM,
    NSA_SEVERITY_HIGH,
    NSA_SEVERITY_CRITICAL
} nsa_bottleneck_severity_t;

/** @brief Bottleneck detection result */
typedef struct {
    nsa_bottleneck_type_t type;
    nsa_bottleneck_severity_t severity;
    char description[256];
    char location[128];          /**< Component/thread/core location */
    double impact_score;         /**< Performance impact score (0-100) */
    uint64_t first_detected;     /**< Timestamp when first detected */
    uint64_t last_seen;          /**< Timestamp when last seen */
    uint32_t occurrence_count;   /**< Number of times detected */
    char recommendations[512];   /**< Optimization recommendations */
} nsa_bottleneck_t;

/* ========================================================================
 * PERFORMANCE TREND ANALYSIS
 * ======================================================================== */

/** @brief Trend analysis result */
typedef struct {
    double slope;                /**< Trend slope */
    double correlation;          /**< Correlation coefficient */
    double prediction_accuracy;  /**< Prediction accuracy percentage */
    uint64_t prediction_horizon; /**< Prediction time horizon (seconds) */
    bool is_degrading;           /**< Performance degradation detected */
    bool is_improving;           /**< Performance improvement detected */
    char trend_description[256]; /**< Human-readable trend description */
} nsa_trend_analysis_t;

/* ========================================================================
 * AUTO-TUNING RECOMMENDATIONS
 * ======================================================================== */

/** @brief Optimization recommendation types */
typedef enum {
    NSA_RECOMMENDATION_CPU_AFFINITY = 0,
    NSA_RECOMMENDATION_MEMORY_TUNING,
    NSA_RECOMMENDATION_QUEUE_SIZING,
    NSA_RECOMMENDATION_DPI_OPTIMIZATION,
    NSA_RECOMMENDATION_SESSION_TUNING,
    NSA_RECOMMENDATION_NUMA_OPTIMIZATION,
    NSA_RECOMMENDATION_CACHE_TUNING,
    NSA_RECOMMENDATION_THREAD_BALANCING,
    NSA_RECOMMENDATION_COUNT
} nsa_recommendation_type_t;

/** @brief Optimization recommendation */
typedef struct {
    nsa_recommendation_type_t type;
    char title[128];
    char description[512];
    char implementation[1024];   /**< How to implement the recommendation */
    double expected_improvement; /**< Expected performance improvement (%) */
    uint32_t implementation_effort; /**< Implementation effort (1-10) */
    uint32_t risk_level;         /**< Risk level (1-10) */
    bool auto_applicable;        /**< Can be applied automatically */
    char parameters[256];        /**< Specific parameters to tune */
} nsa_optimization_recommendation_t;

/* ========================================================================
 * PERFORMANCE ANALYSIS ENGINE
 * ======================================================================== */

/** @brief Performance analysis engine context */
typedef struct {
    bool enabled;
    bool auto_tuning_enabled;
    uint32_t analysis_interval_ms;
    uint32_t history_size;
    
    /* Metrics history */
    nsa_enhanced_metrics_t *metrics_history;
    uint32_t history_head;
    uint32_t history_count;
    
    /* Bottleneck tracking */
    nsa_bottleneck_t bottlenecks[NSA_PERF_MAX_BOTTLENECKS];
    uint32_t bottleneck_count;
    
    /* Recommendations */
    nsa_optimization_recommendation_t recommendations[NSA_PERF_MAX_RECOMMENDATIONS];
    uint32_t recommendation_count;
    
    /* Analysis state */
    uint64_t last_analysis_tsc;
    uint64_t total_analyses;
    uint64_t auto_tuning_actions;
    
} nsa_performance_analyzer_t;

/* ========================================================================
 * CORE ANALYSIS FUNCTIONS
 * ======================================================================== */

/**
 * @brief Initialize the performance analysis engine
 * @return 0 on success, negative on error
 */
int nsa_performance_analyzer_init(void);

/**
 * @brief Cleanup the performance analysis engine
 */
void nsa_performance_analyzer_cleanup(void);

/**
 * @brief Collect enhanced performance metrics
 * @param metrics Output metrics structure
 * @return 0 on success, negative on error
 */
int nsa_collect_enhanced_metrics(nsa_enhanced_metrics_t *metrics);

/**
 * @brief Analyze performance and detect bottlenecks
 * @return 0 on success, negative on error
 */
int nsa_analyze_performance(void);

/**
 * @brief Detect performance bottlenecks
 * @param metrics Current metrics
 * @param bottlenecks Output bottlenecks array
 * @param max_bottlenecks Maximum bottlenecks to detect
 * @param detected_count Output count of detected bottlenecks
 * @return 0 on success, negative on error
 */
int nsa_detect_bottlenecks(const nsa_enhanced_metrics_t *metrics,
                          nsa_bottleneck_t *bottlenecks,
                          uint32_t max_bottlenecks,
                          uint32_t *detected_count);

/**
 * @brief Generate optimization recommendations
 * @param bottlenecks Detected bottlenecks
 * @param bottleneck_count Number of bottlenecks
 * @param recommendations Output recommendations array
 * @param max_recommendations Maximum recommendations to generate
 * @param generated_count Output count of generated recommendations
 * @return 0 on success, negative on error
 */
int nsa_generate_recommendations(const nsa_bottleneck_t *bottlenecks,
                                uint32_t bottleneck_count,
                                nsa_optimization_recommendation_t *recommendations,
                                uint32_t max_recommendations,
                                uint32_t *generated_count);

#ifdef __cplusplus
}
#endif

#endif /* NSA_PERFORMANCE_ANALYZER_H */
