/**
 * @file nsa_pml.c
 * @brief Implementation of the NSA PML Engine module.
 */
#include "nsa_pml.h"
#include <rte_atomic.h>
#include <rte_cycles.h>
#include <rte_spinlock.h>

typedef struct {
    char classify_path[PATH_MAX];
    char rules_path[PATH_MAX];
} nsa_pml_update_paths_t;

rte_atomic64_t g_pml_update_request;

// --- Hot-Reloading Implementation ---

int nsa_pml_hot_reload(const char *classify_path, const char *rules_path) {
    NSA_LOG_DEBUG("PML hot reload requested: classify='%s', rules='%s'",
                  classify_path ? classify_path : "N/A",
                  rules_path ? rules_path : "N/A");

    nsa_pml_update_paths_t *update_paths = calloc(1, sizeof(nsa_pml_update_paths_t));
    if (!update_paths) {
        return -ENOMEM;
    }

    if (classify_path)
        strncpy(update_paths->classify_path, classify_path, PATH_MAX - 1);
    if (rules_path)
        strncpy(update_paths->rules_path, rules_path, PATH_MAX - 1);

    int64_t old_req_ptr =
        rte_atomic64_exchange((volatile uint64_t *) &(g_pml_update_request.cnt), (uint64_t) (uintptr_t) update_paths);
    if (old_req_ptr != 0) {
        NSA_LOG_DEBUG("Overwriting a previous, unhandled PML update request.");
        free((void *) (uintptr_t) old_req_ptr);
    }

    return 0;
}

void nsa_pml_check_for_updates(nsa_rx_thread_pml_context_t *ctx) {
    // 1. Non-blocking check for a new update request.
    int64_t req_ptr_val = rte_atomic64_read(&g_pml_update_request);
    if (req_ptr_val == 0 || ctx->last_update_req_processed == (void *) (uintptr_t) req_ptr_val) {
        return;  // No new request, or this thread has already processed it.
    }

    nsa_pml_update_paths_t *update_paths = (nsa_pml_update_paths_t *) (uintptr_t) req_ptr_val;
    struct pml *new_classify = NULL;
    struct pml *new_rules = NULL;
    bool classify_loaded = false;
    bool rules_loaded = false;

    NSA_LOG_INFO("Lcore %u: Detected signature update request. Starting independent load.", ctx->lcore_id);

    // 2. Each thread independently loads its own new instances.
    if (update_paths->classify_path[0] != '\0') {
        if (pml_load(&new_classify, update_paths->classify_path) == 0) {
            classify_loaded = true;
        } else {
            NSA_LOG_ERROR("Lcore %u: FAILED to load new classify.bin. Will keep using the old version.", ctx->lcore_id);
        }
    }
    if (update_paths->rules_path[0] != '\0') {
        if (pml_load(&new_rules, update_paths->rules_path) == 0) {
            rules_loaded = true;
        } else {
            NSA_LOG_ERROR("Lcore %u: FAILED to load new control.bin. Will keep using the old version.", ctx->lcore_id);
        }
    }

    // 3. Clean up the "grandparent" instances (from two updates ago).
    if (ctx->old_thread_classify_pml) {
        NSA_LOG_DEBUG("Lcore %u: Freeing previous 'old' classify instance.", ctx->lcore_id);
        pml_exit(ctx->old_thread_classify_pml);
        ctx->old_thread_classify_pml = NULL;
    }
    if (ctx->old_thread_rules_pml) {
        NSA_LOG_DEBUG("Lcore %u: Freeing previous 'old' rules instance.", ctx->lcore_id);
        pml_exit(ctx->old_thread_rules_pml);
        ctx->old_thread_rules_pml = NULL;
    }

    // 4. Atomically swap pointers if new instances were loaded successfully.
    if (classify_loaded) {
        ctx->old_thread_classify_pml = ctx->thread_classify_pml;
        ctx->thread_classify_pml = new_classify;
        ctx->classify_available = 1;
        NSA_LOG_INFO("Lcore %u: Switched to new classify instance.", ctx->lcore_id);
    }
    if (rules_loaded) {
        ctx->old_thread_rules_pml = ctx->thread_rules_pml;
        ctx->thread_rules_pml = new_rules;
        ctx->rules_available = 1;
        NSA_LOG_INFO("Lcore %u: Switched to new rules instance.", ctx->lcore_id);
    }

    // 5. Mark this request as processed for this thread.
    ctx->last_update_req_processed = update_paths;
}