/**
 * @file nsa_pml.h
 * @brief Public interface for the NSA PML Engine module.
 *
 * This module encapsulates all interactions with the PML library, including
 * initialization, session context management, packet analysis, and hot-reloading
 * of rule files.
 */

#ifndef NSA_PML_H
#define NSA_PML_H

#include "nsa.h"
#include <rte_atomic.h>

/* ========================================================================
 * Global Variables
 * ======================================================================== */
/**
 * @brief Thread-specific PML instances for each RX thread
 * 
 * This structure stores thread-specific PML instances to avoid
 * thread safety issues when multiple RX threads clone from 
 * the same global PML instances.
 */
typedef struct {
    uint32_t lcore_id;                    /* RX thread lcore ID */
    struct pml *thread_classify_pml;      /* Thread-specific classification PML instance */
    struct pml *thread_rules_pml;         /* Thread-specific rules PML instance */
    int classify_available;               /* Whether classification PML is available */
    int rules_available;                  /* Whether rules PML is available */
    struct rte_mempool *session_ctx_pool;
    uint16_t cached_pml_datetime;
    uint64_t last_time_update_tsc;
    uint64_t time_update_interval_tsc;

    struct pml *old_thread_classify_pml;
    struct pml *old_thread_rules_pml;
    uint64_t cleanup_timer_tsc;
    void* last_update_req_processed;
} nsa_rx_thread_pml_context_t;

// --- Hot-Reloading ---

/**
 * @brief Initiates a hot-reload of the PML rule files.
 * This function is thread-safe and can be called from a management thread.
 * It loads the new files and publishes an update request for all RX threads.
 * @param classify_path Path to the new classify.bin file. Can be NULL to not update.
 * @param rules_path Path to the new control.bin file. Can be NULL to not update.
 * @return 0 on success, negative error code on failure.
 */
int nsa_pml_hot_reload(const char *classify_path, const char *rules_path);

/**
 * @brief Checks for and applies pending PML updates.
 * This function should be called periodically from the slow path of each RX thread.
 * @param nsa_rx_thread_pml_context_t The context of the calling RX thread.
 */
void nsa_pml_check_for_updates(nsa_rx_thread_pml_context_t *pml_thread_ctx);


#endif // NSA_PML_H