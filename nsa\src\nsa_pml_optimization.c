/**
 * @file nsa_pml_optimization.c
 * @brief NSA PML Engine Optimization Implementation
 */

#include "nsa_pml_optimization.h"
#include "nsa.h"
#include "nsa_logging.h"
#include <stdlib.h>
#include <string.h>
#include <rte_cycles.h>
#include <rte_malloc.h>
#include <rte_hash.h>
#include <rte_jhash.h>

/* Global PML optimization context */
static struct {
    bool initialized;
    nsa_pml_optimization_config_t config;
    
    /* Performance metrics */
    nsa_pml_perf_metrics_t metrics;
    
    /* PML context cache */
    struct rte_hash *context_cache;
    nsa_pml_cache_entry_t *cache_entries;
    uint32_t cache_size;
    uint32_t cache_count;
    
    /* Safe application list */
    uint16_t *safe_apps;
    uint32_t safe_app_count;
    uint32_t max_safe_apps;
    
    /* Statistics */
    uint64_t total_scans;
    uint64_t total_evals;
    uint64_t early_detections;
    uint64_t cache_hits;
    uint64_t cache_misses;
    uint64_t rule_eval_bypassed;
    
} g_pml_opt_ctx = {0};

/**
 * @brief Hash function for flow hashes
 */
static uint32_t flow_hash_func(const void *key, uint32_t key_len, uint32_t init_val) {
    return rte_jhash(key, key_len, init_val);
}

/**
 * @brief Initialize PML optimization
 */
int nsa_pml_optimization_init(void) {
    if (g_pml_opt_ctx.initialized) {
        NSA_LOG_WARNING("PML optimization already initialized");
        return 0;
    }
    
    memset(&g_pml_opt_ctx, 0, sizeof(g_pml_opt_ctx));
    
    // Set default configuration
    g_pml_opt_ctx.config = NSA_DEFAULT_PML_OPTIMIZATION;
    g_pml_opt_ctx.cache_size = NSA_PML_CONTEXT_CACHE_SIZE;
    g_pml_opt_ctx.max_safe_apps = 256;
    
    // Allocate cache entries
    g_pml_opt_ctx.cache_entries = rte_zmalloc("pml_cache_entries",
                                             sizeof(nsa_pml_cache_entry_t) * g_pml_opt_ctx.cache_size,
                                             RTE_CACHE_LINE_SIZE);
    if (!g_pml_opt_ctx.cache_entries) {
        NSA_LOG_ERROR("Failed to allocate PML cache entries");
        return -1;
    }
    
    // Create context cache hash table
    struct rte_hash_parameters hash_params = {
        .name = "pml_context_cache",
        .entries = g_pml_opt_ctx.cache_size,
        .key_len = sizeof(uint64_t),
        .hash_func = flow_hash_func,
        .hash_func_init_val = 0x12345678,
        .socket_id = rte_socket_id(),
        .extra_flag = 0
    };
    
    g_pml_opt_ctx.context_cache = rte_hash_create(&hash_params);
    if (!g_pml_opt_ctx.context_cache) {
        NSA_LOG_ERROR("Failed to create PML context cache");
        rte_free(g_pml_opt_ctx.cache_entries);
        return -1;
    }
    
    // Allocate safe applications list
    g_pml_opt_ctx.safe_apps = rte_zmalloc("pml_safe_apps",
                                         sizeof(uint16_t) * g_pml_opt_ctx.max_safe_apps,
                                         RTE_CACHE_LINE_SIZE);
    if (!g_pml_opt_ctx.safe_apps) {
        NSA_LOG_ERROR("Failed to allocate safe applications list");
        rte_hash_free(g_pml_opt_ctx.context_cache);
        rte_free(g_pml_opt_ctx.cache_entries);
        return -1;
    }
    
    // Initialize default safe applications
    g_pml_opt_ctx.safe_apps[0] = 1;    // HTTP
    g_pml_opt_ctx.safe_apps[1] = 2;    // HTTPS
    g_pml_opt_ctx.safe_apps[2] = 53;   // DNS
    g_pml_opt_ctx.safe_apps[3] = 80;   // HTTP (alt)
    g_pml_opt_ctx.safe_apps[4] = 443;  // HTTPS (alt)
    g_pml_opt_ctx.safe_app_count = 5;
    
    g_pml_opt_ctx.initialized = true;
    
    NSA_LOG_INFO("PML optimization initialized successfully");
    return 0;
}

/**
 * @brief Cleanup PML optimization
 */
void nsa_pml_optimization_cleanup(void) {
    if (!g_pml_opt_ctx.initialized) {
        return;
    }
    
    if (g_pml_opt_ctx.context_cache) {
        rte_hash_free(g_pml_opt_ctx.context_cache);
    }
    
    if (g_pml_opt_ctx.cache_entries) {
        rte_free(g_pml_opt_ctx.cache_entries);
    }
    
    if (g_pml_opt_ctx.safe_apps) {
        rte_free(g_pml_opt_ctx.safe_apps);
    }
    
    memset(&g_pml_opt_ctx, 0, sizeof(g_pml_opt_ctx));
    NSA_LOG_INFO("PML optimization cleaned up");
}

/**
 * @brief Check if application is in safe list
 */
bool nsa_is_safe_application(uint16_t app_classification) {
    if (!g_pml_opt_ctx.initialized) {
        return false;
    }
    
    for (uint32_t i = 0; i < g_pml_opt_ctx.safe_app_count; i++) {
        if (g_pml_opt_ctx.safe_apps[i] == app_classification) {
            return true;
        }
    }
    
    return false;
}

/**
 * @brief Add application to safe list
 */
int nsa_add_safe_application(uint16_t app_classification) {
    if (!g_pml_opt_ctx.initialized) {
        NSA_LOG_ERROR("PML optimization not initialized");
        return -1;
    }
    
    if (g_pml_opt_ctx.safe_app_count >= g_pml_opt_ctx.max_safe_apps) {
        NSA_LOG_ERROR("Safe applications list is full");
        return -1;
    }
    
    // Check if already exists
    if (nsa_is_safe_application(app_classification)) {
        NSA_LOG_WARNING("Application %u already in safe list", app_classification);
        return 0;
    }
    
    g_pml_opt_ctx.safe_apps[g_pml_opt_ctx.safe_app_count] = app_classification;
    g_pml_opt_ctx.safe_app_count++;
    
    NSA_LOG_INFO("Added application %u to safe list", app_classification);
    return 0;
}

/**
 * @brief Remove application from safe list
 */
int nsa_remove_safe_application(uint16_t app_classification) {
    if (!g_pml_opt_ctx.initialized) {
        NSA_LOG_ERROR("PML optimization not initialized");
        return -1;
    }
    
    for (uint32_t i = 0; i < g_pml_opt_ctx.safe_app_count; i++) {
        if (g_pml_opt_ctx.safe_apps[i] == app_classification) {
            // Move last element to this position
            g_pml_opt_ctx.safe_apps[i] = g_pml_opt_ctx.safe_apps[g_pml_opt_ctx.safe_app_count - 1];
            g_pml_opt_ctx.safe_app_count--;
            
            NSA_LOG_INFO("Removed application %u from safe list", app_classification);
            return 0;
        }
    }
    
    NSA_LOG_WARNING("Application %u not found in safe list", app_classification);
    return -1;
}

/**
 * @brief Lookup PML results in cache
 */
bool nsa_pml_cache_lookup(uint64_t flow_hash, nsa_pml_cache_entry_t *entry) {
    if (!g_pml_opt_ctx.initialized || !g_pml_opt_ctx.config.enable_context_caching) {
        return false;
    }
    
    int hash_ret = rte_hash_lookup_data(g_pml_opt_ctx.context_cache, &flow_hash, (void**)&entry);
    if (hash_ret >= 0) {
        g_pml_opt_ctx.cache_hits++;
        g_pml_opt_ctx.metrics.cache_hits++;
        return true;
    }
    
    g_pml_opt_ctx.cache_misses++;
    g_pml_opt_ctx.metrics.cache_misses++;
    return false;
}

/**
 * @brief Store PML results in cache
 */
int nsa_pml_cache_store(uint64_t flow_hash, const nsa_pml_cache_entry_t *entry) {
    if (!g_pml_opt_ctx.initialized || !g_pml_opt_ctx.config.enable_context_caching) {
        return -1;
    }
    
    if (g_pml_opt_ctx.cache_count >= g_pml_opt_ctx.cache_size) {
        // Cache is full, implement LRU eviction (simplified)
        // In a real implementation, you'd want a proper LRU mechanism
        g_pml_opt_ctx.cache_count = g_pml_opt_ctx.cache_size / 2; // Evict half
        rte_hash_reset(g_pml_opt_ctx.context_cache);
    }
    
    nsa_pml_cache_entry_t *cache_entry = &g_pml_opt_ctx.cache_entries[g_pml_opt_ctx.cache_count];
    memcpy(cache_entry, entry, sizeof(nsa_pml_cache_entry_t));
    cache_entry->timestamp = rte_rdtsc();
    
    int hash_ret = rte_hash_add_key_data(g_pml_opt_ctx.context_cache, &flow_hash, cache_entry);
    if (hash_ret >= 0) {
        g_pml_opt_ctx.cache_count++;
        return 0;
    }
    
    return -1;
}

/**
 * @brief Optimized PML scan with early detection
 */
int nsa_pml_scan_optimized(nsa_session_context_t *nsa_ctx,
                          const uint8_t *data,
                          uint32_t len,
                          enum pml_match_lang match_mode) {
    if (!g_pml_opt_ctx.initialized || !nsa_ctx) {
        return -1;
    }
    
    g_pml_opt_ctx.total_scans++;
    g_pml_opt_ctx.metrics.total_scans++;
    
    // Early detection check
    if (g_pml_opt_ctx.config.enable_early_detection &&
        nsa_should_continue_analysis(nsa_ctx) == false) {
        g_pml_opt_ctx.early_detections++;
        g_pml_opt_ctx.metrics.early_detections++;
        return 0; // Early detection - analysis complete
    }
    
    // Check cache first
    uint64_t flow_hash = rte_jhash(data, len > 64 ? 64 : len, 0x12345678);
    nsa_pml_cache_entry_t cache_entry;
    
    if (nsa_pml_cache_lookup(flow_hash, &cache_entry)) {
        // Cache hit - use cached results
        nsa_ctx->app_classification = cache_entry.app_classification;
        nsa_ctx->threat_level = cache_entry.threat_level;
        nsa_ctx->analysis_complete = cache_entry.analysis_complete;
        return 0;
    }
    
    // Prefetch PML data if enabled
    if (g_pml_opt_ctx.config.enable_prefetching) {
        nsa_pml_prefetch_data(nsa_ctx);
    }
    
    // Perform actual PML scan
    uint64_t scan_start = rte_rdtsc();
    
    int scan_result = pml_scan(nsa_ctx->pml_classify_instance, data, len, match_mode, &nsa_ctx->pml_context);
    
    uint64_t scan_end = rte_rdtsc();
    uint64_t scan_cycles = scan_end - scan_start;
    
    // Update metrics
    g_pml_opt_ctx.metrics.avg_scan_cycles = 
        (g_pml_opt_ctx.metrics.avg_scan_cycles * (g_pml_opt_ctx.total_scans - 1) + scan_cycles) / 
        g_pml_opt_ctx.total_scans;
    
    // Store results in cache if successful
    if (scan_result >= 0 && nsa_ctx->analysis_complete) {
        cache_entry.flow_hash = flow_hash;
        cache_entry.app_classification = nsa_ctx->app_classification;
        cache_entry.threat_level = nsa_ctx->threat_level;
        cache_entry.analysis_complete = nsa_ctx->analysis_complete;
        
        nsa_pml_cache_store(flow_hash, &cache_entry);
    }
    
    return scan_result;
}

/**
 * @brief Optimized PML evaluation with bypass logic
 */
int nsa_pml_eval_optimized(nsa_session_context_t *nsa_ctx, uint32_t session_id) {
    if (!g_pml_opt_ctx.initialized || !nsa_ctx) {
        return -1;
    }
    
    g_pml_opt_ctx.total_evals++;
    g_pml_opt_ctx.metrics.total_evals++;
    
    // Check if we can bypass rule evaluation for safe applications
    if (g_pml_opt_ctx.config.enable_safe_app_bypass &&
        nsa_ctx->app_classification != 0 &&
        nsa_is_safe_application(nsa_ctx->app_classification)) {
        
        g_pml_opt_ctx.rule_eval_bypassed++;
        g_pml_opt_ctx.metrics.rule_eval_bypassed++;
        
        NSA_LOG_DEBUG("Session %u: Bypassing rule evaluation for safe application %u",
                     session_id, nsa_ctx->app_classification);
        
        nsa_ctx->pml_context.verdict = DPI_VERDICT_ACCEPT;
        return 0;
    }
    
    // Prefetch rule evaluation data
    if (g_pml_opt_ctx.config.enable_prefetching && nsa_ctx->pml_rules_instance) {
        rte_prefetch0(nsa_ctx->pml_rules_instance);
    }
    
    // Perform actual rule evaluation
    uint64_t eval_start = rte_rdtsc();
    
    int eval_result = pml_eval(nsa_ctx->pml_rules_instance,
                              FILTER_TABLE_STATE,
                              (void *)&nsa_ctx->pml_context,
                              sizeof(struct nsa_pml_context),
                              0,
                              &nsa_ctx->pml_context);
    
    uint64_t eval_end = rte_rdtsc();
    uint64_t eval_cycles = eval_end - eval_start;
    
    // Update metrics
    g_pml_opt_ctx.metrics.avg_eval_cycles = 
        (g_pml_opt_ctx.metrics.avg_eval_cycles * (g_pml_opt_ctx.total_evals - 1) + eval_cycles) / 
        g_pml_opt_ctx.total_evals;
    
    return eval_result;
}

/**
 * @brief Get PML performance metrics
 */
int nsa_get_pml_performance_metrics(nsa_pml_perf_metrics_t *metrics) {
    if (!g_pml_opt_ctx.initialized || !metrics) {
        return -1;
    }
    
    // Update current metrics
    g_pml_opt_ctx.metrics.total_scans = g_pml_opt_ctx.total_scans;
    g_pml_opt_ctx.metrics.total_evals = g_pml_opt_ctx.total_evals;
    g_pml_opt_ctx.metrics.early_detections = g_pml_opt_ctx.early_detections;
    g_pml_opt_ctx.metrics.rule_eval_bypassed = g_pml_opt_ctx.rule_eval_bypassed;
    g_pml_opt_ctx.metrics.cache_hits = g_pml_opt_ctx.cache_hits;
    g_pml_opt_ctx.metrics.cache_misses = g_pml_opt_ctx.cache_misses;
    g_pml_opt_ctx.metrics.active_contexts = g_pml_opt_ctx.cache_count;
    g_pml_opt_ctx.metrics.context_pool_utilization = 
        (g_pml_opt_ctx.cache_count * 100) / g_pml_opt_ctx.cache_size;
    
    memcpy(metrics, &g_pml_opt_ctx.metrics, sizeof(nsa_pml_perf_metrics_t));
    return 0;
}

/**
 * @brief Reset PML performance counters
 */
int nsa_reset_pml_performance_counters(void) {
    if (!g_pml_opt_ctx.initialized) {
        return -1;
    }
    
    memset(&g_pml_opt_ctx.metrics, 0, sizeof(nsa_pml_perf_metrics_t));
    g_pml_opt_ctx.total_scans = 0;
    g_pml_opt_ctx.total_evals = 0;
    g_pml_opt_ctx.early_detections = 0;
    g_pml_opt_ctx.cache_hits = 0;
    g_pml_opt_ctx.cache_misses = 0;
    g_pml_opt_ctx.rule_eval_bypassed = 0;
    
    NSA_LOG_INFO("PML performance counters reset");
    return 0;
}

/**
 * @brief Auto-tune PML parameters based on performance
 */
int nsa_auto_tune_pml_parameters(void) {
    if (!g_pml_opt_ctx.initialized) {
        return -1;
    }
    
    // Calculate cache hit rate
    uint64_t total_cache_accesses = g_pml_opt_ctx.cache_hits + g_pml_opt_ctx.cache_misses;
    double cache_hit_rate = 0.0;
    
    if (total_cache_accesses > 0) {
        cache_hit_rate = (double)g_pml_opt_ctx.cache_hits / total_cache_accesses * 100.0;
    }
    
    // Auto-tune based on metrics
    if (cache_hit_rate < 80.0 && g_pml_opt_ctx.cache_size < 1024) {
        // Low cache hit rate - consider increasing cache size
        NSA_LOG_INFO("Auto-tune: Low cache hit rate (%.1f%%), consider increasing cache size", 
                     cache_hit_rate);
    }
    
    if (g_pml_opt_ctx.early_detections > 0) {
        double early_detection_rate = (double)g_pml_opt_ctx.early_detections / 
                                     g_pml_opt_ctx.total_scans * 100.0;
        NSA_LOG_INFO("Auto-tune: Early detection rate: %.1f%%", early_detection_rate);
        
        if (early_detection_rate < 20.0) {
            // Low early detection rate - consider adjusting thresholds
            NSA_LOG_INFO("Auto-tune: Consider lowering early detection thresholds");
        }
    }
    
    if (g_pml_opt_ctx.rule_eval_bypassed > 0) {
        double bypass_rate = (double)g_pml_opt_ctx.rule_eval_bypassed / 
                            g_pml_opt_ctx.total_evals * 100.0;
        NSA_LOG_INFO("Auto-tune: Rule evaluation bypass rate: %.1f%%", bypass_rate);
    }
    
    return 0;
}
