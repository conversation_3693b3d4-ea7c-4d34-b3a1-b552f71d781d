/**
 * @file nsa_pml_optimization.h
 * @brief NSA PML Engine Performance Optimizations
 * 
 * This file contains optimized PML engine functions and configurations
 * for high-performance deep packet inspection in enterprise network security applications.
 * 
 * Key optimizations:
 * - Early detection and classification termination
 * - Application-based rule evaluation bypass
 * - PML context prefetching and caching
 * - Packet and byte limits for analysis
 * - Safe application whitelisting
 * 
 * <AUTHOR> Optimization Team
 * @date 2025
 */

#ifndef NSA_PML_OPTIMIZATION_H
#define NSA_PML_OPTIMIZATION_H

#include <stdint.h>
#include <stdbool.h>
#include <rte_prefetch.h>
#include "nsa.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * PML OPTIMIZATION CONSTANTS
 * ======================================================================== */

/** @brief Maximum packets to analyze per flow for performance */
#define NSA_MAX_PACKETS_PER_FLOW        50

/** @brief Maximum bytes to analyze per flow (64KB) */
#define NSA_MAX_BYTES_PER_FLOW          65536

/** @brief Minimum packets before early detection kicks in */
#define NSA_MIN_PACKETS_FOR_EARLY_DETECTION  5

/** @brief Confidence threshold for early classification */
#define NSA_EARLY_DETECTION_CONFIDENCE  85

/** @brief PML context cache size per thread */
#define NSA_PML_CONTEXT_CACHE_SIZE      256

/* ========================================================================
 * PML PERFORMANCE METRICS
 * ======================================================================== */

/** @brief PML performance metrics */
typedef struct {
    uint64_t total_scans;               /**< Total PML scans performed */
    uint64_t total_evals;               /**< Total PML evaluations performed */
    uint64_t early_detections;          /**< Early detections triggered */
    uint64_t rule_eval_bypassed;        /**< Rule evaluations bypassed */
    uint64_t cache_hits;                /**< PML context cache hits */
    uint64_t cache_misses;              /**< PML context cache misses */
    uint64_t avg_scan_cycles;           /**< Average scan time in cycles */
    uint64_t avg_eval_cycles;           /**< Average eval time in cycles */
    uint32_t active_contexts;           /**< Active PML contexts */
    uint32_t context_pool_utilization;  /**< Context pool utilization (%) */
} nsa_pml_perf_metrics_t;

/* ========================================================================
 * PML OPTIMIZATION CONFIGURATION
 * ======================================================================== */

/** @brief PML optimization configuration */
typedef struct {
    bool enable_early_detection;        /**< Enable early detection optimization */
    bool enable_safe_app_bypass;        /**< Enable safe application bypass */
    bool enable_context_caching;        /**< Enable PML context caching */
    bool enable_prefetching;             /**< Enable data prefetching */
    uint32_t max_packets_per_flow;       /**< Maximum packets to analyze */
    uint32_t max_bytes_per_flow;         /**< Maximum bytes to analyze */
    uint32_t early_detection_threshold;  /**< Confidence threshold for early detection */
    uint32_t context_cache_size;         /**< PML context cache size */
} nsa_pml_optimization_config_t;

/* ========================================================================
 * OPTIMIZED PML FUNCTIONS
 * ======================================================================== */

/**
 * @brief Optimized PML scan with early detection
 * 
 * Performs PML scanning with early detection optimization to reduce
 * unnecessary processing for already classified flows.
 * 
 * @param nsa_ctx NSA session context
 * @param data Packet data to scan
 * @param len Data length
 * @param match_mode PML match mode
 * @return Scan result or early detection verdict
 */
int nsa_pml_scan_optimized(nsa_session_context_t *nsa_ctx,
                          const uint8_t *data,
                          uint32_t len,
                          enum pml_match_lang match_mode);

/**
 * @brief Optimized PML evaluation with bypass logic
 * 
 * Performs PML rule evaluation with smart bypass for safe applications
 * and cached results.
 * 
 * @param nsa_ctx NSA session context
 * @param session_id Session ID for logging
 * @return Evaluation result
 */
int nsa_pml_eval_optimized(nsa_session_context_t *nsa_ctx, uint32_t session_id);

/**
 * @brief Check if flow should continue analysis
 * 
 * Determines if a flow should continue PML analysis based on
 * packet count, byte count, and current classification state.
 * 
 * @param nsa_ctx NSA session context
 * @return true if analysis should continue, false otherwise
 */
static inline bool nsa_should_continue_analysis(const nsa_session_context_t *nsa_ctx) {
    /* Early detection - skip if already classified with high confidence */
    if (nsa_ctx->analysis_complete && 
        nsa_ctx->app_classification != 0 && 
        nsa_ctx->threat_level == 0) {
        return false;
    }
    
    /* Limit analysis to prevent performance degradation */
    if (nsa_ctx->packets_analyzed > NSA_MAX_PACKETS_PER_FLOW ||
        nsa_ctx->bytes_analyzed > NSA_MAX_BYTES_PER_FLOW) {
        return false;
    }
    
    return true;
}

/**
 * @brief Prefetch PML data structures
 * 
 * Prefetches PML-related data structures to improve cache performance.
 * 
 * @param nsa_ctx NSA session context
 */
static inline void nsa_pml_prefetch_data(const nsa_session_context_t *nsa_ctx) {
    if (nsa_ctx->pml_classify_instance) {
        rte_prefetch0(nsa_ctx->pml_classify_instance);
        rte_prefetch0(&nsa_ctx->pml_context);
    }
    
    if (nsa_ctx->pml_rules_instance) {
        rte_prefetch0(nsa_ctx->pml_rules_instance);
    }
}

/* ========================================================================
 * PML CONTEXT CACHING
 * ======================================================================== */

/**
 * @brief PML context cache entry */
typedef struct {
    uint64_t flow_hash;                 /**< Flow hash for cache lookup */
    uint16_t app_classification;        /**< Cached application classification */
    uint8_t threat_level;               /**< Cached threat level */
    uint8_t analysis_complete;          /**< Analysis completion flag */
    uint64_t timestamp;                 /**< Cache entry timestamp */
} nsa_pml_cache_entry_t;

/**
 * @brief Lookup PML results in cache
 * 
 * @param flow_hash Flow hash for lookup
 * @param entry Output cache entry
 * @return true if found in cache, false otherwise
 */
bool nsa_pml_cache_lookup(uint64_t flow_hash, nsa_pml_cache_entry_t *entry);

/**
 * @brief Store PML results in cache
 * 
 * @param flow_hash Flow hash for storage
 * @param entry Cache entry to store
 * @return 0 on success, negative on error
 */
int nsa_pml_cache_store(uint64_t flow_hash, const nsa_pml_cache_entry_t *entry);

/* ========================================================================
 * SAFE APPLICATION MANAGEMENT
 * ======================================================================== */

/**
 * @brief Check if application is in safe list
 * 
 * @param app_classification Application classification ID
 * @return true if application is safe, false otherwise
 */
bool nsa_is_safe_application(uint16_t app_classification);

/**
 * @brief Add application to safe list
 * 
 * @param app_classification Application classification ID
 * @return 0 on success, negative on error
 */
int nsa_add_safe_application(uint16_t app_classification);

/**
 * @brief Remove application from safe list
 * 
 * @param app_classification Application classification ID
 * @return 0 on success, negative on error
 */
int nsa_remove_safe_application(uint16_t app_classification);

/* ========================================================================
 * PML PERFORMANCE MONITORING
 * ======================================================================== */

/**
 * @brief Get PML performance metrics
 * 
 * @param metrics Output metrics structure
 * @return 0 on success, negative on error
 */
int nsa_get_pml_performance_metrics(nsa_pml_perf_metrics_t *metrics);

/**
 * @brief Reset PML performance counters
 * 
 * @return 0 on success, negative on error
 */
int nsa_reset_pml_performance_counters(void);

/**
 * @brief Auto-tune PML parameters based on performance
 * 
 * Automatically adjusts PML optimization parameters based on
 * current performance metrics and workload characteristics.
 * 
 * @return 0 on success, negative on error
 */
int nsa_auto_tune_pml_parameters(void);

/* ========================================================================
 * DEFAULT OPTIMIZED CONFIGURATIONS
 * ======================================================================== */

/** @brief Default PML optimization configuration */
static const nsa_pml_optimization_config_t NSA_DEFAULT_PML_OPTIMIZATION = {
    .enable_early_detection = true,
    .enable_safe_app_bypass = true,
    .enable_context_caching = true,
    .enable_prefetching = true,
    .max_packets_per_flow = NSA_MAX_PACKETS_PER_FLOW,
    .max_bytes_per_flow = NSA_MAX_BYTES_PER_FLOW,
    .early_detection_threshold = NSA_EARLY_DETECTION_CONFIDENCE,
    .context_cache_size = NSA_PML_CONTEXT_CACHE_SIZE
};

#ifdef __cplusplus
}
#endif

#endif /* NSA_PML_OPTIMIZATION_H */
