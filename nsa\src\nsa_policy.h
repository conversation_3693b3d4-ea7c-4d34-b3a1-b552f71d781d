#ifndef _NSA_POLICY_H_
#define _NSA_POLICY_H_

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// Policy action types
typedef enum {
    NSA_POLICY_ACTION_ALLOW = 1,
    NSA_POLICY_ACTION_DENY = 2,
    NSA_POLICY_ACTION_DROP = 3,
    NSA_POLICY_ACTION_REDIRECT = 4,
    NSA_POLICY_ACTION_QOS_LIMIT = 5,
    NSA_POLICY_ACTION_LOG_ONLY = 6,
    NSA_POLICY_ACTION_DECRYPT = 7,
    NSA_POLICY_ACTION_SANDBOX = 8
} nsa_policy_action_t;

// Policy rule priority levels
typedef enum {
    NSA_POLICY_PRIORITY_EMERGENCY = 1,
    NSA_POLICY_PRIORITY_HIGH = 2,
    NSA_POLICY_PRIORITY_MEDIUM = 3,
    NSA_POLICY_PRIORITY_LOW = 4,
    NSA_POLICY_PRIORITY_DEFAULT = 5
} nsa_policy_priority_t;

// Zone definition
typedef struct {
    uint16_t zone_id;
    char zone_name[64];
    uint8_t security_level;    // 1-100, higher is more secure
    uint32_t default_policy_action;
    bool enable_threat_protection;
    bool enable_url_filtering;
    bool enable_app_control;
} nsa_zone_t;

// User/Group information
typedef struct {
    uint32_t user_id;
    uint32_t group_id;
    char username[64];
    char group_name[64];
    uint8_t privilege_level;
    uint32_t quota_bytes_per_day;
    uint16_t allowed_apps[32];  // Array of allowed app IDs
    uint16_t blocked_apps[32];  // Array of blocked app IDs
} nsa_user_info_t;

// URL category and reputation
typedef struct {
    uint32_t url_hash;
    uint16_t category_id;
    uint8_t reputation_score;  // 1-100, higher is better
    char category_name[64];
    bool is_malicious;
    uint32_t last_updated;
} nsa_url_info_t;

// QoS parameters
typedef struct {
    uint32_t bandwidth_limit_kbps;
    uint32_t burst_size_kb;
    uint8_t dscp_marking;
    uint8_t priority_queue;
    bool enable_rate_limiting;
} nsa_qos_params_t;

// Policy rule definition
typedef struct {
    uint32_t rule_id;
    nsa_policy_priority_t priority;
    bool enabled;
    
    // Source/Destination criteria
    uint16_t src_zone_id;
    uint16_t dst_zone_id;
    uint32_t src_user_id;
    uint32_t dst_user_id;
    
    // Network criteria
    uint32_t src_network;
    uint32_t src_netmask;
    uint32_t dst_network;
    uint32_t dst_netmask;
    uint16_t src_port_start;
    uint16_t src_port_end;
    uint16_t dst_port_start;
    uint16_t dst_port_end;
    uint8_t protocol;
    
    // Application criteria
    uint16_t app_id;
    uint16_t app_category;
    uint16_t url_category;
    
    // Threat criteria
    uint8_t min_threat_severity;
    uint16_t threat_category_mask;
    
    // Time-based criteria
    uint32_t time_start;  // Time of day in seconds since midnight
    uint32_t time_end;
    uint8_t days_of_week; // Bitmask: bit 0=Sunday, bit 1=Monday, etc.
    
    // Actions
    nsa_policy_action_t action;
    nsa_qos_params_t qos_params;
    uint32_t redirect_ip;
    uint16_t redirect_port;
    
    // Logging
    bool log_session_start;
    bool log_session_end;
    bool log_threat_detected;
    bool log_policy_violation;
    
    // Statistics
    uint64_t hit_count;
    uint64_t bytes_processed;
    uint32_t last_hit_time;
    
    char rule_name[128];
    char description[256];
} nsa_policy_rule_t;

// Policy evaluation context
typedef struct {
    uint32_t session_id;
    
    // Network context
    uint32_t src_ip;
    uint32_t dst_ip;
    uint16_t src_port;
    uint16_t dst_port;
    uint8_t protocol;
    uint16_t src_zone_id;
    uint16_t dst_zone_id;
    
    // User context
    nsa_user_info_t src_user;
    nsa_user_info_t dst_user;
    
    // Application context
    uint16_t app_id;
    uint16_t app_category;
    uint8_t app_risk;
    
    // URL context
    nsa_url_info_t url_info;
    
    // Threat context
    uint16_t threat_id;
    uint8_t threat_severity;
    uint16_t threat_category;
    
    // Session context
    uint64_t session_bytes;
    uint32_t session_duration;
    uint32_t current_time;
    uint8_t current_day_of_week;
    
    // Results
    nsa_policy_rule_t *matched_rule;
    nsa_policy_action_t final_action;
    nsa_qos_params_t applied_qos;
    bool should_log;
} nsa_policy_context_t;

// Policy engine interface
int nsa_policy_init(void);
void nsa_policy_cleanup(void);

// Rule management
int nsa_policy_add_rule(const nsa_policy_rule_t *rule);
int nsa_policy_remove_rule(uint32_t rule_id);
int nsa_policy_update_rule(uint32_t rule_id, const nsa_policy_rule_t *updated_rule);
int nsa_policy_get_rule(uint32_t rule_id, nsa_policy_rule_t *rule);
int nsa_policy_list_rules(nsa_policy_rule_t *rules, uint32_t max_rules, uint32_t *count);

// Zone management
int nsa_policy_add_zone(const nsa_zone_t *zone);
int nsa_policy_remove_zone(uint16_t zone_id);
int nsa_policy_update_zone(uint16_t zone_id, const nsa_zone_t *updated_zone);
int nsa_policy_get_zone(uint16_t zone_id, nsa_zone_t *zone);

// User management
int nsa_policy_add_user(const nsa_user_info_t *user);
int nsa_policy_remove_user(uint32_t user_id);
int nsa_policy_update_user(uint32_t user_id, const nsa_user_info_t *updated_user);
int nsa_policy_get_user(uint32_t user_id, nsa_user_info_t *user);
int nsa_policy_lookup_user_by_ip(uint32_t ip_addr, nsa_user_info_t *user);

// URL filtering
int nsa_policy_lookup_url_category(const char *hostname, nsa_url_info_t *url_info);
int nsa_policy_update_url_database(const char *database_file);

// Policy evaluation
int nsa_policy_evaluate_session(nsa_policy_context_t *ctx);
nsa_policy_action_t nsa_policy_get_final_verdict(const nsa_policy_context_t *ctx);

// Policy statistics
int nsa_policy_get_rule_stats(uint32_t rule_id, uint64_t *hit_count, uint64_t *bytes);
int nsa_policy_reset_rule_stats(uint32_t rule_id);
int nsa_policy_get_global_stats(uint64_t *total_sessions, uint64_t *blocked_sessions);

#ifdef __cplusplus
}
#endif

#endif // _NSA_POLICY_H_ 