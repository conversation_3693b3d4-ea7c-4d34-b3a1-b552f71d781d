/**
 * @file nsa_profiler.h
 * @brief NSA Performance Profiler and Hot Path Analysis
 * 
 * This file contains advanced profiling tools for identifying performance
 * hot paths, function call overhead, and optimization opportunities.
 * 
 * Key features:
 * - Function-level performance profiling
 * - Hot path identification and analysis
 * - Call graph generation and analysis
 * - Cache miss analysis
 * - Branch prediction analysis
 * - Memory access pattern analysis
 * - Automated optimization suggestions
 * 
 * <AUTHOR> Optimization Team
 * @date 2025
 */

#ifndef NSA_PROFILER_H
#define NSA_PROFILER_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * PROFILING CONFIGURATION
 * ======================================================================== */

#define NSA_PROFILER_MAX_FUNCTIONS      1024    /**< Maximum functions to profile */
#define NSA_PROFILER_MAX_CALL_STACK     64      /**< Maximum call stack depth */
#define NSA_PROFILER_MAX_HOT_PATHS      32      /**< Maximum hot paths to track */
#define NSA_PROFILER_SAMPLE_RATE        1000    /**< Default sampling rate (Hz) */

/** @brief Profiling modes */
typedef enum {
    NSA_PROFILE_DISABLED = 0,           /**< Profiling disabled */
    NSA_PROFILE_SAMPLING,               /**< Statistical sampling */
    NSA_PROFILE_INSTRUMENTATION,       /**< Function instrumentation */
    NSA_PROFILE_HYBRID,                 /**< Hybrid sampling + instrumentation */
    NSA_PROFILE_COUNT
} nsa_profile_mode_t;

/** @brief Profiling events */
typedef enum {
    NSA_EVENT_FUNCTION_ENTRY = 0,       /**< Function entry */
    NSA_EVENT_FUNCTION_EXIT,            /**< Function exit */
    NSA_EVENT_CACHE_MISS,               /**< Cache miss */
    NSA_EVENT_BRANCH_MISS,              /**< Branch misprediction */
    NSA_EVENT_MEMORY_ACCESS,            /**< Memory access */
    NSA_EVENT_CONTEXT_SWITCH,           /**< Context switch */
    NSA_EVENT_COUNT
} nsa_profile_event_t;

/* ========================================================================
 * PROFILING DATA STRUCTURES
 * ======================================================================== */

/** @brief Function profile information */
typedef struct {
    char name[128];                     /**< Function name */
    void *address;                      /**< Function address */
    uint64_t call_count;                /**< Number of calls */
    uint64_t total_cycles;              /**< Total cycles spent */
    uint64_t min_cycles;                /**< Minimum cycles per call */
    uint64_t max_cycles;                /**< Maximum cycles per call */
    uint64_t avg_cycles;                /**< Average cycles per call */
    uint64_t self_cycles;               /**< Cycles excluding children */
    uint64_t cache_misses;              /**< Cache misses in function */
    uint64_t branch_misses;             /**< Branch mispredictions */
    double cpu_percentage;              /**< CPU time percentage */
    bool is_hot_path;                   /**< Is this a hot path function */
} nsa_function_profile_t;

/** @brief Call stack frame */
typedef struct {
    void *function_address;             /**< Function address */
    char function_name[64];             /**< Function name */
    uint64_t entry_tsc;                 /**< Entry timestamp */
    uint32_t call_depth;                /**< Call depth */
} nsa_call_frame_t;

/** @brief Hot path information */
typedef struct {
    char path_description[256];         /**< Path description */
    nsa_call_frame_t call_stack[NSA_PROFILER_MAX_CALL_STACK];
    uint32_t stack_depth;               /**< Current stack depth */
    uint64_t total_cycles;              /**< Total cycles for this path */
    uint64_t hit_count;                 /**< Number of times this path was taken */
    double percentage;                  /**< Percentage of total execution time */
    uint32_t optimization_priority;     /**< Optimization priority (1-10) */
    char optimization_suggestions[512]; /**< Optimization suggestions */
} nsa_hot_path_t;

/** @brief Cache analysis data */
typedef struct {
    uint64_t l1_cache_hits;             /**< L1 cache hits */
    uint64_t l1_cache_misses;           /**< L1 cache misses */
    uint64_t l2_cache_hits;             /**< L2 cache hits */
    uint64_t l2_cache_misses;           /**< L2 cache misses */
    uint64_t l3_cache_hits;             /**< L3 cache hits */
    uint64_t l3_cache_misses;           /**< L3 cache misses */
    uint64_t tlb_hits;                  /**< TLB hits */
    uint64_t tlb_misses;                /**< TLB misses */
    double l1_hit_rate;                 /**< L1 cache hit rate */
    double l2_hit_rate;                 /**< L2 cache hit rate */
    double l3_hit_rate;                 /**< L3 cache hit rate */
    double tlb_hit_rate;                /**< TLB hit rate */
} nsa_cache_analysis_t;

/** @brief Branch prediction analysis */
typedef struct {
    uint64_t total_branches;            /**< Total branches executed */
    uint64_t branch_hits;               /**< Correctly predicted branches */
    uint64_t branch_misses;             /**< Mispredicted branches */
    double prediction_accuracy;         /**< Branch prediction accuracy */
    uint64_t indirect_branches;         /**< Indirect branches */
    uint64_t indirect_misses;           /**< Indirect branch misses */
    double indirect_accuracy;           /**< Indirect branch accuracy */
} nsa_branch_analysis_t;

/** @brief Memory access pattern analysis */
typedef struct {
    uint64_t sequential_accesses;       /**< Sequential memory accesses */
    uint64_t random_accesses;           /**< Random memory accesses */
    uint64_t stride_accesses;           /**< Stride memory accesses */
    double locality_score;              /**< Memory locality score (0-100) */
    uint64_t numa_local_accesses;       /**< NUMA local accesses */
    uint64_t numa_remote_accesses;      /**< NUMA remote accesses */
    double numa_efficiency;             /**< NUMA efficiency (%) */
    uint32_t avg_access_size;           /**< Average access size */
    uint32_t prefetch_effectiveness;    /**< Prefetch effectiveness (%) */
} nsa_memory_analysis_t;

/** @brief Comprehensive profiling results */
typedef struct {
    time_t start_time;                  /**< Profiling start time */
    time_t end_time;                    /**< Profiling end time */
    uint64_t total_samples;             /**< Total samples collected */
    uint64_t total_cycles;              /**< Total cycles profiled */
    
    /* Function profiles */
    nsa_function_profile_t functions[NSA_PROFILER_MAX_FUNCTIONS];
    uint32_t function_count;            /**< Number of profiled functions */
    
    /* Hot paths */
    nsa_hot_path_t hot_paths[NSA_PROFILER_MAX_HOT_PATHS];
    uint32_t hot_path_count;            /**< Number of hot paths */
    
    /* Hardware analysis */
    nsa_cache_analysis_t cache_analysis;
    nsa_branch_analysis_t branch_analysis;
    nsa_memory_analysis_t memory_analysis;
    
    /* Overall metrics */
    double cpu_efficiency;              /**< CPU efficiency score */
    double memory_efficiency;           /**< Memory efficiency score */
    double cache_efficiency;            /**< Cache efficiency score */
    double overall_efficiency;          /**< Overall efficiency score */
    
} nsa_profiling_results_t;

/* ========================================================================
 * PROFILER CONTROL FUNCTIONS
 * ======================================================================== */

/**
 * @brief Initialize the profiler
 * @param mode Profiling mode
 * @param sample_rate Sampling rate in Hz
 * @return 0 on success, negative on error
 */
int nsa_profiler_init(nsa_profile_mode_t mode, uint32_t sample_rate);

/**
 * @brief Start profiling
 * @return 0 on success, negative on error
 */
int nsa_profiler_start(void);

/**
 * @brief Stop profiling
 * @return 0 on success, negative on error
 */
int nsa_profiler_stop(void);

/**
 * @brief Reset profiling data
 * @return 0 on success, negative on error
 */
int nsa_profiler_reset(void);

/**
 * @brief Cleanup profiler resources
 */
void nsa_profiler_cleanup(void);

/**
 * @brief Get profiling results
 * @param results Output results structure
 * @return 0 on success, negative on error
 */
int nsa_profiler_get_results(nsa_profiling_results_t *results);

/* ========================================================================
 * INSTRUMENTATION MACROS
 * ======================================================================== */

#ifdef NSA_ENABLE_PROFILING

/**
 * @brief Function entry instrumentation
 */
#define NSA_PROFILE_FUNCTION_ENTER(func_name) \
    nsa_profiler_function_enter(__func__, __builtin_return_address(0))

/**
 * @brief Function exit instrumentation
 */
#define NSA_PROFILE_FUNCTION_EXIT(func_name) \
    nsa_profiler_function_exit(__func__)

/**
 * @brief Mark a code section for profiling
 */
#define NSA_PROFILE_SECTION_BEGIN(section_name) \
    nsa_profiler_section_begin(section_name)

/**
 * @brief End a profiled code section
 */
#define NSA_PROFILE_SECTION_END(section_name) \
    nsa_profiler_section_end(section_name)

#else

#define NSA_PROFILE_FUNCTION_ENTER(func_name)
#define NSA_PROFILE_FUNCTION_EXIT(func_name)
#define NSA_PROFILE_SECTION_BEGIN(section_name)
#define NSA_PROFILE_SECTION_END(section_name)

#endif /* NSA_ENABLE_PROFILING */

/* ========================================================================
 * ANALYSIS FUNCTIONS
 * ======================================================================== */

/**
 * @brief Identify hot paths in the profiling data
 * @param results Profiling results
 * @param threshold_percentage Minimum percentage to consider hot (e.g., 5.0 for 5%)
 * @return Number of hot paths identified
 */
int nsa_identify_hot_paths(nsa_profiling_results_t *results, double threshold_percentage);

/**
 * @brief Analyze cache performance
 * @param results Profiling results
 * @return 0 on success, negative on error
 */
int nsa_analyze_cache_performance(nsa_profiling_results_t *results);

/**
 * @brief Analyze branch prediction performance
 * @param results Profiling results
 * @return 0 on success, negative on error
 */
int nsa_analyze_branch_performance(nsa_profiling_results_t *results);

/**
 * @brief Analyze memory access patterns
 * @param results Profiling results
 * @return 0 on success, negative on error
 */
int nsa_analyze_memory_patterns(nsa_profiling_results_t *results);

/**
 * @brief Generate optimization recommendations based on profiling data
 * @param results Profiling results
 * @param recommendations Output buffer for recommendations
 * @param buffer_size Size of output buffer
 * @return 0 on success, negative on error
 */
int nsa_generate_optimization_recommendations(const nsa_profiling_results_t *results,
                                             char *recommendations,
                                             size_t buffer_size);

/* ========================================================================
 * REPORTING FUNCTIONS
 * ======================================================================== */

/**
 * @brief Generate a detailed profiling report
 * @param results Profiling results
 * @param report_buffer Output buffer for report
 * @param buffer_size Size of output buffer
 * @return 0 on success, negative on error
 */
int nsa_generate_profiling_report(const nsa_profiling_results_t *results,
                                 char *report_buffer,
                                 size_t buffer_size);

/**
 * @brief Export profiling data to file
 * @param results Profiling results
 * @param filename Output filename
 * @param format Export format ("json", "csv", "xml")
 * @return 0 on success, negative on error
 */
int nsa_export_profiling_data(const nsa_profiling_results_t *results,
                             const char *filename,
                             const char *format);

/**
 * @brief Compare two profiling results for regression analysis
 * @param baseline Baseline profiling results
 * @param current Current profiling results
 * @param comparison_report Output buffer for comparison report
 * @param buffer_size Size of output buffer
 * @return 0 on success, negative on error
 */
int nsa_compare_profiling_results(const nsa_profiling_results_t *baseline,
                                 const nsa_profiling_results_t *current,
                                 char *comparison_report,
                                 size_t buffer_size);

/* ========================================================================
 * INTERNAL INSTRUMENTATION FUNCTIONS
 * ======================================================================== */

/**
 * @brief Record function entry (internal use)
 */
void nsa_profiler_function_enter(const char *func_name, void *return_address);

/**
 * @brief Record function exit (internal use)
 */
void nsa_profiler_function_exit(const char *func_name);

/**
 * @brief Begin profiling a code section (internal use)
 */
void nsa_profiler_section_begin(const char *section_name);

/**
 * @brief End profiling a code section (internal use)
 */
void nsa_profiler_section_end(const char *section_name);

#ifdef __cplusplus
}
#endif

#endif /* NSA_PROFILER_H */
