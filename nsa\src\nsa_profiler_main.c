/**
 * @file nsa_profiler_main.c
 * @brief NSA Profiler Tool Main Program
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <getopt.h>
#include <unistd.h>
#include <signal.h>

#include "nsa_profiler.h"
#include "nsa_logging.h"

/* Global flag for signal handling */
static volatile bool g_profiling_active = false;

/* Signal handler for graceful shutdown */
static void signal_handler(int sig) {
    printf("\nReceived signal %d, stopping profiler...\n", sig);
    g_profiling_active = false;
}

/* Command line options */
static struct option long_options[] = {
    {"help",        no_argument,       0, 'h'},
    {"mode",        required_argument, 0, 'm'},
    {"duration",    required_argument, 0, 'd'},
    {"sample-rate", required_argument, 0, 's'},
    {"output",      required_argument, 0, 'o'},
    {"verbose",     no_argument,       0, 'v'},
    {"hot-paths",   no_argument,       0, 'H'},
    {"functions",   no_argument,       0, 'f'},
    {0, 0, 0, 0}
};

static void print_usage(const char *program_name) {
    printf("NSA Profiler Tool\n");
    printf("Usage: %s [OPTIONS]\n\n", program_name);
    printf("Options:\n");
    printf("  -h, --help              Show this help message\n");
    printf("  -m, --mode MODE         Profiling mode: sampling, instrumentation, hybrid\n");
    printf("  -d, --duration SEC      Profiling duration in seconds (default: 30)\n");
    printf("  -s, --sample-rate HZ    Sampling rate in Hz (default: 1000)\n");
    printf("  -o, --output FILE       Output report file\n");
    printf("  -v, --verbose           Verbose output\n");
    printf("  -H, --hot-paths         Show hot path analysis\n");
    printf("  -f, --functions         Show function-level analysis\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s --mode sampling --duration 60 --sample-rate 2000\n", program_name);
    printf("  %s --mode hybrid --duration 30 --hot-paths\n", program_name);
    printf("  %s --mode instrumentation --output profile_report.txt\n", program_name);
}

static nsa_profile_mode_t parse_mode(const char *mode_str) {
    if (strcmp(mode_str, "sampling") == 0) {
        return NSA_PROFILE_SAMPLING;
    } else if (strcmp(mode_str, "instrumentation") == 0) {
        return NSA_PROFILE_INSTRUMENTATION;
    } else if (strcmp(mode_str, "hybrid") == 0) {
        return NSA_PROFILE_HYBRID;
    } else {
        return NSA_PROFILE_DISABLED;
    }
}

static void print_function_analysis(const nsa_profiling_results_t *results) {
    printf("\n=== Function-Level Analysis ===\n");
    printf("%-30s %10s %15s %15s %10s\n", 
           "Function", "Calls", "Total Cycles", "Avg Cycles", "CPU %");
    printf("%-30s %10s %15s %15s %10s\n", 
           "--------", "-----", "------------", "----------", "-----");
    
    for (uint32_t i = 0; i < results->function_count && i < 20; i++) {
        const nsa_function_profile_t *func = &results->functions[i];
        printf("%-30s %10lu %15lu %15lu %9.2f\n",
               func->name,
               func->call_count,
               func->total_cycles,
               func->avg_cycles,
               func->cpu_percentage);
    }
}

static void print_hot_path_analysis(const nsa_profiling_results_t *results) {
    printf("\n=== Hot Path Analysis ===\n");
    
    if (results->hot_path_count == 0) {
        printf("No hot paths detected.\n");
        return;
    }
    
    for (uint32_t i = 0; i < results->hot_path_count; i++) {
        const nsa_hot_path_t *path = &results->hot_paths[i];
        printf("\nHot Path %u: %s\n", i + 1, path->path_description);
        printf("  Total Cycles: %lu\n", path->total_cycles);
        printf("  Hit Count: %lu\n", path->hit_count);
        printf("  Percentage: %.2f%%\n", path->percentage);
        printf("  Priority: %u/10\n", path->optimization_priority);
        
        if (strlen(path->optimization_suggestions) > 0) {
            printf("  Suggestions: %s\n", path->optimization_suggestions);
        }
        
        printf("  Call Stack:\n");
        for (uint32_t j = 0; j < path->stack_depth && j < 10; j++) {
            printf("    %u: %s\n", j, path->call_stack[j].function_name);
        }
    }
}

static void print_cache_analysis(const nsa_profiling_results_t *results) {
    printf("\n=== Cache Performance Analysis ===\n");
    printf("L1 Cache Hit Rate: %.2f%%\n", results->cache_analysis.l1_hit_rate);
    printf("L2 Cache Hit Rate: %.2f%%\n", results->cache_analysis.l2_hit_rate);
    printf("L3 Cache Hit Rate: %.2f%%\n", results->cache_analysis.l3_hit_rate);
    printf("TLB Hit Rate: %.2f%%\n", results->cache_analysis.tlb_hit_rate);
}

static void print_efficiency_scores(const nsa_profiling_results_t *results) {
    printf("\n=== Efficiency Scores ===\n");
    printf("CPU Efficiency:    %.1f/100\n", results->cpu_efficiency);
    printf("Memory Efficiency: %.1f/100\n", results->memory_efficiency);
    printf("Cache Efficiency:  %.1f/100\n", results->cache_efficiency);
    printf("Overall Efficiency: %.1f/100\n", results->overall_efficiency);
}

int main(int argc, char *argv[]) {
    int opt;
    int option_index = 0;
    
    /* Default parameters */
    nsa_profile_mode_t mode = NSA_PROFILE_SAMPLING;
    uint32_t duration = 30;
    uint32_t sample_rate = 1000;
    char *output_file = NULL;
    bool verbose = false;
    bool show_hot_paths = false;
    bool show_functions = false;
    
    /* Parse command line arguments */
    while ((opt = getopt_long(argc, argv, "hm:d:s:o:vHf", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'h':
                print_usage(argv[0]);
                return 0;
                
            case 'm':
                mode = parse_mode(optarg);
                if (mode == NSA_PROFILE_DISABLED) {
                    fprintf(stderr, "Invalid profiling mode: %s\n", optarg);
                    return 1;
                }
                break;
                
            case 'd':
                duration = atoi(optarg);
                if (duration == 0) {
                    fprintf(stderr, "Invalid duration: %s\n", optarg);
                    return 1;
                }
                break;
                
            case 's':
                sample_rate = atoi(optarg);
                if (sample_rate == 0) {
                    fprintf(stderr, "Invalid sample rate: %s\n", optarg);
                    return 1;
                }
                break;
                
            case 'o':
                output_file = optarg;
                break;
                
            case 'v':
                verbose = true;
                break;
                
            case 'H':
                show_hot_paths = true;
                break;
                
            case 'f':
                show_functions = true;
                break;
                
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    printf("NSA Profiler Tool Starting...\n");
    printf("Mode: %d, Duration: %us, Sample Rate: %u Hz\n", mode, duration, sample_rate);
    
    /* Set up signal handlers */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    /* Initialize profiler */
    if (nsa_profiler_init(mode, sample_rate) != 0) {
        fprintf(stderr, "Failed to initialize profiler\n");
        return 1;
    }
    
    /* Start profiling */
    printf("Starting profiling...\n");
    if (nsa_profiler_start() != 0) {
        fprintf(stderr, "Failed to start profiling\n");
        nsa_profiler_cleanup();
        return 1;
    }
    
    g_profiling_active = true;
    
    /* Run for specified duration */
    printf("Profiling for %u seconds... (Press Ctrl+C to stop early)\n", duration);
    
    for (uint32_t i = 0; i < duration && g_profiling_active; i++) {
        sleep(1);
        if (verbose && (i % 10 == 0)) {
            printf("Profiling... %u/%u seconds\n", i, duration);
        }
    }
    
    /* Stop profiling */
    printf("Stopping profiler...\n");
    if (nsa_profiler_stop() != 0) {
        fprintf(stderr, "Failed to stop profiler\n");
    }
    
    /* Get results */
    nsa_profiling_results_t results;
    if (nsa_profiler_get_results(&results) != 0) {
        fprintf(stderr, "Failed to get profiling results\n");
        nsa_profiler_cleanup();
        return 1;
    }
    
    /* Display results */
    printf("\n=== Profiling Results ===\n");
    printf("Total Samples: %lu\n", results.total_samples);
    printf("Total Cycles: %lu\n", results.total_cycles);
    printf("Functions Profiled: %u\n", results.function_count);
    printf("Hot Paths Found: %u\n", results.hot_path_count);
    
    /* Show efficiency scores */
    print_efficiency_scores(&results);
    
    /* Show cache analysis */
    print_cache_analysis(&results);
    
    /* Show function analysis if requested */
    if (show_functions || results.function_count > 0) {
        print_function_analysis(&results);
    }
    
    /* Show hot path analysis if requested */
    if (show_hot_paths || results.hot_path_count > 0) {
        print_hot_path_analysis(&results);
    }
    
    /* Generate optimization recommendations */
    char recommendations[4096];
    if (nsa_generate_optimization_recommendations(&results, recommendations, sizeof(recommendations)) == 0) {
        printf("\n=== Optimization Recommendations ===\n");
        printf("%s\n", recommendations);
    }
    
    /* Save detailed report if output file specified */
    if (output_file) {
        char report_buffer[16384];
        if (nsa_generate_profiling_report(&results, report_buffer, sizeof(report_buffer)) == 0) {
            FILE *fp = fopen(output_file, "w");
            if (fp) {
                fprintf(fp, "%s", report_buffer);
                fclose(fp);
                printf("Detailed report saved to: %s\n", output_file);
            } else {
                fprintf(stderr, "Failed to write report to: %s\n", output_file);
            }
        }
    }
    
    /* Cleanup */
    nsa_profiler_cleanup();
    
    printf("Profiler tool finished.\n");
    return 0;
}
