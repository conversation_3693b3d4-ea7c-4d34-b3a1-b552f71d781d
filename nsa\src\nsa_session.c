/**
 * @file nsa_session.c
 * @brief NSA Session Management with PML Integration
 * 
 * This file implements session management functionality for the NSA
 * (Network Security Application) with deep packet inspection using
 * the PML (Packet Matching Language) engine.
 * 
 * Key features:
 * - Session lifecycle management (create, analyze, destroy)
 * - PML context management per session
 * - Deep packet inspection and threat analysis
 * - Application classification and identification
 * - Background work offloading for intensive analysis
 * - Integration with DPIF session management
 * 
 * <AUTHOR>
 * @date 2025
 * @copyright Calix Inc.
 */

#include <arpa/inet.h> /* For inet_ntop */
#include <endian.h>    /* For htobe64, be64toh, etc. */
#include <errno.h>
#include <netinet/in.h> /* For AF_INET, AF_INET6 */
#include <rte_mempool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h> /* For usleep */

#include "nsa.h"
#include "nsa_helper.h"
#include "nsa_pml.h"

static nsa_rx_thread_pml_context_t *nsa_get_current_thread_pml_context(void);

#define FILTER_TABLE_STATE (1)

/* ========================================================================
 * PML Integration Core Functions
 * ======================================================================== */

static inline uint16_t calculate_pml_datetime_heavy(void) {
    time_t now;
    struct tm result_tm;

    now = time(NULL);
    if (localtime_r(&now, &result_tm)) {
        uint32_t minutes_since_sunday = result_tm.tm_wday * 24 * 60 + result_tm.tm_hour * 60 + result_tm.tm_min;
        return (uint16_t) minutes_since_sunday;
    }
    return 0;
}

static inline uint16_t get_cached_pml_datetime(nsa_rx_thread_pml_context_t *thread_pml_ctx) {
    uint64_t current_tsc = rte_rdtsc();

    if (unlikely(current_tsc - thread_pml_ctx->last_time_update_tsc > thread_pml_ctx->time_update_interval_tsc)) {
        thread_pml_ctx->cached_pml_datetime = calculate_pml_datetime_heavy();
        thread_pml_ctx->last_time_update_tsc = current_tsc;
        NSA_LOG_DEBUG("Lcore %u: PML datetime cache updated to %u.",
                      thread_pml_ctx->lcore_id,
                      thread_pml_ctx->cached_pml_datetime);
    }

    return thread_pml_ctx->cached_pml_datetime;
}

/**
 * @brief Initialize PML engine for NSA
 * 
 * Initializes the PML (Packet Matching Language) engine and loads
 * the NSA-specific DPI program for deep packet inspection.
 * 
 * @return 0 on success, negative error code on failure
 */
int nsa_pml_init(void) {
    int ret;

    NSA_LOG_INFO("Initializing NSA PML engine");

    /* Initialize PML library */
    ret = pml_init();
    if (ret < 0) {
        NSA_LOG_ERROR("Failed to initialize PML library: %d", ret);
        return ret;
    }

    NSA_LOG_INFO("NSA PML engine initialized successfully");

    return 0;
}

/**
 * @brief Cleanup PML engine resources
 * 
 * Releases all PML-related resources and cleans up the master
 * PML instance.
 */
void nsa_pml_cleanup(void) {
    NSA_LOG_INFO("NSA PML engine cleanup completed");
}

/**
 * @brief Populate PML context from flow information
 * 
 * Fills the PML context structure with network flow information
 * extracted from the DPIF flow info.
 * 
 * @param[out] ctx       NSA PML context to populate
 * @param[in]  flow_info DPIF flow information
 */
void nsa_populate_pml_context(struct nsa_pml_context *ctx, const struct dpif_flow_info *flow_info) {
    if (!ctx || !flow_info) {
        NSA_LOG_ERROR("Invalid parameters: ctx=%p, flow_info=%p", (void *) ctx, (void *) flow_info);
        return;
    }

    /* Initialize context to zero */
    memset(ctx, 0, sizeof(struct nsa_pml_context));

    /* Populate IP addresses */
    if (flow_info->address_family == AF_INET) {
        inet_ntop(AF_INET, &flow_info->src_ip_u.ipv4_src_ip, (char *) ctx->src, 16);
        inet_ntop(AF_INET, &flow_info->dst_ip_u.ipv4_dst_ip, (char *) ctx->dst, 16);
    } else if (flow_info->address_family == AF_INET6) {
        inet_ntop(AF_INET6, flow_info->src_ip_u.ipv6_src_ip, (char *) ctx->src, 16);
        inet_ntop(AF_INET6, flow_info->dst_ip_u.ipv6_dst_ip, (char *) ctx->dst, 16);
    } else {
        NSA_LOG_WARNING("Unknown address family: %u", flow_info->address_family);
        return;
    }

    /* Populate ports and protocol */
    ctx->sport = ntohs(flow_info->src_port);
    ctx->dport = ntohs(flow_info->dst_port);
    ctx->proto = flow_info->proto;

    /* Initialize verdict and zones */
    ctx->verdict = 0;
    ctx->szone = flow_info->src_zone;
    ctx->dzone = flow_info->dst_zone;

    /* Initialize other fields */
    ctx->group = 0;
    ctx->user = 0;
    ctx->host = 0;
    ctx->alp = 0;
    ctx->app = 0;
    ctx->dev = 0;
    ctx->geo = 0;
    ctx->threat = 0;
    ctx->cat = 0;
    ctx->date = 0;

    /* Populate device field from MAC address */
    memcpy(ctx->device, flow_info->src_mac, 6);

    NSA_LOG_DEBUG("PML context populated: proto=%u, sport=%u, dport=%u, szone=%u, dzone=%u",
                  ctx->proto,
                  ctx->sport,
                  ctx->dport,
                  ctx->szone,
                  ctx->dzone);
}

/**
 * @brief Create NSA session context with PML integration
 * 
 * Creates and initializes a new NSA session context with PML
 * instance and analysis state for the given session.
 * 
 * @param[in] flow_info  Flow information for the session
 * @param[in] session_id DPIF session descriptor
 * @return Pointer to allocated context, or NULL on failure
 */
nsa_session_context_t *nsa_create_session_context(const struct dpif_flow_info *flow_info, int session_id) {
    nsa_session_context_t *nsa_ctx = NULL;
    int ret;

    if (!flow_info) {
        NSA_LOG_ERROR("Invalid flow_info for session %d", session_id);
        return NULL;
    }

    /* Allocate NSA session context */
    nsa_rx_thread_pml_context_t *thread_pml_ctx = nsa_get_current_thread_pml_context();
    if (!thread_pml_ctx) {
        NSA_LOG_ERROR("No thread-specific PML context available for session %d", session_id);
        return NULL;
    }
    if (rte_mempool_get(thread_pml_ctx->session_ctx_pool, (void **) &nsa_ctx) < 0) {
        NSA_LOG_ERROR("Session context pool exhausted for lcore %u", thread_pml_ctx->lcore_id);
        return NULL;
    }

    /* Initialize session context */
    nsa_ctx->pml_classify_instance = NULL;
    nsa_ctx->pml_rules_instance = NULL;
    nsa_ctx->rule_state = 0; /* Initialize to 0 - will be set to 1 only if rules are loaded */
    nsa_ctx->last_analysis_time = time(NULL);
    nsa_ctx->app_classification = 0;
    nsa_ctx->category = 0;
    nsa_ctx->packets_analyzed = 0;
    nsa_ctx->bytes_analyzed = 0;
    nsa_ctx->analysis_complete = 0;
    memset(nsa_ctx->app_name, 0, sizeof(nsa_ctx->app_name));
    memset(nsa_ctx->alp_name, 0, sizeof(nsa_ctx->alp_name));

    /* flow_info->direction: 0 means packet direction matches canonical key (small->big) */
    /*                       1 means packet direction is opposite to canonical key (big->small) */
    if (flow_info->direction == 1) {
        nsa_ctx->first_packet_reversed = true;
    } else {
        nsa_ctx->first_packet_reversed = false;
    }

    /* Populate PML context from flow information */
    nsa_populate_pml_context(&nsa_ctx->pml_context, flow_info);

    /* Clone classification PML instance from thread-specific instance */
    if (thread_pml_ctx->classify_available && thread_pml_ctx->thread_classify_pml) {
        ret = pml_clone(&nsa_ctx->pml_classify_instance, thread_pml_ctx->thread_classify_pml);
        if (ret < 0) {
            NSA_LOG_ERROR("Failed to clone classification PML instance for session %d: %d", session_id, ret);
            rte_mempool_put(thread_pml_ctx->session_ctx_pool, nsa_ctx);
            return NULL;
        }

        /* Initialize PML context for classification */
        ret = pml_main(nsa_ctx->pml_classify_instance, &nsa_ctx->pml_context);
        if (ret < 0) {
            NSA_LOG_WARNING("Classification PML main initialization returned %d for session %d", ret, session_id);
            /* Continue anyway - this might not be fatal */
        }

        NSA_LOG_DEBUG(
            "Classification PML instance cloned for session %d (from thread %u)", session_id, thread_pml_ctx->lcore_id);
    } else {
        NSA_LOG_ERROR("No thread-specific classification PML available for session %d", session_id);
        rte_mempool_put(thread_pml_ctx->session_ctx_pool, nsa_ctx);
        return NULL;
    }

    /* Clone rules evaluation PML instance from thread-specific instance */
    if (thread_pml_ctx->rules_available && thread_pml_ctx->thread_rules_pml) {
        ret = pml_clone(&nsa_ctx->pml_rules_instance, thread_pml_ctx->thread_rules_pml);
        if (ret < 0) {
            NSA_LOG_ERROR("Failed to clone rules PML instance for session %d: %d", session_id, ret);
            /* Don't fail - continue without rules evaluation */
            nsa_ctx->pml_rules_instance = NULL;
        } else {
            /* Initialize rule state only if rules are successfully loaded */
            nsa_ctx->rule_state = 1;
            NSA_LOG_DEBUG(
                "Rules PML instance cloned for session %d (from thread %u)", session_id, thread_pml_ctx->lcore_id);
        }
    } else {
        NSA_LOG_DEBUG("No thread-specific rules PML available for session %d - rule evaluation disabled", session_id);
    }

    NSA_LOG_DEBUG("Created NSA session context for session %d (classify: %s, rules: %s)",
                  session_id,
                  nsa_ctx->pml_classify_instance ? "yes" : "no",
                  nsa_ctx->pml_rules_instance ? "yes" : "no");
    return nsa_ctx;
}

/**
 * @brief Destroy NSA session context
 * 
 * Cleans up and deallocates the NSA session context including
 * the associated PML instance and resources.
 * 
 * @param[in] nsa_ctx NSA session context to destroy
 */
void nsa_destroy_session_context(nsa_session_context_t *nsa_ctx) {
    if (!nsa_ctx) {
        return;
    }

    /* Cleanup classification PML instance */
    if (nsa_ctx->pml_classify_instance) {
        pml_exit(nsa_ctx->pml_classify_instance);
        nsa_ctx->pml_classify_instance = NULL;
    }

    /* Cleanup rules PML instance */
    if (nsa_ctx->pml_rules_instance) {
        pml_exit(nsa_ctx->pml_rules_instance);
        nsa_ctx->pml_rules_instance = NULL;
    }

    NSA_LOG_DEBUG("Destroying NSA session context (analyzed %u packets, %lu bytes)",
                  nsa_ctx->packets_analyzed,
                  nsa_ctx->bytes_analyzed);

    /* Free the context */
    nsa_rx_thread_pml_context_t *thread_pml_ctx = nsa_get_current_thread_pml_context();
    if (thread_pml_ctx && thread_pml_ctx->session_ctx_pool) {
        rte_mempool_put(thread_pml_ctx->session_ctx_pool, nsa_ctx);
    } else {
        // fallback, should not happen
        free(nsa_ctx);
    }
}

/**
 * @brief Evaluate PML analysis results
 * 
 * Processes the results from PML analysis and updates the NSA
 * session context with threat assessment and application classification.
 * 
 * @param[in] nsa_ctx    NSA session context
 * @param[in] session_id DPIF session descriptor
 * @return 0 on success, negative error code on failure
 */
int nsa_evaluate_pml_results(nsa_session_context_t *nsa_ctx, int session_id) {
    const char *app_name = "n/a";
    const char *alp_name = "n/a";

    if (!nsa_ctx) {
        NSA_LOG_ERROR("Invalid NSA context for session %d", session_id);
        return -EINVAL;
    }

    /* Use classify instance for symbol resolution */
    struct pml *symbol_instance = nsa_ctx->pml_classify_instance;
    if (!symbol_instance) {
        NSA_LOG_DEBUG("No PML instance available for symbol resolution in session %d", session_id);
        return 0;
    }

    int alp_len = pml_symbol(symbol_instance, nsa_ctx->pml_context.alp, &alp_name);
    if (alp_len >= 0 && alp_name) {
        /* !!!Copy with proper length handling - pml_symbol returns non-null-terminated string!!! */
        int copy_len_alp = (alp_len < sizeof(nsa_ctx->alp_name) - 1) ? alp_len : sizeof(nsa_ctx->alp_name) - 1;
        memcpy(nsa_ctx->alp_name, alp_name, copy_len_alp);
        nsa_ctx->alp_name[copy_len_alp] = '\0';
    }

    int app_len = pml_symbol(symbol_instance, nsa_ctx->pml_context.app, &app_name);
    if (app_len >= 0 && app_name) {
        int copy_len_app = (app_len < sizeof(nsa_ctx->app_name) - 1) ? app_len : sizeof(nsa_ctx->app_name) - 1;
        memcpy(nsa_ctx->app_name, app_name, copy_len_app);
        nsa_ctx->app_name[copy_len_app] = '\0';
    }

    return 0;
}

/**
 * @brief Get session verdict based on analysis
 * 
 * Determines the final verdict for a session based on threat level
 * and application classification results.
 * 
 * @param[in] nsa_ctx NSA session context
 * @return DPI verdict code
 */
dpi_verdict_t nsa_get_session_verdict(nsa_session_context_t *nsa_ctx) {
    if (!nsa_ctx) {
        return DPI_VERDICT_ERROR;
    }

    /* Check PML context verdict first */
    if (nsa_ctx->pml_context.verdict > 0) {
        NSA_LOG_DEBUG("Session verdict from PML: %u", nsa_ctx->pml_context.verdict);
        return (dpi_verdict_t) nsa_ctx->pml_context.verdict;
    }

    /* High threat level - block immediately */
    if (nsa_ctx->pml_context.threat >= NSA_THREAT_BLOCK_THRESHOLD) {
        NSA_LOG_WARNING("Blocking session due to high threat level: %u", nsa_ctx->pml_context.threat);
        return DPI_VERDICT_DROP;
    }

    /* Medium threat level or unknown application - continue analysis */
    if (nsa_ctx->pml_context.threat > 0 || nsa_ctx->app_classification == 0) {
        return DPI_VERDICT_PENDING;
    }

    /* Low/no threat and known application - permit */
    return DPI_VERDICT_PERMITTED;
}

/**
 * @brief Check if background work should be offloaded
 * 
 * Determines whether the current session analysis requires
 * background processing on worker threads.
 * 
 * @param[in] nsa_ctx NSA session context
 * @return 1 if work should be offloaded, 0 otherwise
 */
int nsa_should_offload_work(nsa_session_context_t *nsa_ctx) {
    if (!nsa_ctx) {
        return 0;
    }

    /* Offload for intensive analysis scenarios */
    return (nsa_ctx->pml_context.threat > 0 && nsa_ctx->app_classification == 0 && nsa_ctx->packets_analyzed > 5);
}

/**
 * @brief Schedule background work for session
 * 
 * Creates and schedules background work for intensive analysis
 * tasks that should not block the main data path.
 * 
 * @param[in] session_id DPIF session descriptor
 * @param[in] nsa_ctx    NSA session context
 * @return 0 on success, negative error code on failure
 */
int nsa_schedule_background_work(int session_id, nsa_session_context_t *nsa_ctx) {
    struct dpi_work *work;
    uint8_t *data_buf;
    int ret;

    if (!nsa_ctx) {
        return -EINVAL;
    }

    /* Allocate work item */
    work = dpi_allocate_work(64);
    if (!work) {
        NSA_LOG_ERROR("Failed to allocate work item for session %d", session_id);
        return -ENOMEM;
    }

    /* Set work type and parameters */
    dpi_work_set_type(work, 1);    /* NSA deep analysis work type */
    dpi_work_set_length(work, 64); /* Data length */

    /* Populate work data */
    data_buf = dpi_work_get_data_buffer(work);
    if (data_buf) {
        snprintf((char *) data_buf,
                 64,
                 "NSA_DEEP_ANALYSIS:threat=%u:app=%u",
                 nsa_ctx->pml_context.threat,
                 nsa_ctx->app_classification);
    } else {
        NSA_LOG_ERROR("Failed to get work data buffer for session %d", session_id);
        /* Note: work item will be freed by DPIF if schedule fails */
        return -ENOMEM;
    }

    /* Schedule the work */
    ret = dpif_schedule_work_for_session(session_id, work);
    if (ret != 0) {
        NSA_LOG_ERROR("Failed to schedule background work for session %d: %d", session_id, ret);
        return ret;
    }

    NSA_LOG_DEBUG("Scheduled background work for session %d", session_id);
    return 0;
}

/**
 * @brief Prepare PML context for rule evaluation (convert to network byte order)
 * 
 * Converts certain fields in the PML context to network byte order for
 * rule evaluation, similar to PAL's ctxhtobe function.
 * 
 * @param[in,out] ctx PML context to prepare
 */
static void nsa_prepare_context_for_eval(struct nsa_pml_context *ctx) {
    if (!ctx)
        return;

    /* Convert to network byte order for evaluation */
    ctx->sport = htons(ctx->sport);
    ctx->dport = htons(ctx->dport);
    ctx->szone = htons(ctx->szone);
    ctx->dzone = htons(ctx->dzone);
    ctx->group = htons(ctx->group);
    ctx->user = htonl(ctx->user);
    ctx->host = htobe64(ctx->host);
    ctx->alp = htons(ctx->alp);
    ctx->app = htons(ctx->app);
    ctx->dev = htons(ctx->dev);
    ctx->geo = htons(ctx->geo);
    ctx->threat = htons(ctx->threat);
    ctx->cat = htons(ctx->cat);
    ctx->date = htons(ctx->date);
}

/**
 * @brief Restore PML context from rule evaluation (convert back to host byte order)
 * 
 * Converts fields back to host byte order after rule evaluation,
 * similar to PAL's ctxbetoh function.
 * 
 * @param[in,out] ctx PML context to restore
 */
static void nsa_restore_context_from_eval(struct nsa_pml_context *ctx) {
    if (!ctx)
        return;

    /* Convert back to host byte order */
    ctx->sport = ntohs(ctx->sport);
    ctx->dport = ntohs(ctx->dport);
    ctx->szone = ntohs(ctx->szone);
    ctx->dzone = ntohs(ctx->dzone);
    ctx->group = ntohs(ctx->group);
    ctx->user = ntohl(ctx->user);
    ctx->host = be64toh(ctx->host);
    ctx->alp = ntohs(ctx->alp);
    ctx->app = ntohs(ctx->app);
    ctx->dev = ntohs(ctx->dev);
    ctx->geo = ntohs(ctx->geo);
    ctx->threat = ntohs(ctx->threat);
    ctx->cat = ntohs(ctx->cat);
    ctx->date = ntohs(ctx->date);
}

/* ========================================================================
 * DPIF Session Callback Implementations
 * ======================================================================== */

/**
 * @brief NSA session creation handler
 * 
 * Called when a new session is created by DPIF. Initializes NSA-specific
 * session context with PML integration for deep packet inspection.
 * 
 * @param[in] flow_info Information about the flow
 * @param[in] session_id DPIF session descriptor
 * @return 0 on success, negative error code on failure
 */
int nsa_session_create(const struct dpif_flow_info *flow_info, uint32_t session_id) {
    nsa_session_context_t *nsa_ctx;
    int ret;

    NSA_LOG_DEBUG("NSA session create: session_id=%d", session_id);

    /* Create NSA session context with PML integration */
    nsa_ctx = nsa_create_session_context(flow_info, session_id);
    if (!nsa_ctx) {
        NSA_LOG_ERROR("Failed to create NSA context for session %d", session_id);
        return -ENOMEM;
    }

    /* Store context in DPIF session */
    ret = dpif_set_app_data(session_id, nsa_ctx);
    if (ret < 0) {
        NSA_LOG_ERROR("Failed to set session user data for session %d: %d", session_id, ret);
        nsa_destroy_session_context(nsa_ctx);
        return ret;
    }

    /* Enqueue session creation event for helper thread processing */
    nsa_helper_event_enqueue(NSA_EVENT_SESSION_CREATE, session_id, (void *) flow_info, sizeof(struct dpif_flow_info));

    NSA_LOG_DEBUG("NSA session %d created successfully", session_id);
    return 0;
}

/**
 * @brief NSA session destruction handler
 * 
 * Called when a session is being destroyed by DPIF. Cleans up NSA-specific
 * session context and PML resources.
 * 
 * @param[in] session_id DPIF session descriptor
 * @return 0 on success, negative error code on failure
 */
int nsa_session_destroy(uint32_t session_id, void *app_data) {
    nsa_session_context_t *nsa_ctx = (nsa_session_context_t *) app_data;

    NSA_LOG_DEBUG("NSA session destroy: session_id=%d", session_id);

    if (nsa_ctx) {
        NSA_LOG_DEBUG("Destroying session %d (threat: %u, app: %s, packets: %u)",
                      session_id,
                      nsa_ctx->pml_context.threat,
                      nsa_ctx->app_name,
                      nsa_ctx->packets_analyzed);

        /* Clean up NSA session context */
        nsa_destroy_session_context(nsa_ctx);
    }

    /* Enqueue session destruction event */
    nsa_helper_event_enqueue(NSA_EVENT_SESSION_DESTROY, session_id, NULL, 0);

    NSA_LOG_DEBUG("NSA session %d destroyed", session_id);
    return 0;
}

static char *format_macaddr(const uint8_t *mac, char *buf, size_t size) {
    snprintf(buf, size, "%02x:%02x:%02x:%02x:%02x:%02x", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    return buf;
}

void dump_pml_context(int session_id, nsa_session_context_t *nsa_ctx) {
    const char *app_name = "nil", *alp_name = "nil";
    int app_name_len = 0, alp_name_len = 0;
    char macaddr[18];
    app_name_len = pml_symbol(nsa_ctx->pml_classify_instance, nsa_ctx->pml_context.app, &app_name);
    alp_name_len = pml_symbol(nsa_ctx->pml_classify_instance, nsa_ctx->pml_context.alp, &alp_name);

    /* Handle cases where pml_symbol fails */
    if (app_name_len < 0) {
        app_name = "unknown";
        app_name_len = 7;
    }
    if (alp_name_len < 0) {
        alp_name = "unknown";
        alp_name_len = 7;
    }

    NSA_LOG_DEBUG("Session %d PML context:", session_id);
    NSA_LOG_DEBUG("  device:%s proto:%u alp:%.*s app:%.*s",
                  format_macaddr(nsa_ctx->pml_context.device, macaddr, sizeof(macaddr)),
                  nsa_ctx->pml_context.proto,
                  alp_name_len,
                  alp_name,
                  app_name_len,
                  app_name);
    NSA_LOG_DEBUG(
        "  sip:%s sport:%u szone:%u", nsa_ctx->pml_context.src, nsa_ctx->pml_context.sport, nsa_ctx->pml_context.szone);
    NSA_LOG_DEBUG(
        "  dip:%s dport:%u dzone:%u", nsa_ctx->pml_context.dst, nsa_ctx->pml_context.dport, nsa_ctx->pml_context.dzone);
    NSA_LOG_DEBUG("  threat:%u cat:%u dev:%u verdict:%u group:%u user:%u",
                  nsa_ctx->pml_context.threat,
                  nsa_ctx->pml_context.cat,
                  nsa_ctx->pml_context.dev,
                  nsa_ctx->pml_context.verdict,
                  nsa_ctx->pml_context.group,
                  nsa_ctx->pml_context.user);

    return;
}

// In file: nsa_session.c (at the top with other static functions)

/**
 * @brief Dumps a block of memory in hex and ASCII format for debugging.
 *
 * @param title A title string for the dump.
 * @param data Pointer to the data to be dumped.
 * @param len The number of bytes to dump.
 */
void nsa_dump_payload_hex(const char *title, const uint8_t *data, int len) {
    if (!data || len <= 0) {
        return;
    }

    const int bytes_per_line = 16;
    char line_buf[128];
    int line_chars_written;

    NSA_LOG_INFO("--- BEGIN PAYLOAD DUMP: %s (len=%d) ---", title, len);

    for (int i = 0; i < len; i += bytes_per_line) {
        // Start of the line: Offset
        line_chars_written = snprintf(line_buf, sizeof(line_buf), "  %04x: ", i);

        // Hex part
        for (int j = 0; j < bytes_per_line; ++j) {
            if (i + j < len) {
                line_chars_written += snprintf(
                    line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "%02x ", data[i + j]);
            } else {
                line_chars_written +=
                    snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "   ");
            }
        }

        // ASCII part
        line_chars_written += snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, " |");
        for (int j = 0; j < bytes_per_line; ++j) {
            if (i + j < len) {
                char c = data[i + j];
                if (c >= 32 && c <= 126) {  // isprint()
                    line_chars_written +=
                        snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "%c", c);
                } else {
                    line_chars_written +=
                        snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, ".");
                }
            }
        }
        line_chars_written += snprintf(line_buf + line_chars_written, sizeof(line_buf) - line_chars_written, "|");

        // Print the complete line using NSA_LOG_DEBUG
        NSA_LOG_INFO("%s", line_buf);
    }
    NSA_LOG_INFO("--- END PAYLOAD DUMP: %s ---", title);
}

/**
 * @brief NSA session packet analysis handler
 * 
 * Called for each packet belonging to a session. Performs deep packet
 * inspection using PML and updates threat assessment and application
 * classification.
 * 
 * @param[in] session_id DPIF session descriptor
 * @param[in] packet Packet data structure
 * @return DPI verdict code
 */
int nsa_session_analyze(uint32_t session_id, struct dpi_packet *packet) {
    nsa_session_context_t *nsa_ctx = NULL;
    const uint8_t *data;
    int dir, len, ret;
    int scan_result;
    dpi_verdict_t verdict;

    NSA_LOG_DEBUG("NSA session analyze: session_id=%d", session_id);

    /* Retrieve NSA session context */
    ret = dpif_get_app_data(session_id, (void **) &nsa_ctx);
    if (ret < 0 || !nsa_ctx) {
        NSA_LOG_ERROR("Failed to get NSA context for session %d: %d", session_id, ret);
        return DPI_VERDICT_ERROR;
    }

    nsa_rx_thread_pml_context_t *thread_pml_ctx = nsa_get_current_thread_pml_context();
    if (thread_pml_ctx)
        nsa_ctx->pml_context.date = get_cached_pml_datetime(thread_pml_ctx);

    /* Get packet data */
    len = dpi_packet_getdata(packet, &data, &dir);

    /* Update packet statistics */
    nsa_ctx->packets_analyzed++;
    nsa_ctx->bytes_analyzed += len;
    //nsa_ctx->last_analysis_time = time(NULL);

    /* Perform PML pattern matching and analysis if available */
    if (nsa_ctx->pml_classify_instance) {
        /* Step 1: Perform pattern matching/classification using pml_scan */
        /* Use different match modes based on packet direction:
         * - Client to server: PML_MATCH_NORMAL
         * - Server to client: PML_MATCH_EXTEND
         */
        enum pml_match_lang match_mode;

        /* dir: 0 for small->big, 1 for big->small */
        if (dir == 0) {
            match_mode = nsa_ctx->first_packet_reversed ? PML_MATCH_EXTEND : PML_MATCH_NORMAL;
        } else {
            match_mode = nsa_ctx->first_packet_reversed ? PML_MATCH_NORMAL : PML_MATCH_EXTEND;
        }

        scan_result = pml_scan(nsa_ctx->pml_classify_instance, data, len, match_mode, &nsa_ctx->pml_context);
        if (scan_result < 0) {
            NSA_LOG_WARNING("PML scan failed for session %d: %d", session_id, scan_result);
            return DPI_VERDICT_ERROR;
        }

        /* Step 2: Update classification state based on scan results */
        if (scan_result == 0) {
            /* Scan complete - classification finished */
            NSA_LOG_DEBUG("PML scan completed for session %d", session_id);
            nsa_ctx->analysis_complete = 1;

            /* Human-read format context */
            ret = nsa_evaluate_pml_results(nsa_ctx, session_id);
            if (ret < 0) {
                NSA_LOG_ERROR("Failed to evaluate PML results for session %d: %d", session_id, ret);
                return DPI_VERDICT_ERROR;
            }
        } else {
            /* Scan ongoing - may need more packets for complete classification */
            NSA_LOG_DEBUG("PML scan in progress for session %d (state: %d)", session_id, scan_result);
            return DPI_VERDICT_PENDING;
        }

        /* Step 3: Perform rule evaluation using pml_eval if scan finish */
        if (nsa_ctx->rule_state > 0 && nsa_ctx->pml_rules_instance && nsa_ctx->analysis_complete) {
            /* Prepare context for rule evaluation (convert to network byte order if needed) */
            nsa_prepare_context_for_eval(&nsa_ctx->pml_context);

            /* Evaluate security rules and policies using dedicated rules instance */
            int eval_result = pml_eval(nsa_ctx->pml_rules_instance,
                                       FILTER_TABLE_STATE,
                                       (void *) &nsa_ctx->pml_context,
                                       sizeof(struct nsa_pml_context),
                                       0,
                                       &nsa_ctx->pml_context);

            if (eval_result < 0) {
                NSA_LOG_WARNING("PML eval failed for session %d: %d", session_id, eval_result);
                /* Continue with scan results */
            } else {
                NSA_LOG_DEBUG("PML eval completed for session %d return %d (verdict: %d)",
                              session_id,
                              eval_result,
                              nsa_ctx->pml_context.verdict);
            }
            /* Restore context from network byte order if needed */
            nsa_restore_context_from_eval(&nsa_ctx->pml_context);
        } else if (nsa_ctx->rule_state > 0 && !nsa_ctx->pml_rules_instance) {
            NSA_LOG_DEBUG("Rule evaluation requested for session %d but no rules instance available", session_id);
        }
#if 0
        /* Check if background work should be scheduled */
        if (nsa_should_offload_work(nsa_ctx)) {
            ret = nsa_schedule_background_work(session_id, nsa_ctx);
            if (ret < 0) {
                NSA_LOG_WARNING("Failed to schedule background work for session %d: %d", session_id, ret);
                /* Continue processing - this is not fatal */
            }
        }
#endif
    } else {
        /* Basic mode - no PML analysis available */
        NSA_LOG_DEBUG("Basic mode analysis for session %d: %d bytes", session_id, len);
        /* Set basic defaults for sessions without PML */
        nsa_ctx->app_classification = 0;
        strncpy(nsa_ctx->app_name, "basic", sizeof(nsa_ctx->app_name) - 1);
    }

    /* Dump pml context before return */
    //if (nsa_ctx->analysis_complete)
    dump_pml_context(session_id, nsa_ctx);

    /* Enqueue session enrich event for helper thread processing */
    nsa_helper_event_enqueue(NSA_EVENT_SESSION_ENRICH, session_id, (void *) nsa_ctx, sizeof(nsa_session_context_t));

    if (nsa_ctx->pml_context.verdict == 1)
        verdict = DPI_VERDICT_PERMITTED;
    else
        verdict = DPI_VERDICT_DROP;

    return verdict;
}

/**
 * @brief NSA session background work handler
 * 
 * Called by worker threads to process background work items scheduled
 * for intensive analysis tasks.
 * 
 * @param[in] session_id DPIF session descriptor
 * @param[in] work Work item to process
 * @return 0 on success, negative error code on failure
 */
int nsa_session_work(uint32_t session_id, struct dpi_work *work) {
    nsa_session_context_t *nsa_ctx = NULL;
    uint8_t *work_data;
    int ret;

    NSA_LOG_DEBUG("NSA session work: session_id=%d", session_id);

    /* Retrieve NSA session context */
    ret = dpif_get_app_data(session_id, (void **) &nsa_ctx);
    if (ret < 0 || !nsa_ctx) {
        NSA_LOG_ERROR("Failed to get NSA context for work processing session %d: %d", session_id, ret);
        return -EINVAL;
    }

    /* Get work data */
    work_data = dpi_work_get_data_buffer(work);
    if (work_data) {
        NSA_LOG_DEBUG("Processing background work for session %d: %s", session_id, (char *) work_data);

        /* TODO: Implement intensive analysis algorithms here
         * - Advanced pattern matching
         * - Behavioral analysis
         * - Threat intelligence correlation
         * - Machine learning inference
         */

        /* Simulate processing time */
        usleep(1000); /* 1ms processing time */

        /* Update analysis state */
        nsa_ctx->analysis_complete = 1;
    }

    NSA_LOG_DEBUG("Background work completed for session %d", session_id);
    return 0;
}

/**
 * @brief NSA session update handler
 * 
 * Called periodically for each active session to perform maintenance
 * tasks and policy updates.
 * 
 * @param[in] session_id DPIF session descriptor
 * @return 0 on success, negative error code on failure
 */
int nsa_session_update(uint32_t session_id) {
    nsa_session_context_t *nsa_ctx = NULL;
    int ret;
    time_t current_time;

    NSA_LOG_DEBUG("NSA session update: session_id=%d", session_id);

    /* Retrieve NSA session context */
    ret = dpif_get_app_data(session_id, (void **) &nsa_ctx);
    if (ret < 0 || !nsa_ctx) {
        /* Session might be in the process of being destroyed */
        NSA_LOG_DEBUG("No NSA context for session update %d", session_id);
        return 0;
    }

    current_time = time(NULL);

    /* Check for stale sessions */
    if (current_time - nsa_ctx->last_analysis_time > 300) { /* 5 minutes */
        NSA_LOG_INFO("Session %d appears stale (last analysis: %ld seconds ago)",
                     session_id,
                     current_time - nsa_ctx->last_analysis_time);
    }

    /* Update session statistics and perform maintenance */
    NSA_LOG_DEBUG("Session %d status: threat=%u, app=%s, packets=%u, bytes=%lu",
                  session_id,
                  nsa_ctx->pml_context.threat,
                  nsa_ctx->app_name,
                  nsa_ctx->packets_analyzed,
                  nsa_ctx->bytes_analyzed);

    return 0;
}

/**
 * @brief RX thread initialization callback for NSA
 * 
 * Creates thread-specific PML instances by cloning from master instances.
 * This ensures each RX thread has its own PML instances for thread safety.
 * 
 * @param lcore_id The lcore ID of the RX thread
 * @param thread_data Pointer to store thread-specific data
 * @return 0 on success, negative error code on failure
 */
static int nsa_rx_thread_init(uint32_t lcore_id, void **thread_data) {
    nsa_rx_thread_pml_context_t *pml_ctx;
    int ret;

    NSA_LOG_INFO("Initializing NSA thread-specific PML instances for RX lcore %u", lcore_id);

    /* Allocate thread-specific PML context */
    pml_ctx = calloc(1, sizeof(nsa_rx_thread_pml_context_t));
    if (!pml_ctx) {
        NSA_LOG_ERROR("Failed to allocate thread PML context for lcore %u: %s", lcore_id, strerror(errno));
        return -ENOMEM;
    }

    pml_ctx->lcore_id = lcore_id;
    pml_ctx->classify_available = 0;
    pml_ctx->rules_available = 0;

    ret = pml_load(&pml_ctx->thread_classify_pml, NSA_PML_CLASSIFY_FILE);
    if (ret < 0) {
        NSA_LOG_ERROR("Failed to load classification PML for lcore %u: %d", lcore_id, ret);
    } else {
        pml_ctx->classify_available = 1;
        NSA_LOG_INFO("Classification PML loaded for RX lcore %u", lcore_id);
    }

    ret = pml_load(&pml_ctx->thread_rules_pml, NSA_PML_RULES_FILE);
    if (ret < 0) {
        NSA_LOG_ERROR("Failed to load rules PML for lcore %u: %d", lcore_id, ret);
    } else {
        pml_ctx->rules_available = 1;
        NSA_LOG_INFO("Rules PML loaded for RX lcore %u", lcore_id);
    }

    if (g_nsa_root && g_nsa_root->nsa_session_ctx_pool) {
        pml_ctx->session_ctx_pool = g_nsa_root->nsa_session_ctx_pool;
    } else {
        NSA_LOG_ERROR("Global NSA session context pool is not available for lcore %u", lcore_id);
        free(pml_ctx);
        return -EFAULT;
    }

    uint64_t timer_hz = rte_get_timer_hz();
    pml_ctx->time_update_interval_tsc = timer_hz * 60;
    pml_ctx->last_time_update_tsc = 0;
    pml_ctx->cached_pml_datetime = 0;

    *thread_data = pml_ctx;

    NSA_LOG_INFO("NSA thread-specific PML initialization completed for RX lcore %u (classify: %s, rules: %s)",
                 lcore_id,
                 pml_ctx->classify_available ? "yes" : "no",
                 pml_ctx->rules_available ? "yes" : "no");

    return 0;
}

/**
 * @brief RX thread cleanup callback for NSA
 * 
 * Cleans up thread-specific PML instances when RX thread terminates.
 * 
 * @param lcore_id The lcore ID of the RX thread
 * @param thread_data Thread-specific data to cleanup
 * @return 0 on success, negative error code on failure
 */
static int nsa_rx_thread_cleanup(uint32_t lcore_id, void *thread_data) {
    nsa_rx_thread_pml_context_t *pml_ctx = (nsa_rx_thread_pml_context_t *) thread_data;

    if (!pml_ctx) {
        NSA_LOG_WARNING("No thread PML context to cleanup for lcore %u", lcore_id);
        return 0;
    }

    NSA_LOG_INFO("Cleaning up NSA thread-specific PML instances for RX lcore %u", lcore_id);

    /* Cleanup classification PML instance */
    if (pml_ctx->thread_classify_pml) {
        pml_exit(pml_ctx->thread_classify_pml);
        pml_ctx->thread_classify_pml = NULL;
    }

    /* Cleanup rules PML instance */
    if (pml_ctx->thread_rules_pml) {
        pml_exit(pml_ctx->thread_rules_pml);
        pml_ctx->thread_rules_pml = NULL;
    }

    /* Free ctx mempool */
    if (pml_ctx->session_ctx_pool) {
        rte_mempool_free(pml_ctx->session_ctx_pool);
        pml_ctx->session_ctx_pool = NULL;
    }

    free(pml_ctx);

    NSA_LOG_INFO("NSA thread-specific PML cleanup completed for RX lcore %u", lcore_id);
    return 0;
}

/**
 * @brief Get thread-specific PML context for current RX thread
 * 
 * Uses DPIF API to safely access thread-specific data without violating
 * module boundaries or accessing internal DPIF structures directly.
 * 
 * @return Pointer to thread-specific PML context, or NULL if not available
 */
static nsa_rx_thread_pml_context_t *nsa_get_current_thread_pml_context(void) {
    // Use DPIF API instead of directly accessing global variables
    void *thread_data = dpif_get_current_rx_thread_data();
    if (!thread_data) {
        NSA_LOG_ERROR("Failed to get thread-specific data for current RX thread");
        return NULL;
    }

    // Cast to our specific context type
    nsa_rx_thread_pml_context_t *pml_ctx = (nsa_rx_thread_pml_context_t *) thread_data;
#if 0
    // Validate the context
    if (!pml_ctx->thread_classify_pml || !pml_ctx->thread_rules_pml) {
        NSA_LOG_ERROR("Invalid PML context for current RX thread");
        return NULL;
    }
#endif
    return pml_ctx;
}

/**
 * @brief Handles periodic maintenance tasks for each RX thread.
 */
static void nsa_periodic_maintenance_cb(void *rx_thread_data) {
    nsa_rx_thread_pml_context_t *pml_thread_ctx = (nsa_rx_thread_pml_context_t *) rx_thread_data;
    if (pml_thread_ctx) {
        nsa_pml_check_for_updates(pml_thread_ctx);
    }
}

/**
 * @brief Register NSA session callbacks with DPIF
 * 
 * Registers all NSA session handling callbacks with the DPIF library
 * to enable integration with the data plane.
 */
void nsa_session_register_callbacks(void) {
    dpi_device_t nsa_device = {
        .dpi_session_create = nsa_session_create,
        .dpi_session_destroy = nsa_session_destroy,
        .dpi_session_analyze = nsa_session_analyze,
        .dpi_session_work = nsa_session_work,
        .dpi_session_update = nsa_session_update,
        .dpi_rx_thread_init = nsa_rx_thread_init,
        .dpi_rx_thread_cleanup = nsa_rx_thread_cleanup,
        .dpi_periodic_maintenance = nsa_periodic_maintenance_cb,
    };

    if (dpi_register_device(&nsa_device) != 0) {
        NSA_LOG_ERROR("Failed to register NSA session callbacks");
        return;
    }

    NSA_LOG_INFO("NSA session callbacks registered successfully");
}