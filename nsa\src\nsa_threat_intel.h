#ifndef _NSA_THREAT_INTEL_H_
#define _NSA_THREAT_INTEL_H_

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

// IOC (Indicator of Compromise) types
typedef enum {
    NSA_IOC_TYPE_IP = 1,
    NSA_IOC_TYPE_DOMAIN = 2,
    NSA_IOC_TYPE_URL = 3,
    NSA_IOC_TYPE_FILE_HASH = 4,
    NSA_IOC_TYPE_EMAIL = 5,
    NSA_IOC_TYPE_SSL_CERT = 6,
    NSA_IOC_TYPE_USER_AGENT = 7,
    NSA_IOC_TYPE_JA3_HASH = 8
} nsa_ioc_type_t;

// Threat severity levels
typedef enum {
    NSA_THREAT_SEVERITY_UNKNOWN = 0,
    NSA_THREAT_SEVERITY_LOW = 1,
    NSA_THREAT_SEVERITY_MEDIUM = 2,
    NSA_THREAT_SEVERITY_HIGH = 3,
    NSA_THREAT_SEVERITY_CRITICAL = 4
} nsa_threat_severity_t;

// Threat source types
typedef enum {
    NSA_THREAT_SOURCE_COMMERCIAL = 1,
    NSA_THREAT_SOURCE_OPEN_SOURCE = 2,
    NSA_THREAT_SOURCE_GOVERNMENT = 3,
    NSA_THREAT_SOURCE_INTERNAL = 4,
    NSA_THREAT_SOURCE_COMMUNITY = 5
} nsa_threat_source_t;

// IOC entry structure
typedef struct {
    uint64_t ioc_id;
    nsa_ioc_type_t type;
    char indicator_value[512];  // IP, domain, URL, hash, etc.
    nsa_threat_severity_t severity;
    nsa_threat_source_t source;
    
    // Threat information
    uint32_t threat_family_id;
    char threat_family_name[128];
    char threat_description[512];
    
    // Attribution
    char threat_actor[128];
    char campaign[128];
    
    // Metadata
    time_t first_seen;
    time_t last_seen;
    time_t created_time;
    time_t expiry_time;
    uint32_t confidence_score;  // 0-100
    
    // Context tags
    char tags[256];  // Comma-separated tags
    
    // Actions
    bool block_traffic;
    bool log_events;
    bool send_alert;
    bool quarantine;
    
    // Statistics
    uint64_t hit_count;
    time_t last_hit_time;
} nsa_ioc_entry_t;

// Threat feed metadata
typedef struct {
    uint32_t feed_id;
    char feed_name[128];
    char feed_url[512];
    char feed_provider[128];
    nsa_threat_source_t source_type;
    
    // Feed status
    bool enabled;
    time_t last_update;
    time_t next_update;
    uint32_t update_interval_hours;
    uint32_t total_indicators;
    uint32_t active_indicators;
    
    // Feed quality metrics
    uint32_t false_positive_rate;  // Per 10000
    uint32_t detection_rate;       // Per 10000
    uint8_t reliability_score;     // 1-10
} nsa_threat_feed_t;

// Real-time threat lookup result
typedef struct {
    bool is_malicious;
    nsa_ioc_entry_t matched_ioc;
    uint32_t confidence_score;
    char additional_info[256];
} nsa_threat_lookup_result_t;

// Threat intelligence API
int nsa_threat_intel_init(void);
void nsa_threat_intel_cleanup(void);

// IOC management
int nsa_threat_intel_add_ioc(const nsa_ioc_entry_t *ioc);
int nsa_threat_intel_remove_ioc(uint64_t ioc_id);
int nsa_threat_intel_update_ioc(uint64_t ioc_id, const nsa_ioc_entry_t *updated_ioc);
int nsa_threat_intel_get_ioc(uint64_t ioc_id, nsa_ioc_entry_t *ioc);

// Threat feed management
int nsa_threat_intel_add_feed(const nsa_threat_feed_t *feed);
int nsa_threat_intel_remove_feed(uint32_t feed_id);
int nsa_threat_intel_update_feed(uint32_t feed_id, const nsa_threat_feed_t *updated_feed);
int nsa_threat_intel_list_feeds(nsa_threat_feed_t *feeds, uint32_t max_feeds, uint32_t *count);

// Feed operations
int nsa_threat_intel_refresh_feed(uint32_t feed_id);
int nsa_threat_intel_refresh_all_feeds(void);
int nsa_threat_intel_import_feed_data(uint32_t feed_id, const char *data_file);

// Threat lookups
int nsa_threat_intel_lookup_ip(uint32_t ip_addr, nsa_threat_lookup_result_t *result);
int nsa_threat_intel_lookup_domain(const char *domain, nsa_threat_lookup_result_t *result);
int nsa_threat_intel_lookup_url(const char *url, nsa_threat_lookup_result_t *result);
int nsa_threat_intel_lookup_file_hash(const char *hash, nsa_threat_lookup_result_t *result);

// Bulk operations
int nsa_threat_intel_bulk_lookup_ips(uint32_t *ip_addrs, uint32_t count, 
                                   nsa_threat_lookup_result_t *results);
int nsa_threat_intel_bulk_lookup_domains(char domains[][256], uint32_t count,
                                       nsa_threat_lookup_result_t *results);

// Statistics and reporting
int nsa_threat_intel_get_stats(uint64_t *total_iocs, uint64_t *active_iocs, 
                              uint64_t *total_hits, uint32_t *feeds_count);
int nsa_threat_intel_get_feed_stats(uint32_t feed_id, uint32_t *indicators_count,
                                   uint64_t *hit_count, time_t *last_update);
int nsa_threat_intel_get_top_threats(nsa_ioc_entry_t *top_threats, uint32_t max_count);

// Cache management
int nsa_threat_intel_flush_cache(void);
int nsa_threat_intel_get_cache_stats(uint32_t *cache_size, uint32_t *hit_rate);

#ifdef __cplusplus
}
#endif

#endif // _NSA_THREAT_INTEL_H_ 