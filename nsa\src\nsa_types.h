/**
 * @file nsa_types.h
 * @brief NSA Core Type Definitions
 * 
 * This file contains fundamental type definitions, constants, and
 * enumerations used throughout the NSA (Network Security Application).
 * 
 * <AUTHOR> Li
 * @date 2025
 * @copyright Calix Inc.
 */

#ifndef _NSA_TYPES_H_
#define _NSA_TYPES_H_

#include <stdint.h>
#include <stdbool.h>
#include <time.h>
#include <sys/types.h>
#include <netinet/in.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * Basic Type Definitions
 * ======================================================================== */

/** @brief NSA session identifier type */
typedef uint32_t    nsa_session_id_t;

/** @brief NSA user identifier type */
typedef uint32_t    nsa_user_id_t;

/** @brief NSA policy rule identifier type */
typedef uint32_t    nsa_rule_id_t;

/** @brief NSA application identifier type */
typedef uint16_t    nsa_app_id_t;

/** @brief NSA threat identifier type */
typedef uint32_t    nsa_threat_id_t;

/** @brief NSA zone identifier type */
typedef uint8_t     nsa_zone_id_t;

/* ========================================================================
 * Network Address Types
 * ======================================================================== */

/**
 * @brief NSA IP address union (IPv4/IPv6)
 */
typedef union nsa_ip_addr {
    struct in_addr      ipv4;       /**< IPv4 address */
    struct in6_addr     ipv6;       /**< IPv6 address */
    uint8_t             raw[16];    /**< Raw bytes */
} nsa_ip_addr_t;

/**
 * @brief NSA network address family
 */
typedef enum {
    NSA_AF_UNSPEC   = 0,    /**< Unspecified */
    NSA_AF_INET     = 2,    /**< IPv4 */
    NSA_AF_INET6    = 10    /**< IPv6 */
} nsa_addr_family_t;

/**
 * @brief NSA network endpoint
 */
typedef struct nsa_endpoint {
    nsa_addr_family_t   family;     /**< Address family */
    nsa_ip_addr_t       addr;       /**< IP address */
    uint16_t            port;       /**< Port number */
    uint8_t             reserved[2]; /**< Alignment padding */
} nsa_endpoint_t;

/* ========================================================================
 * Protocol and Traffic Types
 * ======================================================================== */

/**
 * @brief NSA protocol types
 */
typedef enum {
    NSA_PROTO_UNKNOWN   = 0,    /**< Unknown protocol */
    NSA_PROTO_ICMP      = 1,    /**< ICMP */
    NSA_PROTO_TCP       = 6,    /**< TCP */
    NSA_PROTO_UDP       = 17,   /**< UDP */
    NSA_PROTO_ICMPV6    = 58,   /**< ICMPv6 */
    NSA_PROTO_SCTP      = 132,  /**< SCTP */
    NSA_PROTO_ESP       = 50,   /**< IPSec ESP */
    NSA_PROTO_AH        = 51,   /**< IPSec AH */
    NSA_PROTO_GRE       = 47    /**< GRE */
} nsa_protocol_t;

/**
 * @brief NSA traffic direction
 */
typedef enum {
    NSA_DIR_UNKNOWN     = 0,    /**< Unknown direction */
    NSA_DIR_INGRESS     = 1,    /**< Incoming traffic */
    NSA_DIR_EGRESS      = 2,    /**< Outgoing traffic */
    NSA_DIR_LATERAL     = 3     /**< Lateral/internal traffic */
} nsa_traffic_direction_t;

/* ========================================================================
 * Status and Result Types
 * ======================================================================== */

/**
 * @brief NSA operation status codes
 */
typedef enum {
    NSA_STATUS_SUCCESS          = 0,    /**< Operation successful */
    NSA_STATUS_ERROR            = -1,   /**< General error */
    NSA_STATUS_INVALID_PARAM    = -2,   /**< Invalid parameter */
    NSA_STATUS_NOT_FOUND        = -3,   /**< Resource not found */
    NSA_STATUS_NO_MEMORY        = -4,   /**< Out of memory */
    NSA_STATUS_TIMEOUT          = -5,   /**< Operation timeout */
    NSA_STATUS_BUSY             = -6,   /**< Resource busy */
    NSA_STATUS_NOT_SUPPORTED    = -7,   /**< Operation not supported */
    NSA_STATUS_PERMISSION       = -8,   /**< Permission denied */
    NSA_STATUS_NETWORK_ERROR    = -9,   /**< Network error */
    NSA_STATUS_DB_ERROR         = -10,  /**< Database error */
    NSA_STATUS_PARSE_ERROR      = -11,  /**< Parsing error */
    NSA_STATUS_CONFIG_ERROR     = -12   /**< Configuration error */
} nsa_status_t;

/**
 * @brief NSA verdict types (aligned with DPIF)
 */
typedef enum {
    NSA_VERDICT_PENDING     = 0,    /**< Analysis pending */
    NSA_VERDICT_PERMIT      = 1,    /**< Allow traffic */
    NSA_VERDICT_DROP        = 2,    /**< Drop traffic */
    NSA_VERDICT_ERROR       = 3     /**< Error in processing */
} nsa_verdict_t;

/* ========================================================================
 * Time and Duration Types
 * ======================================================================== */

/**
 * @brief NSA timestamp structure
 */
typedef struct nsa_timestamp {
    uint64_t    seconds;        /**< Seconds since epoch */
    uint32_t    nanoseconds;    /**< Nanoseconds */
    uint32_t    reserved;       /**< Alignment padding */
} nsa_timestamp_t;

/**
 * @brief NSA time range structure
 */
typedef struct nsa_time_range {
    nsa_timestamp_t start_time; /**< Start timestamp */
    nsa_timestamp_t end_time;   /**< End timestamp */
} nsa_time_range_t;

/* ========================================================================
 * Statistics and Counters
 * ======================================================================== */

/**
 * @brief NSA traffic statistics
 */
typedef struct nsa_traffic_stats {
    uint64_t    packets_rx;     /**< Packets received */
    uint64_t    packets_tx;     /**< Packets transmitted */
    uint64_t    bytes_rx;       /**< Bytes received */
    uint64_t    bytes_tx;       /**< Bytes transmitted */
    uint64_t    drops;          /**< Dropped packets */
    uint64_t    errors;         /**< Error packets */
} nsa_traffic_stats_t;

/**
 * @brief NSA performance metrics
 */
typedef struct nsa_perf_metrics {
    uint32_t    pps_current;    /**< Current packets per second */
    uint32_t    pps_peak;       /**< Peak packets per second */
    uint32_t    bps_current;    /**< Current bits per second */
    uint32_t    bps_peak;       /**< Peak bits per second */
    uint32_t    latency_avg;    /**< Average latency (microseconds) */
    uint32_t    latency_max;    /**< Maximum latency (microseconds) */
    uint32_t    cpu_usage;      /**< CPU usage percentage */
    uint32_t    memory_usage;   /**< Memory usage percentage */
} nsa_perf_metrics_t;

/* ========================================================================
 * Security and Risk Types
 * ======================================================================== */

/**
 * @brief NSA risk levels
 */
typedef enum {
    NSA_RISK_UNKNOWN    = 0,    /**< Unknown risk */
    NSA_RISK_LOW        = 1,    /**< Low risk */
    NSA_RISK_MEDIUM     = 2,    /**< Medium risk */
    NSA_RISK_HIGH       = 3,    /**< High risk */
    NSA_RISK_CRITICAL   = 4     /**< Critical risk */
} nsa_risk_level_t;

/**
 * @brief NSA security levels
 */
typedef enum {
    NSA_SECURITY_UNTRUSTED      = 0,    /**< Untrusted zone */
    NSA_SECURITY_LIMITED        = 1,    /**< Limited trust */
    NSA_SECURITY_TRUSTED        = 2,    /**< Trusted zone */
    NSA_SECURITY_HIGHLY_TRUSTED = 3     /**< Highly trusted zone */
} nsa_security_level_t;

/* ========================================================================
 * Configuration and Control Types
 * ======================================================================== */

/**
 * @brief NSA configuration flags
 */
typedef enum {
    NSA_CONFIG_DPI_ENABLED      = 0x00000001,  /**< DPI analysis enabled */
    NSA_CONFIG_POLICY_ENABLED   = 0x00000002,  /**< Policy enforcement enabled */
    NSA_CONFIG_LOGGING_ENABLED  = 0x00000004,  /**< Logging enabled */
    NSA_CONFIG_STATS_ENABLED    = 0x00000008,  /**< Statistics collection enabled */
    NSA_CONFIG_DEBUG_ENABLED    = 0x00000010,  /**< Debug mode enabled */
    NSA_CONFIG_HIGH_PERF_MODE   = 0x00000020,  /**< High performance mode */
    NSA_CONFIG_STRICT_MODE      = 0x00000040,  /**< Strict security mode */
    NSA_CONFIG_BYPASS_MODE      = 0x00000080   /**< Bypass mode (emergency) */
} nsa_config_flags_t;

/* ========================================================================
 * Extension and Future Enhancement Types
 * ======================================================================== */

/**
 * @brief NSA module types (for future extensions)
 */
typedef enum {
    NSA_MODULE_CORE         = 0,    /**< Core NSA module */
    NSA_MODULE_DPI          = 1,    /**< DPI analysis module */
    NSA_MODULE_POLICY       = 2,    /**< Policy engine module */
    NSA_MODULE_THREAT_INTEL = 3,    /**< Threat intelligence module */
    NSA_MODULE_LOGGING      = 4,    /**< Enhanced logging module */
    NSA_MODULE_ANALYTICS    = 5,    /**< Analytics module */
    NSA_MODULE_USER_AUTH    = 6,    /**< User authentication module */
    NSA_MODULE_CUSTOM       = 99    /**< Custom/third-party module */
} nsa_module_type_t;

/* ========================================================================
 * Utility Macros and Constants
 * ======================================================================== */

/** @brief Maximum string lengths */
#define NSA_MAX_NAME_LEN        64      /**< Maximum name length */
#define NSA_MAX_DESC_LEN        256     /**< Maximum description length */
#define NSA_MAX_PATH_LEN        512     /**< Maximum file path length */
#define NSA_MAX_URL_LEN         1024    /**< Maximum URL length */
#define NSA_MAX_HOSTNAME_LEN    256     /**< Maximum hostname length */
#define NSA_MAX_USERNAME_LEN    64      /**< Maximum username length */

/** @brief Network constants */
#define NSA_MAX_PACKET_SIZE     9000    /**< Maximum packet size (jumbo frame) */
#define NSA_MIN_PACKET_SIZE     64      /**< Minimum packet size */
#define NSA_MAX_SESSIONS        1000000 /**< Maximum concurrent sessions */
#define NSA_MAX_RULES           10000   /**< Maximum policy rules */

/** @brief Time constants */
#define NSA_SESSION_TIMEOUT_DEFAULT     300     /**< Default session timeout (seconds) */
#define NSA_SESSION_TIMEOUT_TCP         3600    /**< TCP session timeout (seconds) */
#define NSA_SESSION_TIMEOUT_UDP         60      /**< UDP session timeout (seconds) */
#define NSA_SESSION_TIMEOUT_ICMP        30      /**< ICMP session timeout (seconds) */

/** @brief Utility macros */
#define NSA_UNUSED(x)           ((void)(x))                    /**< Mark variable as unused */
#define NSA_ARRAY_SIZE(arr)     (sizeof(arr) / sizeof((arr)[0])) /**< Get array size */
#define NSA_ALIGN(x, a)         (((x) + (a) - 1) & ~((a) - 1)) /**< Align value */
#define NSA_MIN(a, b)           ((a) < (b) ? (a) : (b))       /**< Get minimum value */
#define NSA_MAX(a, b)           ((a) > (b) ? (a) : (b))       /**< Get maximum value */

/** @brief Boolean macros for clarity */
#define NSA_TRUE                true
#define NSA_FALSE               false

/** @brief Success/failure check macros */
#define NSA_SUCCESS(status)     ((status) == NSA_STATUS_SUCCESS)
#define NSA_FAILED(status)      ((status) != NSA_STATUS_SUCCESS)

#ifdef __cplusplus
}
#endif

#endif /* _NSA_TYPES_H_ */ 