#!/bin/bash

# NSA Performance Test Suite
# Comprehensive performance testing and validation script
# Author: Performance Optimization Team
# Date: 2025

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
RESULTS_DIR="$PROJECT_ROOT/performance_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
TEST_RESULTS_FILE="$RESULTS_DIR/performance_test_$TIMESTAMP.log"

# Test configuration
DEFAULT_DURATION=60
DEFAULT_TARGET_PPS=100000
DEFAULT_CONCURRENT_SESSIONS=10000

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$TEST_RESULTS_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$TEST_RESULTS_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$TEST_RESULTS_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$TEST_RESULTS_FILE"
}

# Help function
show_help() {
    cat << EOF
NSA Performance Test Suite

Usage: $0 [OPTIONS]

OPTIONS:
    -h, --help              Show this help message
    -d, --duration SECONDS  Test duration in seconds (default: $DEFAULT_DURATION)
    -p, --pps TARGET        Target packets per second (default: $DEFAULT_TARGET_PPS)
    -s, --sessions COUNT    Concurrent sessions (default: $DEFAULT_CONCURRENT_SESSIONS)
    -t, --test TYPE         Test type: all, throughput, latency, stress, regression
    -b, --baseline FILE     Baseline results file for regression testing
    -v, --verbose           Verbose output
    --profile               Enable profiling during tests
    --no-cleanup            Don't cleanup after tests

TEST TYPES:
    all         - Run all performance tests (default)
    throughput  - Throughput benchmark
    latency     - Latency benchmark  
    stress      - Stress test with high load
    regression  - Performance regression test
    memory      - Memory stress test
    cpu         - CPU stress test

EXAMPLES:
    $0                                          # Run all tests with defaults
    $0 -t throughput -d 120 -p 200000          # Throughput test for 2 minutes
    $0 -t stress -d 300 -p 500000              # Stress test for 5 minutes
    $0 -t regression -b baseline_results.json  # Regression test against baseline

EOF
}

# Initialize test environment
init_test_environment() {
    log_info "Initializing test environment..."
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    # Check if NSA daemon is running
    if ! pgrep -f "nsad" > /dev/null; then
        log_error "NSA daemon is not running. Please start nsad first."
        exit 1
    fi
    
    # Check system resources
    check_system_resources
    
    # Initialize performance analyzer
    log_info "Initializing performance monitoring..."
    
    log_success "Test environment initialized"
}

# Check system resources
check_system_resources() {
    log_info "Checking system resources..."
    
    # Check available memory
    local available_mem=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_mem" -lt 1024 ]; then
        log_warning "Low available memory: ${available_mem}MB. Tests may be affected."
    fi
    
    # Check CPU cores
    local cpu_cores=$(nproc)
    log_info "Available CPU cores: $cpu_cores"
    
    # Check huge pages
    local huge_pages=$(cat /proc/meminfo | grep HugePages_Total | awk '{print $2}')
    if [ "$huge_pages" -eq 0 ]; then
        log_warning "No huge pages configured. Performance may be suboptimal."
    fi
    
    # Check DPDK compatibility
    if [ ! -d "/sys/module/vfio" ] && [ ! -d "/sys/module/uio_pci_generic" ]; then
        log_warning "No DPDK-compatible drivers loaded."
    fi
}

# Run throughput benchmark
run_throughput_test() {
    local duration=$1
    local target_pps=$2
    
    log_info "Running throughput benchmark..."
    log_info "Duration: ${duration}s, Target PPS: $target_pps"
    
    # Start performance monitoring
    start_performance_monitoring
    
    # Run the actual test
    local test_cmd="$BUILD_DIR/nsa_benchmark --type throughput --duration $duration --target-pps $target_pps"
    
    if [ "$ENABLE_PROFILING" = "true" ]; then
        test_cmd="$test_cmd --profile"
    fi
    
    log_info "Executing: $test_cmd"
    
    if $test_cmd >> "$TEST_RESULTS_FILE" 2>&1; then
        log_success "Throughput test completed successfully"
        
        # Collect results
        collect_test_results "throughput" "$duration" "$target_pps"
    else
        log_error "Throughput test failed"
        return 1
    fi
    
    # Stop performance monitoring
    stop_performance_monitoring
}

# Run latency benchmark
run_latency_test() {
    local duration=$1
    local target_pps=$2
    
    log_info "Running latency benchmark..."
    log_info "Duration: ${duration}s, Target PPS: $target_pps"
    
    start_performance_monitoring
    
    local test_cmd="$BUILD_DIR/nsa_benchmark --type latency --duration $duration --target-pps $target_pps"
    
    if [ "$ENABLE_PROFILING" = "true" ]; then
        test_cmd="$test_cmd --profile"
    fi
    
    log_info "Executing: $test_cmd"
    
    if $test_cmd >> "$TEST_RESULTS_FILE" 2>&1; then
        log_success "Latency test completed successfully"
        collect_test_results "latency" "$duration" "$target_pps"
    else
        log_error "Latency test failed"
        return 1
    fi
    
    stop_performance_monitoring
}

# Run stress test
run_stress_test() {
    local duration=$1
    local target_pps=$2
    
    log_info "Running stress test..."
    log_info "Duration: ${duration}s, Target PPS: $target_pps"
    
    start_performance_monitoring
    
    # Stress test with higher load
    local stress_pps=$((target_pps * 2))
    local test_cmd="$BUILD_DIR/nsa_benchmark --type stress --duration $duration --target-pps $stress_pps"
    
    if [ "$ENABLE_PROFILING" = "true" ]; then
        test_cmd="$test_cmd --profile"
    fi
    
    log_info "Executing: $test_cmd"
    
    if $test_cmd >> "$TEST_RESULTS_FILE" 2>&1; then
        log_success "Stress test completed successfully"
        collect_test_results "stress" "$duration" "$stress_pps"
    else
        log_error "Stress test failed"
        return 1
    fi
    
    stop_performance_monitoring
}

# Run regression test
run_regression_test() {
    local baseline_file=$1
    local duration=$2
    local target_pps=$3
    
    if [ ! -f "$baseline_file" ]; then
        log_error "Baseline file not found: $baseline_file"
        return 1
    fi
    
    log_info "Running regression test against baseline: $baseline_file"
    
    start_performance_monitoring
    
    local test_cmd="$BUILD_DIR/nsa_benchmark --type regression --baseline $baseline_file --duration $duration --target-pps $target_pps"
    
    log_info "Executing: $test_cmd"
    
    if $test_cmd >> "$TEST_RESULTS_FILE" 2>&1; then
        log_success "Regression test completed successfully"
        collect_test_results "regression" "$duration" "$target_pps"
    else
        log_error "Regression test failed"
        return 1
    fi
    
    stop_performance_monitoring
}

# Start performance monitoring
start_performance_monitoring() {
    log_info "Starting performance monitoring..."
    
    # Start system monitoring
    iostat -x 1 > "$RESULTS_DIR/iostat_$TIMESTAMP.log" &
    IOSTAT_PID=$!
    
    vmstat 1 > "$RESULTS_DIR/vmstat_$TIMESTAMP.log" &
    VMSTAT_PID=$!
    
    # Start NSA enhanced monitoring
    if [ -x "$BUILD_DIR/nsa_enhanced_monitor" ]; then
        "$BUILD_DIR/nsa_enhanced_monitor" --daemon --output "$RESULTS_DIR/nsa_monitor_$TIMESTAMP.log" &
        NSA_MONITOR_PID=$!
    fi
}

# Stop performance monitoring
stop_performance_monitoring() {
    log_info "Stopping performance monitoring..."
    
    # Stop system monitoring
    if [ ! -z "$IOSTAT_PID" ]; then
        kill $IOSTAT_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$VMSTAT_PID" ]; then
        kill $VMSTAT_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$NSA_MONITOR_PID" ]; then
        kill $NSA_MONITOR_PID 2>/dev/null || true
    fi
}

# Collect test results
collect_test_results() {
    local test_type=$1
    local duration=$2
    local target_pps=$3
    
    log_info "Collecting test results for $test_type test..."
    
    # Create test-specific results directory
    local test_results_dir="$RESULTS_DIR/${test_type}_$TIMESTAMP"
    mkdir -p "$test_results_dir"
    
    # Copy monitoring data
    if [ -f "$RESULTS_DIR/iostat_$TIMESTAMP.log" ]; then
        mv "$RESULTS_DIR/iostat_$TIMESTAMP.log" "$test_results_dir/"
    fi
    
    if [ -f "$RESULTS_DIR/vmstat_$TIMESTAMP.log" ]; then
        mv "$RESULTS_DIR/vmstat_$TIMESTAMP.log" "$test_results_dir/"
    fi
    
    if [ -f "$RESULTS_DIR/nsa_monitor_$TIMESTAMP.log" ]; then
        mv "$RESULTS_DIR/nsa_monitor_$TIMESTAMP.log" "$test_results_dir/"
    fi
    
    # Generate summary report
    generate_test_summary "$test_type" "$test_results_dir" "$duration" "$target_pps"
}

# Generate test summary
generate_test_summary() {
    local test_type=$1
    local results_dir=$2
    local duration=$3
    local target_pps=$4
    
    local summary_file="$results_dir/summary.txt"
    
    cat > "$summary_file" << EOF
NSA Performance Test Summary
============================

Test Type: $test_type
Timestamp: $TIMESTAMP
Duration: ${duration}s
Target PPS: $target_pps

System Information:
- CPU Cores: $(nproc)
- Memory: $(free -h | awk 'NR==2{print $2}')
- Kernel: $(uname -r)
- DPDK Version: $(cat $PROJECT_ROOT/VERSION 2>/dev/null || echo "Unknown")

Test Results:
$(tail -20 "$TEST_RESULTS_FILE")

EOF
    
    log_info "Test summary generated: $summary_file"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    
    stop_performance_monitoring
    
    if [ "$NO_CLEANUP" != "true" ]; then
        # Clean up temporary files
        rm -f /tmp/nsa_test_*
    fi
    
    log_info "Cleanup completed"
}

# Main function
main() {
    local test_type="all"
    local duration=$DEFAULT_DURATION
    local target_pps=$DEFAULT_TARGET_PPS
    local concurrent_sessions=$DEFAULT_CONCURRENT_SESSIONS
    local baseline_file=""
    local verbose=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--duration)
                duration="$2"
                shift 2
                ;;
            -p|--pps)
                target_pps="$2"
                shift 2
                ;;
            -s|--sessions)
                concurrent_sessions="$2"
                shift 2
                ;;
            -t|--test)
                test_type="$2"
                shift 2
                ;;
            -b|--baseline)
                baseline_file="$2"
                shift 2
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            --profile)
                ENABLE_PROFILING=true
                shift
                ;;
            --no-cleanup)
                NO_CLEANUP=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Set up trap for cleanup
    trap cleanup EXIT
    
    # Initialize test environment
    init_test_environment
    
    log_info "Starting NSA Performance Test Suite"
    log_info "Test type: $test_type"
    log_info "Duration: ${duration}s"
    log_info "Target PPS: $target_pps"
    log_info "Results will be saved to: $RESULTS_DIR"
    
    # Run tests based on type
    case $test_type in
        all)
            log_info "Running all performance tests..."
            run_throughput_test "$duration" "$target_pps"
            run_latency_test "$duration" "$target_pps"
            run_stress_test "$duration" "$target_pps"
            ;;
        throughput)
            run_throughput_test "$duration" "$target_pps"
            ;;
        latency)
            run_latency_test "$duration" "$target_pps"
            ;;
        stress)
            run_stress_test "$duration" "$target_pps"
            ;;
        regression)
            if [ -z "$baseline_file" ]; then
                log_error "Baseline file required for regression test. Use -b option."
                exit 1
            fi
            run_regression_test "$baseline_file" "$duration" "$target_pps"
            ;;
        *)
            log_error "Unknown test type: $test_type"
            show_help
            exit 1
            ;;
    esac
    
    log_success "Performance test suite completed successfully"
    log_info "Results saved to: $RESULTS_DIR"
}

# Run main function
main "$@"
